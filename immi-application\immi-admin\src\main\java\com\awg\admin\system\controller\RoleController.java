package com.awg.admin.system.controller;

import com.awg.comm.dto.ProductBannerDto;
import com.awg.comm.entity.Product;
import com.awg.comm.externalService.IProductExternalService;
import com.awg.comm.mapper.ProductCrossMapper;
import com.awg.comm.mapper.ProductMapper;
import com.awg.common.base.eo.ImageInfoEo;
import com.awg.common.utils.FileBaseUtil;
import com.awg.crm.entity.Leads;
import com.awg.crm.entity.LeadsLog;
import com.awg.crm.externalService.ILeadsCustomSortExternalService;
import com.awg.crm.externalService.ILeadsExternalService;
import com.awg.crm.mapper.LeadsLogMapper;
import com.awg.crm.mapper.LeadsMapper;
import com.awg.qs.entity.FillRecord;
import com.awg.qs.mapper.FillRecordMapper;
import com.awg.system.dto.MenuInfoDto;
import com.awg.system.dto.RoleDto;
import com.awg.system.dto.RoleInfoDTO;
import com.awg.system.entity.UserInfo;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.system.mapper.UserInfoMapper;
import com.awg.system.service.IRoleService;
import com.awg.system.vo.AddRoleVo;
import com.awg.system.vo.QueryRoleVo;
import com.awg.system.vo.UpdateRoleVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.RoleTypeEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import com.awg.comm.entity.ProductData;
import com.awg.comm.mapper.ProductDataMapper;
import com.awg.common.enums.TrueFalseEnum;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 25 )
@Api( tags = {"角色相关接口"} )
@RestController
@RequestMapping( "/system/role" )
public class RoleController extends BaseController {

    // 引入角色服务
    @Resource
    private IRoleService roleService;

    @Resource
    private IOrgExternalService orgExternalService;

    @Resource
    private ILeadsExternalService leadsExternalService;

    @Resource
    private LeadsMapper leadsMapper;

    @Resource
    private LeadsLogMapper leadsLogMapper;

    @Resource
    private FillRecordMapper fillRecordMapper;

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private ILeadsCustomSortExternalService leadsCustomSortExternalService;

    @Resource
    private ProductDataMapper productDataMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private IProductExternalService productExternalService;

    @Resource
    private ProductCrossMapper productCrossMapper;

    @ApiOperation( "获取角色列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = RoleDto.class )
    })
    public DataResult list(@RequestBody QueryRoleVo vo) {
        ValidationUtils.validate(vo);

        BasePageResult<RoleDto> result = roleService.getRoleList(vo, getCurrentOrgId());
        return renderSuccess(result);
    }

    @ApiOperation( "获取全部角色列表" )
    @PostMapping( "/listAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = RoleDto.class )
    })
    public DataResult listAll() {
        List<RoleDto> result = roleService.getRoleListAll(getCurrentOrgId(), getCurrentOrgType(), null);
        return renderSuccess(result);
    }

    @ApiOperation( "获取所有菜单列表" )
    @PostMapping( "/getMenuAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MenuInfoDto.class )
    })
    public DataResult getMenuAll() {
        List<MenuInfoDto> result = roleService.getMenuAll(getCurrentOrgId(), RoleTypeEnum.ADMIN.getCode());        // 角色类型先固定为管理员角色
        return renderSuccess(result);
    }

    @ApiOperation( "获取角色信息" )
    @PostMapping( "/info/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = RoleInfoDTO.class )
    })
    public DataResult info(@PathVariable( value = "id" ) Integer id) {

        AssertUtils.isTrue(id <= 0, "角色id不合法");

        RoleInfoDTO result = roleService.getRoleInfo(id, getCurrentOrgId());
        return renderSuccess(result);
    }

    @ApiOperation( "添加角色" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult add(@RequestBody AddRoleVo vo) {
        ValidationUtils.validate(vo);

        Integer roleId = roleService.addRole(vo, getCurrentOrgId());
        return renderSuccess(roleId);
    }

    @ApiOperation( "更新角色" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult update(@RequestBody UpdateRoleVo vo) {
        ValidationUtils.validate(vo);

        Integer roleId = roleService.updateRole(vo, getCurrentOrgId());
        return renderSuccess(roleId);
    }

    @Authority( AuthorityEnum.NOCHECK )
    @ApiOperation( "重载所有角色缓存" )
    @PostMapping( "/reloadAllRoleCache/{password}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult reloadAllRoleCache(@PathVariable( value = "password" ) String password) {
        AssertUtils.isTrue(password == null || password.trim().length() == 0, "密码不能为空");
        AssertUtils.isFalse(password.equals("oioidiadoia8ehdiaidaj8"), "密码不正确");

        roleService.setAllRoleRedis();
        return renderSuccess();
    }

    @Authority( AuthorityEnum.NOCHECK )
    @ApiOperation( "重载所有角色缓存" )
    @PostMapping( "/reloadAllRoleCache26/{password}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult reloadAllRoleCache26(@PathVariable( value = "password" ) String password) {
        AssertUtils.isTrue(password == null || password.trim().length() == 0, "密码不能为空");
        AssertUtils.isFalse(password.equals("oioidiadoia8ehdiaidaj8"), "密码不正确");

        // 查询所有未被删除的Product记录
        List<Product> productList = productMapper.selectList(Wrappers.<Product>lambdaQuery()
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        for (Product product : productList) {
            Long productNo = product.getProductNo();
            Integer productVid = product.getProductVid();

            // 查找当前生效的ProductData
            ProductData productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                    .eq(ProductData::getProductNo, productNo)
                    .eq(ProductData::getProductVid, productVid)
                    .eq(ProductData::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );

            if (productData != null) {
                String cover = productData.getCover();
                if (StringUtils.isBlank(cover)) {
                    // 获取banner列表
                    List<ProductBannerDto> productBannerList = productCrossMapper.getProductBannerDtoList(productNo.toString(), productVid);
                    if (productBannerList != null && !productBannerList.isEmpty()) {
                        ProductBannerDto firstBanner = productBannerList.get(0);
                        if (firstBanner.getType() == 0) { // 图片类型
                            cover = firstBanner.getPath();
                        } else if (firstBanner.getType() == 1) { // 视频类型
                            cover = firstBanner.getCoverUrl();
                        }
                    }
                }

                // 处理封面URL
                String finalCoverUrl = StringUtils.isNotBlank(cover) ? cover : "";

                // 当封面URL不包含".videocc.net/"时，获取图片宽高
                if (StringUtils.isNotBlank(finalCoverUrl) && !finalCoverUrl.contains(".videocc.net/")) {
                    ImageInfoEo imageInfo = FileBaseUtil.getCosImageDimension(finalCoverUrl);
                    if (imageInfo != null) {
                        productDataMapper.update(null, Wrappers.<ProductData>lambdaUpdate()
                            .set(ProductData::getCoverWidth, imageInfo.getWidth())
                            .set(ProductData::getCoverHeight, imageInfo.getHeight())
                            .eq(ProductData::getId, productData.getId())
                        );
                    }
                }
            }
        }
        return renderSuccess();
    }

    @ApiOperation( "删除角色" )
    @PostMapping( "/del/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult del(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "角色id不合法");

        roleService.delRole(id, getCurrentOrgId());
        return renderSuccess();
    }
}