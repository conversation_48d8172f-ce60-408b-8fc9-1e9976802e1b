package com.awg.common.security;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/***
 * @description: 接口鉴权注解
 * @author: 夜晓
 * @date: 2021/03/21
 **/
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Authority {

    /**
     * 权限检查注解
     *
     * @return default CHECK
     */
    AuthorityEnum value() default AuthorityEnum.CHECK;
}
