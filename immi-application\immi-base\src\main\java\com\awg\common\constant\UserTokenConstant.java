package com.awg.common.constant;

/**
 * <p>
 * <b>UserAdminTokenConstant</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2020/12/10 14:49
 */
public class UserTokenConstant {

    /**
     * admin在线用户 key前缀
     */
    public static final String ADMIN_ONLINE_USER_PREFIX = "immiUser:admin:online:";

    /**
     * admin角色 jey前缀
     */
    public static final String ADMIN_ONLINE_ROLE_PREFIX = "immiRole:admin:online:";

    /**
     * app在线用户 key前缀
     */
    public static final String APP_ONLINE_USER_PREFIX = "immiUser:app:online:";

    /**
     * 当前登录人用户id key命名
     */
    public static final String CURRENT_ADMIN_USERID = "userId";

    /**
     * 调试模块式redis key值
     */
    public static final String DEBUG_KYE = "app:debug:flag";

    /**
     * 入参调试key
     */
    public static final String DEBUG_PARAMETER_KEY = "app:reservation:flag";

    /**
     * admin用户 邀请链接缓存key前缀
     */
    public static final String ADMIN_INVITE_URL_PREFIX = "immiInvite:admin:url:";

}