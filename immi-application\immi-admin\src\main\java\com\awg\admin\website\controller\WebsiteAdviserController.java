package com.awg.admin.website.controller;

import com.awg.comm.vo.ProductSortVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.website.dto.AdviserDto;
import com.awg.website.dto.NewsDto;
import com.awg.website.dto.OfficeDto;
import com.awg.website.dto.ProjectDto;
import com.awg.website.service.IWebsiteAdviserService;
import com.awg.website.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@ApiSupport( order = 60 )
@Api( tags = {"官网管理-团队相关接口"} )
@RestController
@RequestMapping( "/website/team" )
public class WebsiteAdviserController extends BaseController {

    @Resource
    private IWebsiteAdviserService websiteAdviserService;

    @ApiOperationSupport(order = 6)
    @ApiOperation( "获取办公室列表" )
    @PostMapping( "/officeListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OfficeDto.class )
    })
    public DataResult officeListAll() {
        return renderSuccess(websiteAdviserService.getOfficeListAll(getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "保存办公室列表" )
    @PostMapping( "/saveOfficeList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult saveOfficeList(@RequestBody SaveOfficeListVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");

        ValidationUtils.validate(vo);

        // 迭代校验列表
        vo.getOfficeList().forEach( item -> {
            ValidationUtils.validate(item);
        });

        websiteAdviserService.saveOfficeList(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 50)
    @ApiOperation( "获取顾问列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AdviserDto.class )
    })
    public DataResult getAdviserList(@RequestBody QueryAdviserVo vo) {
        ValidationUtils.validate(vo);
        vo.setOrgId(getUserLoginInfoEo().getOrgId());
        BasePageResult<AdviserDto> result = websiteAdviserService.queryAdviserList(vo,getUserLoginInfoEo());
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 60)
    @ApiOperation( "添加顾问" )
    @PostMapping( "/addAdviser" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult addAdviser(@RequestBody AdviserVo vo) {
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");
        ValidationUtils.validate(vo);
        return renderSuccess(websiteAdviserService.addAdviser(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 70)
    @ApiOperation( "编辑顾问" )
    @PostMapping( "/updateAdviser" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateAdviser(@RequestBody AdviserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteAdviserService.updateAdviser(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 80)
    @ApiOperation( "删除顾问" )
    @PostMapping( "/delete/{adviserNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult deleteAdviser(@PathVariable( value = "adviserNo" ) String adviserNo) {
        AssertUtils.isTrue( Long.parseLong(adviserNo) <= 0, "No不合法");
        websiteAdviserService.deleteAdviser(adviserNo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 90)
    @ApiOperation( "切换首页显示" )
    @PostMapping( "/switchDisplayFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchAdviserFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteAdviserService.switchDisplayFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 100)
    @ApiOperation( "顾问排序" )
    @PostMapping( "/sortAdviser" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortAdviser(@RequestBody AdviserSortVo vo) {
        ValidationUtils.validate(vo);
        websiteAdviserService.sortAdviser(vo, getUserLoginInfoEo());
        return renderSuccess();
    }



}
