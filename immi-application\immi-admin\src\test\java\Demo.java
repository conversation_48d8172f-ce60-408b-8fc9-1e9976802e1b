import com.awg.ImmiAdminMain;
import com.awg.utils.date.DateUtils;
import org.junit.jupiter.api.Test;

import org.springframework.boot.test.context.SpringBootTest;

import java.util.Calendar;
import java.util.Date;

/**
 * <p>
 * <b>Demo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/12/2
 */

@SpringBootTest(classes = {ImmiAdminMain.class})
public class Demo {

    @Test
    public void fun() throws Exception {
        Date date = DateUtils.computingTime(new Date(), Calendar.HOUR,  -2);
        System.out.println(DateUtils.formatDate(date, DateUtils.DEFAULT_DATE_TIME_FORMAT_PATTERN));
    }
}
