package com.awg.common.base.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>LabelResult</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/10/11 16:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("通用标签信息")
public class LabelResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "备注")
    private String remark;
}
