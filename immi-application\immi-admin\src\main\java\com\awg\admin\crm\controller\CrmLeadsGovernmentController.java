package com.awg.admin.crm.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.dto.CrmLeadsGovernmentDto;
import com.awg.crm.dto.QueryBusinessStatisticsDto;
import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.service.CrmLeadsGovernmentService;
import com.awg.crm.vo.CrmLeadsGovernmentVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.List;


@ApiSupport( order = 37 )
@Api( tags = {"潜客政府费相关接口"} )
@RestController
@RequestMapping("/crm-leads/government")
@Validated
public class CrmLeadsGovernmentController extends BaseController {

    @Resource
    CrmLeadsGovernmentService crmLeadsGovernmentService;




    /**
     * 通过潜客id查询所有政府费
     * @param leadsId
     * @return
     */
    @GetMapping("leads/{leadsId}")
    @ApiOperation( "通过潜客id查询所有政府费" )
    public DataResult listByLeadsId(@PathVariable   @Min(value = 1, message = "ID必须大于等于1") Long leadsId) {
        List<CrmLeadsGovernmentDto> crmLeadsGovernmentVo = crmLeadsGovernmentService.listByLeadsId(leadsId);
        return renderSuccess(crmLeadsGovernmentVo);
    }

    /**
     * 通过政府费id查询政府费
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation( "通过政府费id查询政府费" )
    public DataResult getById(@PathVariable @Min(value = 1, message = "ID必须大于等于1") Long id) {
        CrmLeadsGovernmentDto crmLeadsGovernmentVo = crmLeadsGovernmentService.getById(id);
        return renderSuccess(crmLeadsGovernmentVo);
    }


    /**
     * 新增政府费
     * @param crmLeadsGovernmentVo
     * @return
     */
    @PostMapping("/add")
    @ApiOperation( "新增政府费" )
    public DataResult createCrmLeadsGovernment(@Validated @RequestBody  CrmLeadsGovernmentVo crmLeadsGovernmentVo) {
        crmLeadsGovernmentService.createCrmLeadsGovernment(crmLeadsGovernmentVo,  getUserLoginInfoEo());
        return renderSuccess();
    }


    /**
     * 根据政府费id删除政府费
     * @param id
     * @return
     */
    @PostMapping("/delete/{id}")
    @ApiOperation( "通过政府费id删除政府费" )
    public DataResult deleteCrmLeadsGovernmentById(@PathVariable @Min(value = 1, message = "ID必须大于等于1") Long id) {
        crmLeadsGovernmentService.deleteCrmLeadsGovernmentByld(id, getUserLoginInfoEo());
        return renderSuccess();
    }


    /**
     * 修改政府费
     * @param crmLeadsGovernmentVo
     * @return
     */
    @PostMapping("/update")
    @ApiOperation( "修改政府费" )
    public DataResult updateCrmLeadsGovernment(@Validated @RequestBody CrmLeadsGovernmentVo crmLeadsGovernmentVo) {
        crmLeadsGovernmentService.updateCrmLeadsGovernment(crmLeadsGovernmentVo, getUserLoginInfoEo());
        return renderSuccess();
    }













}
