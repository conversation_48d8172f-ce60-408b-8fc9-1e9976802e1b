package com.awg.admin.comm.controller;

import com.awg.client.dto.BusinessMemberItemDto;
import com.awg.client.vo.QueryBMemberVo;
import com.awg.client.vo.UpdateBusinessMemberVo;
import com.awg.comm.dto.*;
import com.awg.comm.entity.ProductKeyword;
import com.awg.comm.service.IProductKeywordService;
import com.awg.comm.service.IAppModuleConfigService;
import com.awg.comm.dto.AppModuleConfigDto;
import com.awg.comm.vo.AppModuleConfigVo;
import com.awg.comm.service.IProductService;
import com.awg.comm.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.redis.RedisUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.utils.json.JsonUtil;
import com.awg.website.vo.WebsiteSortVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 220 )
@Api( tags = {"商品相关接口"} )
@RestController
@RequestMapping( "/comm/product" )
public class ProductController extends BaseController {

    @Resource
    private IProductService productService;

    @Resource
    private IProductKeywordService productKeywordService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private IAppModuleConfigService appModuleConfigService;



    @ApiOperation("商品同步")
    @PostMapping("/synchronization/{productNo}/{type}")
    @ApiOperationSupport(order = 11)
    public DataResult synchronizeProduct(@PathVariable @NotBlank(message = "商品编号不能为空") String productNo, @PathVariable @NotBlank(message = "同步状态码不能为空") String type) {
        // 调用商品同步service方法
        productService.synchronizeProduct(productNo, getUserLoginInfoEo(), type);
        return renderSuccess("商品同步成功");
    }

    @ApiOperation("批量商品同步")
    @PostMapping("/batch-synchronization")
    @ApiOperationSupport(order = 12)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BatchSyncResultVo.class )
    })
    public DataResult batchSynchronizeProduct(@Validated @RequestBody BatchSynchronizeProductVo batchSyncVo) {
        // 调用批量商品同步service方法
        BatchSyncResultVo result =  productService.batchSynchronizeProduct(batchSyncVo, getUserLoginInfoEo());
        return renderSuccess(result);
    }



    @ApiOperation( "申校商品查看平台最新内容" )
    @PostMapping( "/schoolApplication/updateDetail/{productNo}" )
    @ApiOperationSupport(order = 22)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductCompareResultVo.class )
    })
    public DataResult schoolApplicationUpdateDetail(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/schoolApplication/detail/{productNo}", getCurrentOrgId(), productNo)
                , "没有权限");
        return renderSuccess(productService.getProductAndPlatformDetail(productNo, getUserLoginInfoEo()));
    }




    @ApiOperation( "签证商品列表" )
    @PostMapping( "/list" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductItemDto.class )
    })
    public DataResult getProductList(@RequestBody QueryProductVo vo){
        ValidationUtils.validate(vo);
        vo.setOnlyProductEditorList(productService.getOnlyProductEditorFlag(getUserInfoId(), "/comm/product/list", getCurrentOrgId()));
        vo.setOnlyProductEditorUpdate(productService.getOnlyProductEditorFlag(getUserInfoId(), "/comm/product/update", getCurrentOrgId()));
        vo.setUserInfoId(getUserInfoId());
        BasePageResult<ProductItemDto> result = productService.getProductList(vo, getUserLoginInfoEo(),1);

        return renderSuccess(result);
    }

    @ApiOperation( "申校商品列表" )
    @PostMapping( "/schoolApplication/list" )
    @ApiOperationSupport(order = 12)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductItemDto.class )
    })
    public DataResult schoolApplicationList(@RequestBody QueryProductVo vo){
        ValidationUtils.validate(vo);

        vo.setOnlyProductEditorList(productService.getOnlyProductEditorFlag(getUserInfoId(), "/comm/product/schoolApplication/list", getCurrentOrgId()));
        vo.setOnlyProductEditorUpdate(productService.getOnlyProductEditorFlag(getUserInfoId(), "/comm/product/schoolApplication/update", getCurrentOrgId()));

        vo.setUserInfoId(getUserInfoId());
        BasePageResult<ProductItemDto> result = productService.getProductList(vo, getUserLoginInfoEo(),2);

        return renderSuccess(result);
    }

    @ApiOperation( "本地服务商品列表" )
    @PostMapping( "/localService/list" )
    @ApiOperationSupport(order = 13)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductItemDto.class )
    })
    public DataResult localServiceList(@RequestBody QueryProductVo vo){
        ValidationUtils.validate(vo);
        vo.setOnlyProductEditorList(productService.getOnlyProductEditorFlag(getUserInfoId(), "/comm/product/localService/list", getCurrentOrgId()));
        vo.setOnlyProductEditorUpdate(productService.getOnlyProductEditorFlag(getUserInfoId(), "/comm/product/localService/update", getCurrentOrgId()));
        vo.setUserInfoId(getUserInfoId());
        BasePageResult<ProductItemDto> result = productService.getProductList(vo, getUserLoginInfoEo(),3);

        return renderSuccess(result);
    }

    @ApiOperation ( "保存记忆相关设置" )
    @PostMapping ( "/setting" )
    @SuppressWarnings ( "unchecked" )
    public DataResult updateSetting(@RequestBody ProductSetVo vo) {
        ValidationUtils.validate(vo);

        try {
            String key = "productService:setting:" + getCurrentOrgId();
            redisUtils.hPutAll(key, JsonUtil.jsonToObj(JsonUtil.objToJson(vo), Map.class));
        } catch (JsonProcessingException e) {
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "设置保存失败，请联系管理员");
        }

        return renderSuccess();
    }

    @ApiOperation ( "获取记忆相关设置" )
    @ApiImplicitParams(
            @ApiImplicitParam ( name = "orgId", value = "机构id", paramType = "Integer" )
    )
    @GetMapping ( "/setting/{orgId}" )
    public DataResult getSetting(@PathVariable ( value = "orgId" ) Integer orgId) {

        ProductSetVo vo = new ProductSetVo();

        String key = "productService:setting:" + orgId;
        if (redisUtils.exists(key)) {
            try{
                Map<String, Object> data = JsonUtil.jsonToObj(JsonUtil.objToJson(redisUtils.hGetAll(key)), Map.class);

                vo.setSalesConsultationType((Integer)data.getOrDefault("salesConsultationType", 0));
                vo.setPurchaseButtonText((String)data.getOrDefault("purchaseButtonText", "立即购买"));
                vo.setPromotionButtonText((String)data.getOrDefault("promotionButtonText", "立即抢购"));
                vo.setConsultationButtonText((String)data.getOrDefault("consultationButtonText", "立即咨询"));

            } catch (JsonProcessingException e) {
                AssertUtils.isTrue(true, "获取失败");
            }

            return renderSuccess(vo);
        }

        return renderSuccess();
    }

    @ApiOperation( "商品选择列表" )
    @PostMapping( "/selectList" )
    @ApiOperationSupport(order = 15)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductSelectDto.class )
    })
    public DataResult getProductSelectList(@RequestBody QueryProductVo vo){
        if(vo.getOrgId()==null || (!vo.getOrgId().equals(1)) ) {
            vo.setOrgId(getCurrentOrgId());
        }
        ValidationUtils.validate(vo);
        BasePageResult<ProductSelectDto> result = productService.getProductSelectList(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "签证商品详情" )
    @PostMapping( "/detail/{productNo}" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductInfoDto.class )
    })
    public DataResult getDetail(@PathVariable( value = "productNo" ) String productNo, @RequestBody ProductDetailVo vo){
        vo.setOrgId(getCurrentOrgId());
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/detail/{productNo}", getCurrentOrgId(), productNo)
                , "没有权限");
        return renderSuccess(productService.getProductDetail(productNo, getUserLoginInfoEo(), vo, true, null,1, true));
    }



    @ApiOperation( "申校商品详情" )
    @PostMapping( "/schoolApplication/detail/{productNo}" )
    @ApiOperationSupport(order = 22)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductInfoDto.class )
    })
    public DataResult schoolApplicationDetail(@PathVariable( value = "productNo" ) String productNo, @RequestBody ProductDetailVo vo){
        vo.setOrgId(getCurrentOrgId());
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/schoolApplication/detail/{productNo}", getCurrentOrgId(), productNo)
                , "没有权限");
        return renderSuccess(productService.getProductDetail(productNo, getUserLoginInfoEo(), vo, true, null,2, true));
    }

    @ApiOperation( "本地服务商品详情" )
    @PostMapping( "/localService/detail/{productNo}" )
    @ApiOperationSupport(order = 23)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductInfoDto.class )
    })
    public DataResult localServiceDetail(@PathVariable( value = "productNo" ) String productNo, @RequestBody ProductDetailVo vo){
        vo.setOrgId(getCurrentOrgId());
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/localService/detail/{productNo}", getCurrentOrgId(), productNo)
                , "没有权限");
        return renderSuccess(productService.getProductDetail(productNo, getUserLoginInfoEo(), vo, true, null,3, true));
    }

    @ApiOperation( "签证商品赠送优惠券信息" )
    @PostMapping( "/giftCouponInfo/{productNo}" )
    @ApiOperationSupport(order = 25)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = GiftCouponInfoDto.class )
    })
    public DataResult giftCouponInfo(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        return renderSuccess(productService.giftCouponInfo(productNo, 1));
    }

    @ApiOperation( "申校商品赠送优惠券信息" )
    @PostMapping( "/schoolApplication/giftCouponInfo/{productNo}" )
    @ApiOperationSupport(order = 27)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = GiftCouponInfoDto.class )
    })
    public DataResult schoolApplicationGiftCouponInfo(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        return renderSuccess(productService.giftCouponInfo(productNo, 2));
    }

    @ApiOperation( "本地服务赠送优惠券信息" )
    @PostMapping( "/localService/giftCouponInfo/{productNo}" )
    @ApiOperationSupport(order = 28)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = GiftCouponInfoDto.class )
    })
    public DataResult localServiceGiftCouponInfo(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        return renderSuccess(productService.giftCouponInfo(productNo, 3));
    }

    @ApiOperation( "创建签证商品" )
    @PostMapping( "/create" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult createProduct(@RequestBody ProductVo vo){
        vo.setChangeStock(true);
        vo.setChangePromoStock(true);
        vo.setCategory(1);

        if(vo.getStock()==null) {
            vo.setStock(0);
        }

        ValidationUtils.validate(vo);
        vo.getPdfList().forEach(pdfVo -> {
            ValidationUtils.validate(pdfVo);
        });

        // 循环验证必须材料
        vo.getRequiredMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证非必须材料
        vo.getOptionalMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证分组材料
        vo.getMaterialGroupList().forEach(materialGroupVo -> {
            ValidationUtils.validate(materialGroupVo);

            // 循环验证材料
            materialGroupVo.getMaterialList().forEach(materialVo -> {
                ValidationUtils.validate(materialVo);

                // 循环校验图片
                materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                    ValidationUtils.validate(materialImageRelationVo);
                });
            });
        });


        // 循环校验人员
        vo.getCopywriterList().forEach(personVo -> {
            ValidationUtils.validate(personVo);
        });

        vo.getCouponList().forEach(couponVo -> {
            ValidationUtils.validate(couponVo);
        });


        String productNo = productService.createProduct(vo, getUserLoginInfoEo());

        return renderSuccess(productNo);
    }

    @ApiOperation( "创建申校商品" )
    @PostMapping( "/schoolApplication/create" )
    @ApiOperationSupport(order = 32)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult createSchoolApplicationProduct(@RequestBody ProductVo vo){
        vo.setChangeStock(true);
        vo.setChangePromoStock(true);
        vo.setCategory(2);

        if(vo.getStock()==null) {
            vo.setStock(0);
        }

        ValidationUtils.validate(vo);
        vo.getPdfList().forEach(pdfVo -> {
            ValidationUtils.validate(pdfVo);
        });

        // 循环验证必须材料
        vo.getRequiredMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证非必须材料
        vo.getOptionalMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证分组材料
        vo.getMaterialGroupList().forEach(materialGroupVo -> {
            ValidationUtils.validate(materialGroupVo);

            // 循环验证材料
            materialGroupVo.getMaterialList().forEach(materialVo -> {
                ValidationUtils.validate(materialVo);

                // 循环校验图片
                materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                    ValidationUtils.validate(materialImageRelationVo);
                });
            });
        });


        // 循环校验人员
        vo.getCopywriterList().forEach(personVo -> {
            ValidationUtils.validate(personVo);
        });

        vo.getCouponList().forEach(couponVo -> {
            ValidationUtils.validate(couponVo);
        });


        String productNo = productService.createProduct(vo, getUserLoginInfoEo());

        return renderSuccess(productNo);
    }

    @ApiOperation( "创建本地服务商品" )
    @PostMapping( "/localService/create" )
    @ApiOperationSupport(order = 33)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult createLocalServiceProduct(@RequestBody ProductVo vo){
        vo.setChangeStock(true);
        vo.setChangePromoStock(true);
        vo.setCategory(3);

        if(vo.getStock()==null) {
            vo.setStock(0);
        }

        ValidationUtils.validate(vo);
        vo.getPdfList().forEach(pdfVo -> {
            ValidationUtils.validate(pdfVo);
        });

        // 循环验证必须材料
        vo.getRequiredMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证非必须材料
        vo.getOptionalMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证分组材料
        vo.getMaterialGroupList().forEach(materialGroupVo -> {
            ValidationUtils.validate(materialGroupVo);

            // 循环验证材料
            materialGroupVo.getMaterialList().forEach(materialVo -> {
                ValidationUtils.validate(materialVo);

                // 循环校验图片
                materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                    ValidationUtils.validate(materialImageRelationVo);
                });
            });
        });


        // 循环校验人员
        vo.getCopywriterList().forEach(personVo -> {
            ValidationUtils.validate(personVo);
        });

        vo.getCouponList().forEach(couponVo -> {
            ValidationUtils.validate(couponVo);
        });


        String productNo = productService.createProduct(vo, getUserLoginInfoEo());

        return renderSuccess(productNo);
    }

    @ApiOperation( "编辑签证商品" )
    @PostMapping( "/update" )
    @ApiOperationSupport(order = 40)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateProduct(@RequestBody ProductVo vo){
        vo.setCategory(1);

        if(vo.getStock()==null) {
            vo.setStock(0);
        }

        ValidationUtils.validate(vo);
        vo.getPdfList().forEach(pdfVo -> {
            ValidationUtils.validate(pdfVo);
        });

        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/update", getCurrentOrgId(), vo.getProductNo())
                , "没有权限");

        // 循环验证必须材料
        vo.getRequiredMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证非必须材料
        vo.getOptionalMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证分组材料
        vo.getMaterialGroupList().forEach(materialGroupVo -> {
            ValidationUtils.validate(materialGroupVo);

            // 循环验证材料
            materialGroupVo.getMaterialList().forEach(materialVo -> {
                ValidationUtils.validate(materialVo);

                // 循环校验图片
                materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                    ValidationUtils.validate(materialImageRelationVo);
                });
            });
        });

        // 循环校验人员
        vo.getCopywriterList().forEach(personVo -> {
            ValidationUtils.validate(personVo);
        });
        vo.getCouponList().forEach(couponVo -> {
            ValidationUtils.validate(couponVo);
        });
        String productNo = productService.updateProduct(vo, getUserLoginInfoEo());

        return renderSuccess(productNo);
    }

    @ApiOperation( "编辑申校商品" )
    @PostMapping( "/schoolApplication/update" )
    @ApiOperationSupport(order = 42)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateSchoolApplicationProduct(@RequestBody ProductVo vo){
        vo.setCategory(2);

        if(vo.getStock()==null) {
            vo.setStock(0);
        }

        ValidationUtils.validate(vo);
        vo.getPdfList().forEach(pdfVo -> {
            ValidationUtils.validate(pdfVo);
        });

        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/schoolApplication/update", getCurrentOrgId(), vo.getProductNo())
                , "没有权限");

        // 循环验证必须材料
        vo.getRequiredMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证非必须材料
        vo.getOptionalMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证分组材料
        vo.getMaterialGroupList().forEach(materialGroupVo -> {
            ValidationUtils.validate(materialGroupVo);

            // 循环验证材料
            materialGroupVo.getMaterialList().forEach(materialVo -> {
                ValidationUtils.validate(materialVo);

                // 循环校验图片
                materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                    ValidationUtils.validate(materialImageRelationVo);
                });
            });
        });

        // 循环校验人员
        vo.getCopywriterList().forEach(personVo -> {
            ValidationUtils.validate(personVo);
        });
        vo.getCouponList().forEach(couponVo -> {
            ValidationUtils.validate(couponVo);
        });
        String productNo = productService.updateProduct(vo, getUserLoginInfoEo());

        return renderSuccess(productNo);
    }

    @ApiOperation("获取小程序模块配置")
    @PostMapping("/appModuleConfig/get")
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AppModuleConfigDto.class )
    })
    public DataResult getAppModuleConfig(){
        return renderSuccess(appModuleConfigService.getAppModuleConfig(getCurrentOrgId()));
    }

    @ApiOperation("保存小程序模块配置")
    @PostMapping("/appModuleConfig/set")
    @ApiOperationSupport(order = 51)
    public DataResult setAppModuleConfig(@RequestBody AppModuleConfigVo vo){
        ValidationUtils.validate(vo);
        appModuleConfigService.setAppModuleConfig(vo, getCurrentOrgId());
        return renderSuccess();
    }

    @ApiOperation( "编辑本地服务商品" )
    @PostMapping( "/localService/update" )
    @ApiOperationSupport(order = 43)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateLocalServiceProduct(@RequestBody ProductVo vo){
        vo.setCategory(3);

        if(vo.getStock()==null) {
            vo.setStock(0);
        }

        ValidationUtils.validate(vo);
        vo.getPdfList().forEach(pdfVo -> {
            ValidationUtils.validate(pdfVo);
        });

        AssertUtils.isFalse(
                productService.checkProductPermission(getUserInfoId(), "/comm/product/localService/update", getCurrentOrgId(), vo.getProductNo())
                , "没有权限");

        // 循环验证必须材料
        vo.getRequiredMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证非必须材料
        vo.getOptionalMaterialList().forEach(materialVo -> {
            ValidationUtils.validate(materialVo);

            // 循环校验图片
            materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                ValidationUtils.validate(materialImageRelationVo);
            });
        });

        // 循环验证分组材料
        vo.getMaterialGroupList().forEach(materialGroupVo -> {
            ValidationUtils.validate(materialGroupVo);

            // 循环验证材料
            materialGroupVo.getMaterialList().forEach(materialVo -> {
                ValidationUtils.validate(materialVo);

                // 循环校验图片
                materialVo.getMaterialImageList().forEach(materialImageRelationVo -> {
                    ValidationUtils.validate(materialImageRelationVo);
                });
            });
        });

        // 循环校验人员
        vo.getCopywriterList().forEach(personVo -> {
            ValidationUtils.validate(personVo);
        });
        vo.getCouponList().forEach(couponVo -> {
            ValidationUtils.validate(couponVo);
        });
        String productNo = productService.updateProduct(vo, getUserLoginInfoEo());

        return renderSuccess(productNo);
    }

    @ApiOperation( "删除签证商品" )
    @PostMapping( "/delete/{productNo}" )
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult deleteProduct(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        return renderSuccess(productService.deleteProduct(productNo, getUserLoginInfoEo(), 1));
    }

    @ApiOperation( "删除申校商品" )
    @PostMapping( "/schoolApplication/delete/{productNo}" )
    @ApiOperationSupport(order = 52)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult deleteSchoolApplicationProduct(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        return renderSuccess(productService.deleteProduct(productNo, getUserLoginInfoEo(), 2));
    }

    @ApiOperation( "删除本地服务商品" )
    @PostMapping( "/localService/delete/{productNo}" )
    @ApiOperationSupport(order = 53)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult deleteLocalServiceProduct(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        return renderSuccess(productService.deleteProduct(productNo, getUserLoginInfoEo(), 3));
    }

    @ApiOperationSupport(order = 55)
    @ApiOperation( "签证商品排序" )
    @PostMapping( "/sortProduct" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortProduct(@RequestBody ProductSortVo vo) {
        ValidationUtils.validate(vo);
        productService.sortProduct(vo, getUserLoginInfoEo(), 1);
        return renderSuccess();
    }

    @ApiOperationSupport(order = 57)
    @ApiOperation( "申校商品排序" )
    @PostMapping( "/schoolApplication/sortProduct" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortSchoolApplicationProduct(@RequestBody ProductSortVo vo) {
        ValidationUtils.validate(vo);
        productService.sortProduct(vo, getUserLoginInfoEo(), 2);
        return renderSuccess();
    }

    @ApiOperationSupport(order = 58)
    @ApiOperation( "本地服务商品排序" )
    @PostMapping( "/localService/sortProduct" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortLocalServiceProduct(@RequestBody ProductSortVo vo) {
        ValidationUtils.validate(vo);
        productService.sortProduct(vo, getUserLoginInfoEo(), 3);
        return renderSuccess();
    }

    @ApiOperation( "签证商品状态改变" )
    @PostMapping( "/statusChange" )
    @ApiOperationSupport(order = 60)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult statusChange(@RequestBody ProductStatusChangeVo vo){
        ValidationUtils.validate(vo);

        return renderSuccess(productService.ProductStatusChange(vo, getUserLoginInfoEo(), 1));
    }

    @ApiOperation( "申校商品状态改变" )
    @PostMapping( "/schoolApplication/statusChange" )
    @ApiOperationSupport(order = 62)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult schoolApplicationStatusChange(@RequestBody ProductStatusChangeVo vo){
        ValidationUtils.validate(vo);

        return renderSuccess(productService.ProductStatusChange(vo, getUserLoginInfoEo(), 2));
    }

    @ApiOperation( "本地服务商品状态改变" )
    @PostMapping( "/localService/statusChange" )
    @ApiOperationSupport(order = 63)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult localServiceStatusChange(@RequestBody ProductStatusChangeVo vo){
        ValidationUtils.validate(vo);

        return renderSuccess(productService.ProductStatusChange(vo, getUserLoginInfoEo(), 3));
    }

    @ApiOperation( "获取关键词列表" )
    @PostMapping( "/getProductKeywordAll" )
    @ApiOperationSupport(order = 67)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductKeyword.class )
    })
    public DataResult getProductKeywordAll(@RequestBody QueryDistrictVo vo){
        vo.setOrgId(getCurrentOrgId());
        return renderSuccess(productKeywordService.getProductKeywordAll(vo, null));
    }

    @ApiOperation( "材料库材料列表" )
    @PostMapping( "/materialLibraryList" )
    @ApiOperationSupport(order = 70)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MaterialDto.class )
    })
    public DataResult materialLibraryList(@RequestBody QueryMaterialVo vo){
        if(vo.getOrgId()==null || (!vo.getOrgId().equals(1)) ) {
            vo.setOrgId(getCurrentOrgId());
        }
        ValidationUtils.validate(vo);
        BasePageResult<MaterialDto> result = productService.materialLibraryList(vo, getCurrentOrgId());

        return renderSuccess(result);
    }

    @ApiOperation( "材料库-添加" )
    @PostMapping( "/materialLibraryAdd" )
    @ApiOperationSupport(order = 80)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult materialLibraryAdd(@RequestBody MaterialVo vo){
        ValidationUtils.validate(vo);
        productService.materialLibraryAdd(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "材料库-编辑" )
    @PostMapping( "/materialLibraryUpdate" )
    @ApiOperationSupport(order = 90)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult materialLibraryUpdate(@RequestBody MaterialVo vo){
        ValidationUtils.validate(vo);
        productService.materialLibraryUpdate(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "材料库-删除" )
    @PostMapping( "/materialLibraryDelete/{materialNo}" )
    @ApiOperationSupport(order = 100)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult materialLibraryDelete(@PathVariable( value = "materialNo" ) String materialNo){
        AssertUtils.isTrue(StringUtils.isBlank(materialNo), "材料编号不能为空");
        return renderSuccess(productService.materialLibraryDelete(materialNo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 110)
    @ApiOperation( "材料库-排序" )
    @PostMapping( "/materialLibrarySort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult materialLibrarySort(@RequestBody ProductSortVo vo) {
        ValidationUtils.validate(vo);
        productService.materialLibrarySort(vo, getUserLoginInfoEo());
        return renderSuccess();
    }
}
