package com.awg.common.config.thread;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 * <b>ThreadPoolConfig</b> is 线程池
 * </p>
 *
 * <AUTHOR>
 * @date 2022/3/12 16:16
 */
@Configuration
@EnableAsync //开启多线程
public class ThreadPoolConfig {

    @Bean("taskExecutor")
    public Executor asyncServiceExecutor(){
        /**
         * 工作顺序：
         * 1、线程池创建，准备好core数量的核心线程，准备接受任务
         * 2、core满了，就将再进来的任务放到阻塞队列中，空闲的core就会自己去阻塞队列获取任务直销
         * 3、max满了就RejectedExecutionHandler拒绝任务
         * 4、max都直销完成，有很多空闲，在指定时间keepAliveTime以后，释放max-core这些线程
         *
         * new LinkedBlockingDeque<() :默认是Integer的最大值，内存会泄露
         */


        ThreadPoolTaskExecutor executor=new ThreadPoolTaskExecutor();
        //设置核心线程数
        executor.setCorePoolSize(20);
        //设置最大线程数
        executor.setMaxPoolSize(40);
        //配置队列大小
        executor.setQueueCapacity(300);
        //设置线程活跃时间（秒） []
        executor.setKeepAliveSeconds(60);
        //设置线程名称
        executor.setThreadNamePrefix("同步数据线程池");
        //配置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //等待所有任务结束后再关闭线程
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //执行初始化
        executor.initialize();
        return executor;
    }
}