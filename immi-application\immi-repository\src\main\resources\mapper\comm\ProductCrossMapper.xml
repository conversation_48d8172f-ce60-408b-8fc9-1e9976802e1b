<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.awg.comm.mapper.ProductCrossMapper">
    <select id="getProductList" resultType="com.awg.comm.dto.ProductItemDto">
        select
            p.id as product_id,
            p.product_no as productNo,
            p.product_vid as productVid,
            p.is_sync,
            p.show_update_btn,
            p.is_modified,
            pd.category,
            pd.educational_stage,
            pd.keyword_ids,
            pd.district_id,
            pd.source_no,
            pd.source_type,
            pd.delivery_type,
            CASE WHEN pd.source_no > 0 THEN source_pd.platform_delivery_price ELSE NULL END as platform_delivery_price,
            pd.sales_consultation_type,
            pd.purchase_button_text,
            pd.promotion_button_text,
            pd.consultation_button_text,
            pd.consultant_wechat,
            pd.consultant_qrcode,
            CASE
                WHEN cdip.id > 0 THEN CONCAT(cdip.name,'-', cdi.name)
                ELSE cdi.name
            END AS districtName,
            pd.secondary_category,
            cpsc.name AS secondaryCategoryName,
            pd.name as name,
            b.business_no as relatedBusinessNo,
            b.name as relatedBusinessName,
            bc.business_no as relatedChildBusinessNo,
            bc.name as relatedChildBusinessName,
            ifnull(sales.salesVolume, 0) as salesVolume,
            ifnull(sales.salesAmount, 0) as salesAmount,
            pd.price,
            pd.promo_price,
            pd.cover,
            pd.school_logo as schoolLogo,
            p.availability_status,
            p.resettable_stock as stock,
            ifnull(p.promo_stock, '') as promoStock,
            p.stock_open_flag,
            p.promo_open_flag,
            gnt.giftCouponNum,
            <if test="query.onlyProductEditorUpdate != null and query.onlyProductEditorUpdate != 0">
                CASE WHEN cper.id > 0 THEN 1 ELSE 0 END as editButtonFlag,
            </if>
            <if test="query.onlyProductEditorUpdate == null or query.onlyProductEditorUpdate == 0">
                CASE WHEN p.id > 0 THEN 1 ELSE 0 END as editButtonFlag,
            </if>
            p.created_at as createTime
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        left join bp_business b on b.business_no = pd.related_business_no and b.is_delete = 0 and b.level = 1
                                       and ( b.org_id = p.org_id or (p.org_id=1 and b.org_id=0) )
        left join bp_business bc on bc.business_no = pd.related_child_business_no and bc.is_delete = 0 and bc.level = 2
                                        and ( bc.org_id = p.org_id or (p.org_id=1 and bc.org_id=0) )
        left join comm_district_info cdi on cdi.id = pd.district_id and cdi.is_delete = 0
        left join comm_district_info cdip on cdip.id = cdi.pid and cdip.is_delete = 0
        left join comm_product_secondary_category cpsc on cpsc.id = pd.secondary_category and cpsc.is_delete = 0
        left join comm_product source_p on pd.source_no > 0 and source_p.product_no = pd.source_no
        left join comm_product_data source_pd on source_p.product_no = source_pd.product_no and source_p.product_vid = source_pd.product_vid
        left join (
            select oop.product_no, count(oop.id) as salesVolume, sum(oo.order_amount) as salesAmount from ord_order oo
            left join ord_order_product oop on oop.order_no = oo.order_no and oop.is_delete = 0
            where
                oo.is_delete = 0
                and oo.status in (1,2)
                and oo.member_id > 0
                and oop.id > 0
                <!--每个订单目前只有一个商品，这里进行特殊处理，防止出现多个订单-->
                and
                (
                    oop.id in (
                        select min(id) from ord_order_product where is_delete = 0 group by order_no
                    )
                    or oop.id is null
                )
            group by oop.product_no
        ) sales on sales.product_no = p.product_no
        left join (
            select count(*) as giftCouponNum, product_no, product_vid from comm_product_gift_coupon_relation cpgcr
            group by product_no, product_vid
        ) gnt on gnt.product_no = p.product_no and gnt.product_vid = p.product_vid
        <if test="query.onlyProductEditorUpdate != null and query.onlyProductEditorUpdate != 0">
            left join comm_product_editor_relation cper on cper.user_info_id = #{query.userInfoId} and cper.is_delete = 0 and p.product_no = cper.product_no
        </if>
        where
            p.is_delete = 0
            and pd.category = #{category}

            <if test="query.orgId!=null">
                and p.org_id = #{query.orgId}
            </if>
            <if test="query.orgId!=null">
                and pd.org_id = #{query.orgId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and
                (
                    pd.name like concat('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.onlyProductEditorList != null and query.onlyProductEditorList != 0">
                and p.product_no in
                (
                    select product_no from comm_product_editor_relation
                    where user_info_id = #{query.userInfoId}
                    and is_delete = 0
                )
            </if>
            <if test="query.secondaryCategory != null and query.secondaryCategory!= ''">
                and pd.secondary_category = #{query.secondaryCategory}
            </if>
            <if test="query.keywordIdList != null and query.keywordIdList.size!=0">
                and (
                <foreach collection="query.keywordIdList" item="keywordId" open="(" separator=" or " close=")">
                    concat(',',pd.keyword_ids,',') like concat('%,', #{keywordId}, ',%')
                </foreach>
                )
            </if>
            <if test="query.priceMin != null and query.priceMin != ''">
                and
                (
                    pd.price <![CDATA[>=]]> #{query.priceMin}
                )
            </if>
            <if test="query.priceMax != null and query.priceMax != ''">
                and
                (
                    pd.price <![CDATA[<=]]> #{query.priceMax}
                )
            </if>
            <if test="query.availabilityStatus != null">
                and
                (
                    p.availability_status = #{query.availabilityStatus}
                )
            </if>
            <if test="query.districtId != null and query.districtId>0">
                and
                (
                cdi.id = #{query.districtId}
                or cdip.id = #{query.districtId}
                )
            </if>
            <if test="query.educationalStage != null and query.educationalStage>0">
                and pd.educational_stage = #{query.educationalStage}
            </if>
        order by p.sort asc
    </select>

    <select id="getProductSelectList" resultType="com.awg.comm.dto.ProductSelectDto">
        select
            p.product_no as productNo,
            p.product_vid as productVid,
            p.category,
            p.availability_status,
            b.business_no as relatedBusinessNo,
            b.name as relatedBusinessName,
            bc.business_no as relatedChildBusinessNo,
            bc.name as relatedChildBusinessName,
            pd.platform_delivery_price,
            pd.name as name,
            pd.price
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        left join bp_business b on b.business_no = pd.related_business_no and b.is_delete = 0 and b.level = 1
                                       and ( b.org_id = p.org_id or (p.org_id=1 and b.org_id=0) )
        left join bp_business bc on bc.business_no = pd.related_child_business_no and bc.is_delete = 0 and bc.level = 2
                                        and ( bc.org_id = p.org_id or (p.org_id=1 and bc.org_id=0) )
        where
        p.is_delete = 0

        <if test="query.orgId!=null">
            and p.org_id = #{query.orgId}
        </if>
        <if test="query.orgId!=null">
            and pd.org_id = #{query.orgId}
        </if>
        <if test="query.category != null and query.category>0">
            and p.category = #{query.category}
        </if>

        <if test="query.keyword != null and query.keyword != ''">
            and
            (
                pd.name like concat('%', #{query.keyword}, '%')
            )
        </if>
        <if test="query.relatedBusinessNo != null and query.relatedBusinessNo != ''">
            and
            (
                b.business_no = #{query.relatedBusinessNo}
            )
        </if>
        <if test="query.priceMin != null and query.priceMin != ''">
            and
            (
                pd.price <![CDATA[>=]]> #{query.priceMin}
            )
        </if>
        <if test="query.priceMax != null and query.priceMax != ''">
            and
            (
                pd.price <![CDATA[<=]]> #{query.priceMax}
            )
        </if>
        <if test="query.availabilityStatus != null">
            and
            (
                p.availability_status = #{query.availabilityStatus}
            )
        </if>
        order by p.sort asc
    </select>

    <select id="getProductMiniList" resultType="com.awg.comm.dto.ProductMinDto">
        select
            p.product_no as productNo,
            p.product_vid as productVid,
            pd.category,
            pd.educational_stage,
            pd.district_id,
            pd.source_no,
            pd.source_type,
            pd.delivery_type,
            pd.platform_delivery_price,
            pd.sales_consultation_type,
            pd.purchase_button_text,
            pd.promotion_button_text,
            pd.consultation_button_text,
            pd.consultant_wechat,
            pd.consultant_qrcode,
            CASE
                WHEN cdip.id > 0 THEN CONCAT(cdip.name,'-', cdi.name)
                ELSE cdi.name
            END AS districtName,
            pd.secondary_category,
            cpsc.name AS secondaryCategoryName,
            pd.name as name,
            b.business_no as relatedBusinessNo,
            b.name as relatedBusinessName,
            bc.business_no as relatedChildBusinessNo,
            bc.name as relatedChildBusinessName,
            pd.price,
            pd.promo_price,
            pd.cover,
            pd.cover_width as coverWidth,
            pd.cover_height as coverHeight,
            pd.school_logo as schoolLogo,
            pd.description,
            pd.notes,
            pd.keyword_ids,
            p.availability_status,
            p.resettable_stock as stock,
            ifnull(p.promo_stock, '') as promoStock,
            p.stock_open_flag,
            p.promo_open_flag,
            p.created_at as createTime
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        left join bp_business b on b.business_no = pd.related_business_no and b.is_delete = 0 and b.level = 1
                                       and ( b.org_id = p.org_id or (p.org_id=1 and b.org_id=0) )
        left join bp_business bc on bc.business_no = pd.related_child_business_no and bc.is_delete = 0 and bc.level = 2
                                        and ( bc.org_id = p.org_id or (p.org_id=1 and bc.org_id=0) )
        left join comm_district_info cdi on cdi.id = pd.district_id and cdi.is_delete = 0
        left join comm_district_info cdip on cdip.id = cdi.pid and cdip.is_delete = 0
        left join comm_product_secondary_category cpsc on cpsc.id = pd.secondary_category and cpsc.is_delete = 0
        where
        p.is_delete = 0

        <if test="query.orgId!=null">
            and p.org_id = #{query.orgId}
        </if>
        <if test="query.orgId!=null">
            and pd.org_id = #{query.orgId}
        </if>
        <if test="query.category != null and query.category>0">
            and pd.category = #{query.category}
        </if>
        <if test="query.secondaryCategory != null and query.secondaryCategory!= ''">
            and pd.secondary_category = #{query.secondaryCategory}
        </if>
        <if test="query.keywordIdList != null and query.keywordIdList.size!=0">
            and (
            <foreach collection="query.keywordIdList" item="keywordId" open="(" separator=" or " close=")">
                concat(',',pd.keyword_ids,',') like concat('%,', #{keywordId}, ',%')
            </foreach>
            )
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            and
            (
                pd.name like concat('%', #{query.keyword}, '%')
            )
        </if>
        <if test="query.priceMin != null and query.priceMin != ''">
            and
            (
                case when
                    p.stock_open_flag = 1 AND p.promo_open_flag = 1 AND p.promo_stock>0 AND pd.promo_price IS NOT null
                then
                    pd.promo_price <![CDATA[>=]]> #{query.priceMin}
                ELSE
                    pd.price <![CDATA[>=]]> #{query.priceMin}
                END
            )
        </if>
        <if test="query.priceMax != null and query.priceMax != ''">
            and
            (
                case when
                    p.stock_open_flag = 1 AND p.promo_open_flag = 1 AND p.promo_stock>0 AND pd.promo_price IS NOT null
                then
                    pd.promo_price <![CDATA[<=]]> #{query.priceMax}
                ELSE
                    pd.price <![CDATA[<=]]> #{query.priceMax}
                END
            )
        </if>
        <if test="query.availabilityStatus != null">
            and
            (
                p.availability_status = #{query.availabilityStatus}
            )
        </if>
        <if test="query.districtId != null and query.districtId>0">
            and
            (
                cdi.id = #{query.districtId}
                or cdip.id = #{query.districtId}
            )
        </if>
        <if test="query.educationalStage != null and query.educationalStage>0">
            and pd.educational_stage = #{query.educationalStage}
        </if>
        order by p.sort asc
    </select>

    <select id="materialLibraryList" resultType="com.awg.comm.dto.MaterialDto">
        select
            cm.material_no,
            cm.title,
            cm.material_type,
            cm.remarks,
            cm.library_id,
            cm.source
        from comm_material cm
        where
        cm.is_delete = 0
        and material_library_flag = 1
        <if test="query.orgId!=null">
            and cm.org_id = #{query.orgId}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            and
            (
                cm.title like concat('%', #{query.keyword}, '%')
            )
        </if>
        order by cm.sort asc
    </select>

    <select id="getProductListByMaterialNo" resultType="com.awg.comm.entity.Product">
        select
            distinct cp.product_no
        from comm_product cp
        left join comm_material cm on cp.product_no = cm.product_no and cp.product_vid = cm.product_vid
        where
            cp.is_delete = 0
            and cm.is_delete = 0
            and cm.material_no = #{materialNo}
    </select>

    <select id="getReuseLastCopywriterRecordList" resultType="com.awg.comm.entity.CopywriterAssignmentRecord">
        select
            cp.*
        from comm_copywriter_assignment_record cp
            left join ord_order oo on cp.order_no = oo.order_no and oo.is_delete = 0
        where
            cp.is_delete = 0
            and oo.member_id = #{memberId}
            and cp.product_no = #{productNo}
        order by cp.created_at desc
    </select>

    <select id="getProductBannerDtoList" resultType="com.awg.comm.dto.ProductBannerDto">
        select
            pb.type,
            pb.description,
            pb.path,
            pb.upload_record_no,
            pur.status,
            pur.vid,
            pur.width,
            pur.height,
            pur.duration,
            pur.cover_url
        from comm_product_banner pb
                 left join plv_upload_record pur on pur.upload_record_no = pb.upload_record_no and pur.is_delete = 0
        where
            pb.is_delete = 0
          and pb.product_vid = #{productVid}
          and pb.product_no = #{productNo}
        order by pb.sort asc
    </select>

    <select id="countChildBusinessByNo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        <where>
            p.is_delete = 0
            and pd.is_delete = 0
            and pd.related_child_business_no = #{childBusinessNo}
        </where>
    </select>

    <select id="getUseChildBusiness" resultType="com.awg.comm.eo.ProductNoEo">
        SELECT p.product_no
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        <where>
            p.is_delete = 0
            and pd.is_delete = 0
            and pd.related_child_business_no = #{childBusinessNo}
        </where>
    </select>

    <select id="countDistrictById" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        left join comm_district_info cdi on cdi.id = pd.district_id and cdi.is_delete = 0
        left join comm_district_info cdip on cdip.id = cdi.pid and cdip.is_delete = 0
        <where>
            p.is_delete = 0
            and pd.is_delete = 0
            and
            (
                pd.district_id = #{districtId}
                or cdip.id = #{districtId}
            )
        </where>
    </select>

    <select id="countSecondaryCategoryById" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        <where>
            p.is_delete = 0
            and pd.is_delete = 0
            and pd.secondary_category = #{secondaryCategory}
        </where>
    </select>
    <select id="countKeywordById" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from comm_product p
        left join comm_product_data pd on pd.product_no = p.product_no and pd.product_vid = p.product_vid
        <where>
            p.is_delete = 0
            and pd.is_delete = 0
            and concat(',', pd.keyword_ids, ',') like concat('%,', #{keywordId}, ',%')
            <if test="orgId != null and orgId != 1">
                and p.org_id = #{orgId}
                and pd.org_id = #{orgId}
            </if>
        </where>
    </select>
</mapper>