package com.awg.crm.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;



@Data
@TableName("crm_leads_government")
@ApiModel(value = "CrmLeadsGovernment对象", description = "政府费参数")
public class CrmLeadsGovernment extends NewBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("潜客id")
    private Integer leadsId;

    @ApiModelProperty("费用名称")
    private String feeName;

    @ApiModelProperty("实付金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("金额单位")
    private String currencyUnit;




}
