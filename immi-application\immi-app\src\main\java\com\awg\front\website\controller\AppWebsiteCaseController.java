package com.awg.front.website.controller;

import com.awg.comm.dto.ProductMinDto;
import com.awg.comm.vo.QueryProductVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.CaseApprovalLetterDto;
import com.awg.website.dto.CaseDataDto;
import com.awg.website.dto.CaseDto;
import com.awg.website.service.IWebsiteCaseService;
import com.awg.website.vo.QueryCaseVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 85 )
@Api( tags = {"案例相关接口"} )
@RestController
@RequestMapping( "/app/website/case" )
public class AppWebsiteCaseController extends BaseController {

    @Resource
    private IWebsiteCaseService websiteCaseService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperation( "案例列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = CaseDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult list(@RequestBody QueryCaseVo vo) {
        if(vo.getDeliveryType()==null) {
            vo.setDeliveryType(0);
        }
        if(vo.getOrgId()==null) {
            vo.setOrgId(1);
        }
        vo.setWatermarkOrgId(vo.getOrgId());
        if(vo.getDeliveryType().equals(1)) {
            vo.setOrgId(1);
        }
        vo.setRegion("CA");

        BasePageResult<CaseDto> caseResult = websiteCaseService.queryCaseList(vo, vo.getOrgId(),1);

        // 循环填入完整路径
        for (CaseDto caseDto : caseResult.getData()) {
            for( CaseApprovalLetterDto caseApprovalLetterDto : caseDto.getApprovalLetterList() ){
                caseApprovalLetterDto.setApprovalLetterFileUrl(
                        FileBaseUtil.getFileUrl(caseApprovalLetterDto.getApprovalLetterFilePath())
                );
            }
        }

        return DataResult.success(caseResult);
    }

    @ApiOperation( "案例详细描述" )
    @PostMapping( "/description/{caseNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult description(@PathVariable( value = "caseNo" ) String caseNo) {
        AssertUtils.isTrue(StringUtils.isBlank(caseNo), "案例编号不能为空");

        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(1);
        CaseDto caseDto = websiteCaseService.caseDetail(caseNo, userLoginInfoEo, 1);
        AssertUtils.isNull(caseDto, "案例不存在");

        Map<String, Object> result = new HashMap<>(16);
        result.put("description", caseDto.getDescription());
        result.put("name", caseDto.getName());

        return DataResult.success(result);
    }

    @ApiOperation( "案例详细描述" )
    @PostMapping( "/description" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult descriptionNew(@RequestBody QueryCaseVo vo) {
        AssertUtils.isTrue(StringUtils.isBlank(vo.getCaseNo()), "案例编号不能为空");

        if(vo.getDeliveryType()==null) {
            vo.setDeliveryType(0);
        }
        if(vo.getOrgId()==null) {
            vo.setOrgId(1);
        }
        vo.setWatermarkOrgId(vo.getOrgId());
        if(vo.getDeliveryType().equals(1)) {
            vo.setOrgId(1);
        }

        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());
        CaseDto caseDto = websiteCaseService.caseDetail(vo.getCaseNo(), userLoginInfoEo, vo.getWatermarkOrgId());
        AssertUtils.isNull(caseDto, "案例不存在");

        Map<String, Object> result = new HashMap<>(16);
        result.put("description", caseDto.getDescription());
        result.put("name", caseDto.getName());

        return DataResult.success(result);
    }
}
