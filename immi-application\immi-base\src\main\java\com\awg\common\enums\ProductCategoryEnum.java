package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b>商品分类枚举</b>
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ProductCategoryEnum {

    /**
     * 商品分类（1=签证类，2=申校类，3=本地服务）
     */
    VISA(1, "签证类"),
    SCHOOL_APPLICATION(2, "申校类"),
    LOCAL_SERVICE(3, "本地服务");

    private final Integer code;
    private final String label;

    /**
     * 根据code获取枚举
     *
     * @param code 商品分类代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProductCategoryEnum parse(Integer code) {
        return Arrays.stream(ProductCategoryEnum.values())
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据label获取枚举
     *
     * @param label 商品分类名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProductCategoryEnum parse(String label) {
        return Arrays.stream(ProductCategoryEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的商品分类代码
     *
     * @param code 商品分类代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValid(Integer code) {
        return parse(code) != null;
    }

    /**
     * 判断是否为签证类商品
     *
     * @param code 商品分类代码
     * @return true表示是签证类，false表示不是
     */
    public static boolean isVisa(Integer code) {
        ProductCategoryEnum category = parse(code);
        return category != null && category == VISA;
    }

    /**
     * 判断是否为申校类商品
     *
     * @param code 商品分类代码
     * @return true表示是申校类，false表示不是
     */
    public static boolean isSchoolApplication(Integer code) {
        ProductCategoryEnum category = parse(code);
        return category != null && category == SCHOOL_APPLICATION;
    }

    /**
     * 判断是否为本地服务商品
     *
     * @param code 商品分类代码
     * @return true表示是本地服务，false表示不是
     */
    public static boolean isLocalService(Integer code) {
        ProductCategoryEnum category = parse(code);
        return category != null && category == LOCAL_SERVICE;
    }

    /**
     * 获取所有商品分类代码
     *
     * @return 所有商品分类代码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(ProductCategoryEnum.values())
                .map(ProductCategoryEnum::getCode)
                .toArray(Integer[]::new);
    }

    /**
     * 获取所有商品分类名称
     *
     * @return 所有商品分类名称数组
     */
    public static String[] getAllLabels() {
        return Arrays.stream(ProductCategoryEnum.values())
                .map(ProductCategoryEnum::getLabel)
                .toArray(String[]::new);
    }
}
