#\u7AEF\u53E3\u548C\u8DEF\u5F84
server.port=9710
server.servlet.context-path=/immi-admin

# \u8DEF\u5F84\u5339\u914D\u6A21\u5F0F\uFF0C\u4F7F\u7528AntPathMatcher\u6A21\u5F0F\uFF0C\u56E0\u4E3Aswagger\u4E0D\u517C\u5BB9\u9ED8\u8BA4\u7684PathPatternParser\u6A21\u5F0F
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

#tomcat\u914D\u7F6E
##tomcat\u7684URI\u7F16\u7801
server.tomcat.uri-encoding=UTF-8
#tomcat\u6700\u5927\u7EBF\u7A0B\u6570\uFF0C\u9ED8\u8BA4\u4E3A200
server.tomcat.max-threads=800
#Tomcat\u542F\u52A8\u521D\u59CB\u5316\u7684\u7EBF\u7A0B\u6570\uFF0C\u9ED8\u8BA4\u503C25
server.tomcat.min-spare-threads=30
spring.application.name=immi-admin

# redis\u914D\u7F6E
spring.redis.host=**************
spring.redis.port=6379
spring.redis.password=cW4mF3xF3229kllJD7qE8aD9q19xH6uH5vF
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A15\uFF09
spring.redis.database=12
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.timeout=5000
# redis \u9A8C\u8BC1\u7801\u4FE1\u606F
#\u5931\u6548\u65F6\u95F4 3\u5206\u949F
spring.redis.key.expire.userAuthCode=1

#\u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.jedis.pool.max-active=100
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.jedis.pool.max-idle=10
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.jedis.pool.max-wait=100000
#\u6BCFms\u8FD0\u884C\u4E00\u6B21\u7A7A\u95F2\u8FDE\u63A5\u56DE\u6536\u5668\uFF08\u72EC\u7ACB\u7EBF\u7A0B\uFF09
spring.redis.jedis.pool.time-between-eviction-runs=1000

#\u6570\u636E\u5E93\u8FDE\u63A5
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=******************************************************************************************************************************************************************
spring.datasource.username=immi-staging
spring.datasource.password=iZRRSKWkWcRXWJPr112
#\u5F00\u542F\u6570\u636E\u5E93\u52A0\u5BC6
jasypt.encryptor.password=rR2rN4rS9I1fss9U7mQ1mE0kU

# token\u8FC7\u671F\u65F6\u95F4\uFF0C\u65F6\u95F4\u79D2\uFF08\u9ED8\u8BA4\u4E00\u4E2A\u6708\uFF09
token.expires=2592000

#knife4j\u5F00\u542F\u589E\u5F3A
knife4j.enable=true
#\u5F00\u542FSwagger\u7684Basic\u8BA4\u8BC1\u529F\u80FD,\u9ED8\u8BA4\u662Ffalse
#knife4j.basic.enable=false
# Basic\u8BA4\u8BC1\u7528\u6237\u540D
#knife4j.basic.username=developer
# Basic\u8BA4\u8BC1\u5BC6\u7801
#knife4j.basic.password=admin123456

# \u65E5\u5FD7\u76D1\u63A7\uFF0C\u4F7F\u7528slf4j \u8FDB\u884C\u65E5\u5FD7\u8F93\u51FA
spring.datasource.druid.filter.slf4j.enabled=true
spring.datasource.druid.filter.slf4j.statement-log-error-enabled=true
spring.datasource.druid.filter.slf4j.statement-create-after-log-enabled=false
spring.datasource.druid.filter.slf4j.statement-close-after-log-enabled=false
spring.datasource.druid.filter.slf4j.result-set-open-after-log-enabled=false
spring.datasource.druid.filter.slf4j.result-set-close-after-log-enabled=false

# \u56FD\u9645\u5316\u591A\u8BED\u8A00\u914D\u7F6E
spring.messages.encoding=utf-8
spring.messages.basename=static/i18n/messages

#cos\u6587\u4EF6\u524D\u7F00
cos.key.prefix=/bV21M9tF3oC8zK2t/immi-staging
cos.key.fileServiceUrlBase = http://**************:9715/immi-staging
immiConfig.domain.questionnaire = http://**************:9712
immiConfig.domain.officialWebsite = http://**************:9716
#\u65F6\u95F4\u8F6C\u6362
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
#\u6BCF\u4E2A\u6587\u4EF6\u7684\u914D\u7F6E\u6700\u5927\u4E3A10MB\uFF0C\u5355\u6B21\u8BF7\u6C42\u7684\u6587\u4EF6\u7684\u603B\u6570\u4E0D\u80FD\u5927\u4E8E100MB
#\u5982\u679C\u662F\u60F3\u8981\u4E0D\u9650\u5236\u6587\u4EF6\u4E0A\u4F20\u7684\u5927\u5C0F\uFF0C\u90A3\u4E48\u5C31\u628A\u4E24\u4E2A\u503C\u90FD\u8BBE\u7F6E\u4E3A-1\u5C31\u884C\u5566
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB

# ================================================================= #
# mybatis-plus
mybatis-plus.global-config.id-type=2
mybatis-plus.global-config.field-strategy=2
mybatis-plus.global-config.auto-set-db-type=true
mybatis-plus.global-config.db-column-underline=true
mybatis-plus.global-config.refresh-mapper=true
mybatis-plus.global-config.db-config.id-type=auto
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false

immi.env.current=test



#druid \u8FDE\u63A5\u6C60\u914D\u7F6E
#\u521D\u59CB\u5316\u65F6\u5EFA\u7ACB\u7269\u7406\u8FDE\u63A5\u7684\u4E2A\u6570
spring.datasource.druid.initial-size=10
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570
spring.datasource.druid.max-active=20
# \u8FDE\u63A5\u6C60\u6700\u5C0F\u8FDE\u63A5\u6570
spring.datasource.druid.min-idle=10
# \u83B7\u53D6\u8FDE\u63A5\u65F6\u6700\u5927\u7B49\u5F85\u65F6\u95F4\uFF0C\u5355\u4F4D\u6BEB\u79D2
spring.datasource.druid.max-wait=60000
# \u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.time-between-eviction-runs-millis=2000
#\u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.min-evictable-idle-time-millis=600000
spring.datasource.druid.max-evictable-idle-time-millis=900000
#\u7528\u6765\u6D4B\u8BD5\u8FDE\u63A5\u662F\u5426\u53EF\u7528\u7684SQL\u8BED\u53E5,\u9ED8\u8BA4\u503C\u6BCF\u79CD\u6570\u636E\u5E93\u90FD\u4E0D\u76F8\u540C,\u8FD9\u662Fmysql
spring.datasource.druid.validation-query=select 1
# \u5E94\u7528\u5411\u8FDE\u63A5\u6C60\u7533\u8BF7\u8FDE\u63A5\uFF0C\u5E76\u4E14testOnBorrow\u4E3Afalse\u65F6\uFF0C\u8FDE\u63A5\u6C60\u5C06\u4F1A\u5224\u65AD\u8FDE\u63A5\u662F\u5426\u5904\u4E8E\u7A7A\u95F2\u72B6\u6001\uFF0C\u5982\u679C\u662F\uFF0C\u5219\u9A8C\u8BC1\u8FD9\u6761\u8FDE\u63A5\u662F\u5426\u53EF\u7528
spring.datasource.druid.test-while-idle=true
# \u5982\u679C\u4E3Atrue\uFF0C\u9ED8\u8BA4\u662Ffalse\uFF0C\u5E94\u7528\u5411\u8FDE\u63A5\u6C60\u7533\u8BF7\u8FDE\u63A5\u65F6\uFF0C\u8FDE\u63A5\u6C60\u4F1A\u5224\u65AD\u8FD9\u6761\u8FDE\u63A5\u662F\u5426\u662F\u53EF\u7528\u7684
spring.datasource.druid.test-on-borrow=false
#\u5982\u679C\u4E3Atrue\uFF08\u9ED8\u8BA4false\uFF09\uFF0C\u5F53\u5E94\u7528\u4F7F\u7528\u5B8C\u8FDE\u63A5\uFF0C\u8FDE\u63A5\u6C60\u56DE\u6536\u8FDE\u63A5\u7684\u65F6\u5019\u4F1A\u5224\u65AD\u8BE5\u8FDE\u63A5\u662F\u5426\u8FD8\u53EF\u7528
spring.datasource.druid.test-on-return=false
#\u662F\u5426\u7F13\u5B58preparedStatement\uFF0C\u4E5F\u5C31\u662FPSCache\u3002PSCache\u5BF9\u652F\u6301\u6E38\u6807\u7684\u6570\u636E\u5E93\u6027\u80FD\u63D0\u5347\u5DE8\u5927\uFF0C\u6BD4\u5982\u8BF4oracle
spring.datasource.druid.pool-prepared-statements=true
# \u8981\u542F\u7528PSCache\uFF0C\u5FC5\u987B\u914D\u7F6E\u5927\u4E8E0\uFF0C\u5F53\u5927\u4E8E0\u65F6\uFF0C poolPreparedStatements\u81EA\u52A8\u89E6\u53D1\u4FEE\u6539\u4E3Atrue\uFF0C
# \u5728Druid\u4E2D\uFF0C\u4E0D\u4F1A\u5B58\u5728Oracle\u4E0BPSCache\u5360\u7528\u5185\u5B58\u8FC7\u591A\u7684\u95EE\u9898\uFF0C
# \u53EF\u4EE5\u628A\u8FD9\u4E2A\u6570\u503C\u914D\u7F6E\u5927\u4E00\u4E9B\uFF0C\u6BD4\u5982\u8BF4100
spring.datasource.druid.max-open-prepared-statements=20
# \u8FDE\u63A5\u6C60\u4E2D\u7684minIdle\u6570\u91CF\u4EE5\u5185\u7684\u8FDE\u63A5\uFF0C\u7A7A\u95F2\u65F6\u95F4\u8D85\u8FC7minEvictableIdleTimeMillis\uFF0C\u5219\u4F1A\u6267\u884CkeepAlive\u64CD\u4F5C
spring.datasource.druid.keepAlive=true
# Spring\u76D1\u63A7AOP\u5207\u5165\u70B9\uFF0C\u5982x.y.z.service.*,\u914D\u7F6E\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u9694
spring.datasource.druid.aop-patterns=com.awg.admin.*.controller.*
#\u914D\u7F6E\u76D1\u63A7\u7EDF\u8BA1\u62E6\u622A\u7684filters
#\u542F\u7528\u5185\u7F6E\u8FC7\u6EE4\u5668\uFF08\u7B2C\u4E00\u4E2A stat\u5FC5\u987B\uFF0C\u5426\u5219\u76D1\u63A7\u4E0D\u5230SQL\uFF09
spring.datasource.druid.filters=stat,wall,log4j2
# \u81EA\u5DF1\u914D\u7F6E\u76D1\u63A7\u7EDF\u8BA1\u62E6\u622A\u7684filter
#\u5F00\u542Fdruiddatasource\u7684\u72B6\u6001\u76D1\u63A7
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.db-type=mysql
# \u5F00\u542F\u6162sql\u76D1\u63A7\uFF0C\u8D85\u8FC72s \u5C31\u8BA4\u4E3A\u662F\u6162sql\uFF0C\u8BB0\u5F55\u5230\u65E5\u5FD7\u4E2D
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=2000
#\u8FDE\u63A5\u6CC4\u6F0F\u76D1\u6D4B-\u4E0A\u7EBF\u540E\u4E0D\u5EFA\u8BAE\u5F00\u542F
spring.datasource.dynamic.druid.remove-abandoned=true
#1800\u79D2\uFF0C\u4E5F\u5C31\u662F30\u5206\u949F
spring.datasource.dynamic.druid.remove-abandoned-timeout=1800
#\u5173\u95EDabanded\u8FDE\u63A5\u65F6\u8F93\u51FA\u9519\u8BEF\u65E5\u5FD7
spring.datasource.dynamic.druid.log-abandoned=true
#\u914D\u7F6EWebStatFilter\uFF0C\u7528\u4E8E\u91C7\u96C6web\u5173\u8054\u76D1\u63A7\u7684\u6570\u636E ##########
spring.datasource.druid.web-stat-filter.enabled=true
# \u8FC7\u6EE4\u6240\u6709url
spring.datasource.druid.web-stat-filter.url-pattern=/*
# \u6392\u9664\u4E00\u4E9B\u4E0D\u5FC5\u8981\u7684url
spring.datasource.druid.web-stat-filter.exclusions=/druid/*
spring.datasource.druid.web-stat-filter.session-stat-enable=true
# session\u7684\u6700\u5927\u4E2A\u6570,\u9ED8\u8BA4100
spring.datasource.druid.web-stat-filter.session-stat-max-count=1000
#druid \u76D1\u63A7\u914D\u7F6E
#StatViewServlet\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_StatViewServlet\u914D\u7F6E
#\u542F\u7528StatViewServlet[\u542F\u7528\u5185\u7F6E\u7684\u76D1\u63A7\u9875\u9762]
spring.datasource.druid.stat-view-servlet.enabled=true
#\u8BBF\u95EE\u5185\u7F6E\u76D1\u63A7\u9875\u9762\u7684\u8DEF\u5F84\uFF0C\u5185\u7F6E\u76D1\u63A7\u9875\u9762\u7684\u9996\u9875\u662F/druid/index.html
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
#\u5173\u95ED Reset All \u529F\u80FD[\u4E0D\u5141\u8BB8\u6E05\u7A7A\u7EDF\u8BA1\u6570\u636E,\u91CD\u65B0\u8BA1\u7B97]
spring.datasource.druid.stat-view-servlet.reset-enable=false
#\u8BBE\u7F6E\u767B\u5F55\u7528\u6237\u540D
spring.datasource.druid.stat-view-servlet.loginUsername=developerImmI
#\u8BBE\u7F6E\u767B\u5F55\u5BC6\u7801
spring.datasource.druid.stat-view-servlet.loginPassword=fN6V8H4TuV3jR4xMTnE8pB1006abY5
# \u5141\u8BB8\u8BBF\u95EE\u7684\u5730\u5740\uFF0C\u5982\u679Callow\u6CA1\u6709\u914D\u7F6E\u6216\u8005\u4E3A\u7A7A\uFF0C\u5219\u5141\u8BB8\u6240\u6709\u8BBF\u95EE
spring.datasource.druid.stat-view-servlet.allow=
# \u62D2\u7EDD\u8BBF\u95EE\u7684\u5730\u5740\uFF0Cdeny\u4F18\u5148\u4E8Eallow\uFF0C\u5982\u679C\u5728deny\u5217\u8868\u4E2D\uFF0C\u5C31\u7B97\u5728allow\u5217\u8868\u4E2D\uFF0C\u4E5F\u4F1A\u88AB\u62D2\u7EDD
spring.datasource.druid.stat-view-servlet.deny=

cert.path=/home/<USER>/cret/apiclient_cert_service.p12

#\u652F\u4ED8\u56DE\u8C03\u5730\u5740
pay.notify.url=https://api.test.dajiaochuguo.com/immi-admin/wechat/wxOrder/notify28302918203827162763

# \u7535\u5546\u5C0F\u7A0B\u5E8F\u914D\u7F6E
immiConfig.wxBase.weapp.merchantWxAppid=appid
immiConfig.wxBase.weapp.merchantWxSecret=secret

# \u6587\u4EF6COS key
immiFile.comm.material.watermark.key=/bV21M9tF3oC8zK2t/immi-staging/system/watermark/comm_material_watermark.png