package com.awg.common.utils;


import com.awg.common.exception.AssertUtils;
import com.awg.utils.json.JsonUtil;
import com.awg.utils.request.post.PostUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.awg.thirdparty.sdk.cos.TencentCOSUtils;
import com.awg.common.base.eo.ImageInfoEo;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * <b>FileBaseUtil</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023/8/10 16:14
 */

@Component
public class FileBaseUtil {
    private static String fileUrlBase;
    private static String cosKeyPrefix;

    @Value( "${cos.key.fileServiceUrlBase}" )
    public void setFileUrlBase(String fileUrlBase) {
        FileBaseUtil.fileUrlBase = fileUrlBase;
    }

    @Value( "${cos.key.prefix}" )
    public void setCosKeyPrefix(String cosKeyPrefix) {
        FileBaseUtil.cosKeyPrefix = cosKeyPrefix;
    }

    /**
     * 文件url拼上前缀
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static String getFileUrl(String url) {

        if(StringUtils.isBlank(url)) {
            return url;
        }

        // 如果URL已包含域名，则直接返回原URL
        if (url.indexOf("://") > 0) {
            return url;
        }

        return fileUrlBase + url;
    }

    /**
     * 获取cosPrefix
     *
     * @return
     * @throws Exception
     */
    public static String getCosKeyPrefix() {
        return cosKeyPrefix;
    }

    /**
     * 获取fileUrlBase
     *
     * @return
     * @throws Exception
     */
    public static String getFileUrlBase() {
        return fileUrlBase;
    }

    /**
     * 传入url，自动去掉域名部分，只保留路径部分，如果没有域名部分，则不去除
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static String getRelativeUrl(String url) {
        if (url == null) {
            return null;
        }
        int index = url.indexOf("://");
        if (index > 0) {
            url = url.substring(index + 3);

            index = url.indexOf("/");
            if (index > 0) {
                url = url.substring(index);
            }

            // 还得固定去掉第一个目录
            index = url.indexOf("/");
            if(index == 0) {
                url = url.substring(1);
            }

            index = url.indexOf("/");
            if (index > 0) {
                url = url.substring(index);
            }
        }

        return url;
    }

    /**
     * 删除文件
     *
     * @param path
     * @return
     * @throws Exception
     */
    public static void delFile(String path) {

        if(path == null || path.length() == 0) {
            AssertUtils.isTrue(true, "文件路径不能为空");
        }

        TencentCOSUtils.deleteObject(cosKeyPrefix+path);

        return;
    }

    /**
     * 获取腾讯云对象存储中的图片宽高信息
     *
     * @param url
     * @return
     */
    public static ImageInfoEo getCosImageDimension(String url) {
        try {
            // 使用getFileUrl处理传入的url
            String processedUrl = getFileUrl(url);
            // 添加?imageInfo后缀
            // 分割URL为路径和文件名部分
            int lastSlashIndex = processedUrl.lastIndexOf("/");
            String path = processedUrl.substring(0, lastSlashIndex + 1);
            String fileName = processedUrl.substring(lastSlashIndex + 1);
            // 仅编码文件名部分
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            String imageInfoUrl = path + encodedFileName + "?imageInfo";

            // 发送GET请求获取图片信息
            String resultJson = PostUtils.get(imageInfoUrl, new HashMap<>(1));
        

            @SuppressWarnings("unchecked")
            Map<String, Object> result = JsonUtil.jsonToObj(resultJson, Map.class);

            
            // 从结果中获取宽高信息
            Integer width = result.get("width") != null ? Integer.parseInt(result.get("width").toString()) : null;
            Integer height = result.get("height") != null ? Integer.parseInt(result.get("height").toString()) : null;

            // 如果宽或高为null，返回null
            if (width == null || height == null) {
                return null;
            }

            // 创建并返回ImageInfoEo对象
            ImageInfoEo imageInfo = new ImageInfoEo();
            imageInfo.setWidth(width);
            imageInfo.setHeight(height);

            return imageInfo;
        } catch (Exception e) {
            return null;
        }
    }
}