package com.awg.common.web;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.awg.common.security.AuthTokenInterceptor;
import com.awg.utils.date.DateUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * <p>
 * <b>JacksonConfig</b> is 返回数据为null时返回空字符串
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/16 11:29
 */

@Configuration
public class WebConfig extends WebMvcConfigurationSupport {

    /**
     * @param converters:
     * @description: 使用阿里 fastjson 作为JSON MessageConverter
     * @author: yang qiang
     * @date: 2021/3/16 22:13
     * @return: void
     **/
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
        FastJsonConfig config = new FastJsonConfig();
        // 时间格式化
        config.setDateFormat(DateUtils.DEFAULT_DATE_TIME_FORMAT_PATTERN);
        config.setSerializerFeatures(
                // 保留map空的字段
                SerializerFeature.WriteMapNullValue,
                // 将String类型的null转成""
                SerializerFeature.WriteNullStringAsEmpty,
                // 将Number类型的null转成0
                SerializerFeature.WriteNullNumberAsZero,
                // 将List类型的null转成[]
                SerializerFeature.WriteNullListAsEmpty,
                // 将Boolean类型的null转成false
                SerializerFeature.WriteNullBooleanAsFalse,
                // 避免循环引用
                SerializerFeature.DisableCircularReferenceDetect);
        converter.setFastJsonConfig(config);
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        // 补充缺失类型
        List<MediaType> mediaTypes = Arrays.asList(
                MediaType.APPLICATION_JSON,
                MediaType.TEXT_PLAIN,
                MediaType.TEXT_HTML,
                MediaType.TEXT_XML,
                MediaType.APPLICATION_OCTET_STREAM);
        converter.setSupportedMediaTypes(mediaTypes);
        converters.add(converter);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**", "/doc.html", "/webjars/**",
                                    "/swagger-resources/configuration/ui", "/swagger-resources/**", "/upload/**")
                .addResourceLocations("classpath:/static/", "classpath:/META-INF/resources/",
                                      "classpath:/META-INF/resources/webjars/");

    }

    /**
     * 默认解析器 其中locale表示默认语言
     * 默认为中文
     */
    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
        localeResolver.setDefaultLocale(Locale.CHINA);
        return localeResolver;
    }

    /**
     * 配置servlet处理，在srping boot 2.7中已经弃用，所以注释掉，可以使用addResourceHandlers来处理
     */
//    @Override
//    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
//        configurer.enable();
//    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //设置（模糊）匹配的url
        List<String> urlPatterns = new ArrayList<>();
        // addPathPatterns("/**") 表示拦截所有的请求，
        urlPatterns.add("/**");
        registry.addInterceptor(authTokenInterceptor())
                .addPathPatterns(urlPatterns)
                //不需要过滤的情况
                .excludePathPatterns(
                        "/doc.html",
                        "/webjars/**",
                        "/externalAPI/**",
                        "/swagger-resources/**",
                        "/swagger-resources/configuration/ui",
                        "/files/ueditor/exec");

        // 读取多语言环境
        LocaleChangeInterceptor localeInterceptor = new LocaleChangeInterceptor();
        localeInterceptor.setParamName("i18n-lang");  //拦截lang参数
        registry.addInterceptor(localeInterceptor);
    }

    /**
     * @description: 将拦截器作为bean写入配置中
     * @author: yang qiang
     * @date: 2021/3/16 15:05
     * @return: com.awg.common.security.AuthTokenInterceptor
     **/
    @Bean ( "newAuthTokenInterceptor" )
    public AuthTokenInterceptor authTokenInterceptor() {
        return new AuthTokenInterceptor();
    }

    /**
     * @description: 处理跨域问题
     * @author: yang qiang
     * @date: 2021/5/21 10:33
     * @return: org.springframework.web.filter.CorsFilter
     **/
    @Bean
    public CorsFilter corsFilter() {
        final UrlBasedCorsConfigurationSource urlBasedCorsConfigurationSource = new UrlBasedCorsConfigurationSource();
        final CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowCredentials(true);
        corsConfiguration.addAllowedOriginPattern("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        urlBasedCorsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
        return new CorsFilter(urlBasedCorsConfigurationSource);
    }

}