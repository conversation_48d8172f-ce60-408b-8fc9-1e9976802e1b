package com.awg.comm.service;

import com.awg.comm.dto.ProductInfoDto;
import com.awg.comm.entity.ProductSynchronization;
import com.awg.comm.vo.ProductSynchronizationCreateVo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品同步表 服务接口
 */
public interface IProductSynchronizationService extends IService<ProductSynchronization> {
    // 可根据业务需求添加自定义方法

    /**
     * 根据机构ID查询商品同步信息
     */
    ProductSynchronization getProductSynchronizationByOrgId(Long orgId);

    /**
     * 创建商品同步信息
     * @param productSynchronizationCreateVo
     * @param userLoginInfoEo
     * @return
     */
    void createProductSynchronization(ProductSynchronizationCreateVo productSynchronizationCreateVo, UserLoginInfoEo userLoginInfoEo);


    /**
     * 同步单个商品到指定机构
     * @param productNo 平台商品编号
     * @param orgId B端机构id
     * @param productSynchronization 商品同步信息
     * @param productInfoDto 平台商品详情
     */
    void syncSingleProductToOrg(String productNo, Integer orgId, ProductSynchronization productSynchronization, ProductInfoDto productInfoDto);


    /**
     * 开启机构关键字、地区、业务显示
     * @param orgId 机构id
     */
    void enableDisplayFlags(Integer orgId);



}
