package com.awg.crm.mapper;

import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.entity.CrmLeadsGovernmentScreenshot;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmLeadsGovernmentScreenshotMapper extends BaseMapper<CrmLeadsGovernmentScreenshot> {
    /**
     * 根据政府费id查询所有的图片文件（包含逻辑删除的数据）
     * @param crmLeadsGovernmentIds
     * @param createdAt
     * @return
     */
    List<CrmLeadsGovernmentScreenshot> listByCrmLeadsGovernmentIdsIncludeDeleted(@Param("crmLeadsGovernmentIds") List<Integer> crmLeadsGovernmentIds, @Param("createdAt") long createdAt);
}
