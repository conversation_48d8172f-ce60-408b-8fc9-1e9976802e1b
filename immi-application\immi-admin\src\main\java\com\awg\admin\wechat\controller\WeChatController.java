package com.awg.admin.wechat.controller;

import com.awg.admin.wechat.aes.WXBizMsgCrypt;
import com.awg.common.base.controller.BaseController;
import com.awg.common.redis.RedisUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.utils.io.IOUtil;
import com.awg.utils.json.JsonUtil;
import com.awg.utils.xml.XmlUtil;
import com.awg.wechat.constant.WechatConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 372 )
@Api( tags = {"微信相关接口"} )
@RestController
@RequestMapping( "/wechat/base" )
@Slf4j
public class WeChatController extends BaseController {

    @Resource
    private RedisUtils redisUtils;

    /**
     * <p>
     * 微信授权回调
     * </p>
     *
     * @author: 夜晓
     * @date: 2021/8/18
     * @return: void
     */
    @ApiIgnore
    @RequestMapping ( value = "/authorize/notify", method = {RequestMethod.GET, RequestMethod.POST} )
    @Authority( AuthorityEnum.NOCHECK )
    public String authorizeNotify(@RequestParam ( value = "msg_signature", required = false ) String msgSignature,
                                  @RequestParam ( value = "timestamp", required = false ) String timestamp,
                                  @RequestParam( value = "nonce", required = false ) String nonce) {
        System.out.println("========= authorize notify =========");

        Map<String, Object> data = decryptMsg(false, msgSignature, timestamp, nonce);

        try {
            System.out.println("=======================" + JsonUtil.objToJson(data) + "=======================");
            // 只保存componentVerifyTicket票据信息
            if (data.containsKey("ComponentVerifyTicket")) {
                // component_verify_ticket的过期时间是12小时，但这里不设置过期时间, 避免两边失效时间的不一致导致出现无法拿到component_verify_ticket的情况
                redisUtils.set(WechatConstant.WECHAT_TICKET, JsonUtil.objToJson(data));
            }
        } catch (JsonProcessingException e) {
            log.error("微信授权回调json转换错误：{0}", e.fillInStackTrace());
        }

        return "success";
    }

    /**
     * <p>
     * 微信回调内容解码
     * </p>
     *
     * @author: 夜晓
     * @date: 2021/8/18
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     */
    private Map<String, Object> decryptMsg(boolean isAwg, String msgSignature, String timestamp, String nonce) {
        Map<String, Object> result = new LinkedHashMap<>();
        if (StringUtils.isBlank(msgSignature) || StringUtils.isBlank(timestamp) || StringUtils.isBlank(nonce)) {
            return result;
        }

        try {
            String content = IOUtil.read(request.getInputStream());
            if (StringUtils.isBlank(content)) {
                return result;
            }

            WXBizMsgCrypt crypt;
            if (isAwg) {
                crypt = new WXBizMsgCrypt(WechatConstant.IMMI_CRM_WX_APP_ID, WechatConstant.IMMI_CRM_WX_TOKEN, WechatConstant.IMMI_CRM_ENCODING_AES_KEY);
            } else {
                crypt = new WXBizMsgCrypt();
            }

            // 解码xml信息
            String xmlText = crypt.decryptMsg(msgSignature, timestamp, nonce, content);
            result = XmlUtil.xmlToMap(xmlText);
        } catch (Exception e) {
            log.error("微信消息解码异常：{0}", e.fillInStackTrace());
        }

        return result;
    }

}
