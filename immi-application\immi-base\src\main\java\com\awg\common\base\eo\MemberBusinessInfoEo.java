package com.awg.common.base.eo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>MemberBusinessInfoEo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "B端用户信息数据")
public class MemberBusinessInfoEo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    private Integer memberId;

    /**
     * 会员编号
     */
    private Long memberNo;

    /**
     * bid
     */
    private Integer bid;

    /**
     * 姓名
     */
    private String name;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 公司名称
     */
    private String businessName;
}
