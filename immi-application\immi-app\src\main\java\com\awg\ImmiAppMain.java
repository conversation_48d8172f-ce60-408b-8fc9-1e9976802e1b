package com.awg;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;

/**
 * @description:
 * @author: yangqiang
 * @date: 2020/10/28 16:42
 * @version: V1.0
 **/

/**
 * 开启事务支持
 */
@EnableTransactionManagement
@Slf4j
@MapperScan ( "com.awg.*.*.mapper" )
@SpringBootApplication ( scanBasePackages = {"com.awg"} )
public class ImmiAppMain {
    public static void main(String[] args) throws Exception {
        ConfigurableApplicationContext application = SpringApplication.run(ImmiAppMain.class, args);
        Environment env = application.getEnvironment();
        log.info("\n----------------------------------------------------------\n\t" +
                         "Application '{}' is running! Access URLs:\n\t" +
                         "Doc: \thttp://{}:{}/immi-app/doc.html\n" +
                         "----------------------------------------------------------",
                 env.getProperty("spring.application.name"),
                 InetAddress.getLocalHost().getHostAddress(),
                 env.getProperty("server.port"),
                 InetAddress.getLocalHost().getHostAddress(),
                 env.getProperty("server.port"));
    }
}
