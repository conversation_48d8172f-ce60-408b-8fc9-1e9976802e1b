package com.awg.common.base.eo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>UserLoginInfoEo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "用户信息数据")
public class UserLoginInfoEo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业用户id
     */
    private Integer userId;

    /**
     * 用户信息id
     */
    private Integer userInfoId;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 机构类型
     */
    private Integer orgType;

    /**
     * 角色类型
     */
    private Integer roleType;

    /**
     * ip
     */
    private String ip;
}
