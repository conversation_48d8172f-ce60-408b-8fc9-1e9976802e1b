package com.awg.admin.op.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.op.dto.BannerDto;
import com.awg.op.service.IOpBannerService;
import com.awg.op.vo.AddBannerVo;
import com.awg.op.vo.BannerSortVo;
import com.awg.op.vo.QueryBannerVo;
import com.awg.op.vo.SwitchDisplayFlagVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 300 )
@Api( tags = {"运营-banner相关接口"} )
@RestController
@RequestMapping( "/op/banner" )
public class OpBannerController extends BaseController {

    @Resource
    private IOpBannerService opBannerService;

    @ApiOperation( "banner列表" )
    @PostMapping( "/list" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BannerDto.class )
    })
    public DataResult bannerList(@RequestBody QueryBannerVo vo){
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        BasePageResult<BannerDto> result = opBannerService.bannerList(vo);
        return renderSuccess(result);
    }

    @ApiOperation( "新建banner" )
    @PostMapping( "/add" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult addBanner(@RequestBody AddBannerVo vo){
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        return renderSuccess(opBannerService.addBanner(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "编辑banner" )
    @PostMapping( "/update" )
    @ApiOperationSupport(order = 40)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateBanner(@RequestBody AddBannerVo vo){
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        return renderSuccess(opBannerService.updateBanner(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "切换显示状态" )
    @PostMapping( "/switchDisplayFlag" )
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchDisplayFlagVo vo){
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        opBannerService.switchDisplayStatus(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 60)
    @ApiOperation( "banner排序" )
    @PostMapping( "/sort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortBanner(@RequestBody BannerSortVo vo) {
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        opBannerService.sortBanner(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "删除banner" )
    @PostMapping( "/del/{bannerNo}" )
    @ApiOperationSupport(order = 70)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult delBanner(@PathVariable( value = "bannerNo" ) String bannerNo){
        AssertUtils.isTrue(StringUtils.isBlank(bannerNo), "banner编号不能为空");
        opBannerService.deleteBanner(bannerNo, getUserLoginInfoEo());
        return renderSuccess();
    }
}
