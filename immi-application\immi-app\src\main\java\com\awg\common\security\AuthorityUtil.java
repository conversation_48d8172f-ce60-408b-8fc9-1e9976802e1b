package com.awg.common.security;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.constant.UserTokenConstant;
import com.awg.common.jwt.JWTUtils;
import com.awg.common.jwt.JwtConstant;
import com.awg.common.jwt.JwtProperties;
import com.awg.common.redis.RedisUtils;
import com.awg.system.externalService.IUserExternalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;

/**
 * <p>
 * <b>AuthorityUtil</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/10/25 15:09
 */

@Component
@Slf4j
public class AuthorityUtil {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private IUserExternalService userExternalService;


    /**
     * <p>
     * 权限鉴定，鉴权当前登录用户是否有接口权限
     * </p>
     *
     * @param accessToken : token
     * @param uri         : 请求接口uri
     * @author: 夜晓
     * @date: 2021-10-25
     * @return: boolean
     */
    public boolean authentication(String accessToken, String uri) {
        //验证token合法性
        DecodedJWT decodedJwt = JWTUtils.verify(accessToken);

        //token是否失效
        String memberId = decodedJwt.getClaim(JwtConstant.MEMBER_ID).asString();
        String timeKey = decodedJwt.getClaim(JwtConstant.TIME_KEY).asString();

        String hashKey = UserTokenConstant.APP_ONLINE_USER_PREFIX + memberId + ":" + timeKey;

        if (!redisUtils.exists(hashKey)) {
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
        }
        Map<Object, Object> userRedisMap = redisUtils.hGetAll(hashKey);
        if (!StringUtils.equals(String.valueOf(userRedisMap.getOrDefault(JwtProperties.TOKEN_KEY, "")), accessToken)) {
            //如果请求token与缓存token信息不一致，代表缓存已更新过，需要重新登录
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
        }

        return true;
    }

}
