package com.awg.crm.dto;

import com.awg.crm.entity.CrmLeadsGovernmentScreenshot;
import com.awg.crm.vo.CrmLeadsGovernmentScreenshotVo;
import com.awg.mybatis.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CrmLeadsGovernmentDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer leadsId;


    private String feeName;


    private BigDecimal actualAmount;


    private String currencyUnit;


    private List<CrmLeadsGovernmentScreenshot> screenshots;
}
