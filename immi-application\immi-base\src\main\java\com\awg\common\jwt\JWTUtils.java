package com.awg.common.jwt;


import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.InvalidClaimException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;

import java.util.Calendar;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 2020/9/9.
 */
public class JWTUtils {

    /**
     * 生产token
     */
    public static String getToken(Map<String, String> map, Integer expires) {
        JWTCreator.Builder builder = JWT.create();
        //payload
        map.forEach(builder::withClaim);

        Calendar instance = Calendar.getInstance();
        //设置过期时间
        instance.add(Calendar.SECOND, expires);
        //指定令牌的过期时间
        builder.withExpiresAt(instance.getTime());
        //签发者信息
        builder.withIssuer(JwtProperties.ISSUER);
        //签名
        return builder.sign(Algorithm.HMAC256(JwtProperties.SECRET));
    }

    /**
     * 验证token
     */
    public static DecodedJWT verify(String token) {
        //如果有任何验证异常，此处都会抛出异常
        DecodedJWT decodedJwt;
        try {
            decodedJwt = JWT.require(Algorithm.HMAC256(JwtProperties.SECRET)).build().verify(token);
        } catch (SignatureVerificationException e) {
            throw new BusinessException(BaseResponseCode.ILLEGAL_REQUEST.getCode(), "签名不一致");
        } catch (TokenExpiredException e) {
            //令牌过期
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH.getCode(), BaseResponseCode.LOGIN_AUTH.getMsg());
        } catch (AlgorithmMismatchException e) {
            throw new BusinessException(BaseResponseCode.ILLEGAL_REQUEST.getCode(), "算法不匹配");
        } catch (InvalidClaimException e) {
            throw new BusinessException(BaseResponseCode.ILLEGAL_REQUEST.getCode(), "失效的payload");
        } catch (Exception e) {
            //token无效
            throw new BusinessException(BaseResponseCode.ILLEGAL_REQUEST.getCode(), BaseResponseCode.ILLEGAL_REQUEST.getMsg());
        }
        return decodedJwt;
    }

}
