package com.awg.common.base.exception;


import java.util.Map;

/**
 * BusinessException
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2020年3月18日
 */
public class BusinessException extends RuntimeException {
    /**
     * 异常编号
     */
    private final int messageCode;

    /**
     * 对messageCode 异常信息进行补充说明
     */
    private final String detailMessage;

    public BusinessException(int messageCode, String message) {
        super(message);
        this.messageCode = messageCode;
        this.detailMessage = message;
    }

    public BusinessException(Map<String, Object> content) {
        super((String) content.get("msg"));
        this.messageCode = (Integer) content.get("code");
        this.detailMessage = (String) content.get("msg");
    }

    /**
     * 构造函数
     *
     * @param code 异常码
     */
    public BusinessException(BaseResponseCode code) {
        this(code.getCode(), code.getMsg());
    }

    public int getMessageCode() {
        return messageCode;
    }

    public String getDetailMessage() {
        return detailMessage;
    }
}
