package com.awg.admin.comm.controller;

import com.awg.bp.dto.BusinessChildDto;
import com.awg.bp.vo.BusinessListAllVo;
import com.awg.bp.vo.ChildBusinessVo;
import com.awg.comm.dto.DistrictDto;
import com.awg.comm.service.IDistrictInfoService;
import com.awg.comm.service.IProductService;
import com.awg.comm.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 222 )
@Api( tags = {"商品地区信息相关接口"} )
@RestController
@RequestMapping( "/comm/districtInfo" )
public class DistrictController extends BaseController {

    @Resource
    private IDistrictInfoService districtInfoService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取地区信息列表【全部】" )
    @PostMapping( "/listAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = DistrictDto.class )
    })
    public DataResult listAll(@RequestBody QueryDistrictVo vo) {
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        List<DistrictDto> result = districtInfoService.getDistrictListAll(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "添加" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addDistrictInfo(@RequestBody DistrictInfoVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(districtInfoService.addDistrictInfo(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "更新" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateDistrictInfo(@RequestBody DistrictInfoVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(districtInfoService.updateDistrictInfo(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除" )
    @PostMapping( "/delete/{districtId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult deleteDistrictInfo(@PathVariable( value = "districtId" ) Integer districtId) {
        AssertUtils.isTrue(districtId==null || districtId<=0, "请先传入id");
        districtInfoService.deleteDistrictInfo(districtId, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 55)
    @ApiOperation( "排序" )
    @PostMapping( "/sort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortDistrictInfo(@RequestBody DistrictSortVo vo) {
        ValidationUtils.validate(vo);
        districtInfoService.sortDistrictInfo(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 65)
    @ApiOperation( "显示/隐藏" )
    @PostMapping( "/displayChange" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult displayChange(@RequestBody DisplayChangeVo vo) {
        ValidationUtils.validate(vo);
        districtInfoService.displayChange(vo, getUserLoginInfoEo());
        return renderSuccess();
    }


}
