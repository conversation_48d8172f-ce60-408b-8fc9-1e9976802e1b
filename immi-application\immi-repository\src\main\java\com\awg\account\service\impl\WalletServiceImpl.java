package com.awg.account.service.impl;

import com.awg.account.dto.WalletInfoDto;
import com.awg.account.dto.WalletTransactionItemDto;
import com.awg.account.entity.Wallet;
import com.awg.account.enums.WalletActionCategoryEnum;
import com.awg.account.enums.WalletActionCodeEnum;
import com.awg.account.mapper.WalletMapper;
import com.awg.account.mapper.WalletTransactionCrossMapper;
import com.awg.account.mapper.WalletTransactionMapper;
import com.awg.account.service.IWalletBaseService;
import com.awg.account.service.IWalletService;
import com.awg.account.vo.AdjustBalanceVo;
import com.awg.account.vo.MemberWalletInfoVo;
import com.awg.account.vo.QueryWalletTransactionVo;
import com.awg.client.externalService.IMemberValidExternalService;
import com.awg.comm.dto.ProductItemDto;
import com.awg.comm.vo.QueryProductVo;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.exception.AssertUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 钱包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class WalletServiceImpl extends ServiceImpl<WalletMapper, Wallet> implements IWalletService {

    @Resource
    private IWalletBaseService walletBaseService;

    @Resource
    private WalletTransactionMapper walletTransactionMapper;

    @Resource
    private WalletTransactionCrossMapper walletTransactionCrossMapper;

    @Resource
    private IMemberValidExternalService memberValidExternalService;

    /**
     * <p>
     * 获取钱包信息B端
     * </p>
     *
     * @return:
     */
    @Override
    public WalletInfoDto getBusinessWalletInfo(String walletNo) {

        // 获取钱包
        Wallet wallet = walletBaseService.getWalletByWalletNo(walletNo);
        AssertUtils.isNull(wallet, "钱包不存在");

        return wallet2WalletInfo(wallet);

    }

    /**
     * <p>
     * 获取钱包信息(C端)
     * </p>
     *
     * @return:
     */
    @Override
    public WalletInfoDto getConsumerWalletInfo(String walletNo) {

        // 获取钱包
        Wallet wallet = walletBaseService.getWalletByWalletNo(walletNo);
        AssertUtils.isNull(wallet, "钱包不存在");

        return wallet2WalletInfo(wallet);

    }

    /**
     * <p>
     * 钱包对象转钱包信息
     * </p>
     *
     * @return:
     */
    private WalletInfoDto wallet2WalletInfo(Wallet wallet) {

        AssertUtils.isNull(wallet, "钱包不存在");

        WalletInfoDto walletInfoDto = new WalletInfoDto();
        walletInfoDto.setWalletNo(wallet.getWalletNo().toString());
        walletInfoDto.setBalance(wallet.getBalance());
        walletInfoDto.setAvailableBalance(wallet.getAvailableBalance());
        walletInfoDto.setPendingBalance(wallet.getPendingBalance());

        return walletInfoDto;
    }

    /**
     * <p>
     * 钱包交易流水列表(B端)
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public BasePageResult<WalletTransactionItemDto> businessWalletTransactionList(QueryWalletTransactionVo vo) {

        // 钱包编号必填
        AssertUtils.isTrue(StringUtils.isBlank(vo.getWalletNo()), "钱包编号不能为空");

        // 获取钱包
        Wallet wallet = walletBaseService.getWalletByWalletNo(vo.getWalletNo());
        AssertUtils.isNull(wallet, "钱包不存在");

        Page<WalletTransactionItemDto> page = new Page<>(vo.getPageNo(), vo.getPageSize());

        Page<WalletTransactionItemDto> result = walletTransactionCrossMapper.walletTransactionList(page, vo);

        // 循环填入操作类型名称和操作行为名称
        for (WalletTransactionItemDto item : result.getRecords()) {
            item.setActionCategoryLabel(WalletActionCategoryEnum.parse(item.getActionCategory()).getLabel());
            item.setActionLabel(WalletActionCodeEnum.parse(item.getActionCode()).getLabel());

            if(item.getActionCode().equals(401)) {
                if( item.getCommissionRecipientType()!=null && item.getCommissionRecipientType().equals(2) ) {
                    // 接受人是C端时，文案叫做返现
                    item.setActionLabel("订单返现");
                }
            }
        }

        return new BasePageResult<>(result.getTotal(), result.getRecords(), vo);
    }

    /**
     * <p>
     * 钱包交易流水列表(C端)
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public BasePageResult<WalletTransactionItemDto> consumerWalletTransactionList(QueryWalletTransactionVo vo) {

        // 钱包编号必填
        AssertUtils.isTrue(StringUtils.isBlank(vo.getWalletNo()), "钱包编号不能为空");

        // 获取钱包
        Wallet wallet = walletBaseService.getWalletByWalletNo(vo.getWalletNo());
        AssertUtils.isNull(wallet, "钱包不存在");

        Page<WalletTransactionItemDto> page = new Page<>(vo.getPageNo(), vo.getPageSize());

        Page<WalletTransactionItemDto> result = walletTransactionCrossMapper.walletTransactionList(page, vo);

        // 循环填入操作类型名称和操作行为名称
        for (WalletTransactionItemDto item : result.getRecords()) {
            item.setActionCategoryLabel(WalletActionCategoryEnum.parse(item.getActionCategory()).getLabel());
            item.setActionLabel(WalletActionCodeEnum.parse(item.getActionCode()).getLabel());
        }

        return new BasePageResult<>(result.getTotal(), result.getRecords(), vo);
    }

    /**
     * <p>
     * 手动调整钱包余额（B端）
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void adjustBusinessBalance(AdjustBalanceVo vo, UserLoginInfoEo userLoginInfoEo) {

        // 获取钱包
        Wallet wallet = walletBaseService.getWalletByWalletNo(vo.getWalletNo());
        AssertUtils.isNull(wallet, "钱包不存在");

        AssertUtils.isFalse(memberValidExternalService.checkMemberValid(wallet.getMemberId(), userLoginInfoEo.getOrgId()), "会员不存在");

        // 调整余额
        walletBaseService.adjustBalance(vo, userLoginInfoEo);
    }

    /**
     * <p>
     * 手动调整钱包余额（C端）
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void adjustConsumerBalance(AdjustBalanceVo vo, UserLoginInfoEo userLoginInfoEo) {

        // 获取钱包
        Wallet wallet = walletBaseService.getWalletByWalletNo(vo.getWalletNo());
        AssertUtils.isNull(wallet, "钱包不存在");

        AssertUtils.isFalse(memberValidExternalService.checkMemberValid(wallet.getMemberId(), userLoginInfoEo.getOrgId()), "会员不存在");

        // 调整余额
        walletBaseService.adjustBalance(vo, userLoginInfoEo);
    }

    /**
     * <p>
     * 获取会员钱包信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-05
     */
    @Transactional( rollbackFor = Exception.class )
    @Override
    public WalletInfoDto getMemberWalletInfo(MemberWalletInfoVo vo, MemberLoginInfoEo loginInfoEo){

        Integer type = vo.getType();
        Integer memberId = loginInfoEo.getMemberId();

        // 目前以C端身份为基准判断有效性即可
        Integer cid = memberValidExternalService.getCidByMemberId(memberId);
        AssertUtils.isNull(cid, "C端身份已失效");
        boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
        AssertUtils.isTrue(!validFlag, "C端身份不存在");

        // 获取钱包
        Wallet wallet = walletBaseService.getMemberWallet(memberId);
        return wallet2WalletInfo(wallet);
    }
}
