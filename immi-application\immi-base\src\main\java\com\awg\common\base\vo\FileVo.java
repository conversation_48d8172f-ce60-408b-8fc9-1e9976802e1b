package com.awg.common.base.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: lun
 * @date: 2023/09/12 18:12
 * @version: V1.0
 **/
@Data
public class FileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不可为空")
    private String name;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径", required = true)
    @NotBlank(message = "文件路径不可为空")
    private String path;

    /**
     * uid,前端自定义字段
     */
    @ApiModelProperty(value = "uid-前端自定义字段")
    private String uid;
}
