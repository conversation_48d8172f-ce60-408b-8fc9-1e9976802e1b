package com.awg.comm.service.impl;

import com.awg.bp.entity.Business;
import com.awg.bp.entity.NewBusiness;
import com.awg.bp.externalService.IBusinessExternalService;
import com.awg.bp.service.IBusinessService;
import com.awg.bp.service.NewBusinessService;
import com.awg.client.dto.BusinessMemberItemDto;
import com.awg.client.entity.Member;
import com.awg.client.vo.QueryBMemberVo;
import com.awg.client.vo.UpdateBusinessMemberVo;
import com.awg.comm.dto.*;
import com.awg.comm.entity.*;
import com.awg.comm.eo.ProductLogEo;
import com.awg.comm.externalService.IProductLogExternalService;
import com.awg.comm.mapper.*;
import com.awg.comm.service.*;
import com.awg.comm.vo.*;
import com.awg.common.base.eo.ImageInfoEo;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.enums.*;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileBaseUtil;
import com.awg.crm.dto.LeadsDto;
import com.awg.file.externalService.IWatermarkImageExternalService;
import com.awg.kbs.entity.KbsCategory;
import com.awg.mybatis.entity.BaseEntity;
import com.awg.ord.eo.CouponTemplateEo;
import com.awg.ord.externalService.ICouponExternalService;
import com.awg.plv.externalService.IPlvUploadRecordExternalService;
import com.awg.system.eo.UserMinEo;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.system.externalService.IUserExternalService;
import com.awg.system.externalService.IUserInfoExternalService;
import com.awg.utils.random.IdWorker;
import com.awg.utils.random.RandomString;
import com.awg.utils.url.UrlUtil;
import com.awg.website.dto.OfficeDto;
import com.awg.website.entity.WebsiteOffice;
import com.awg.website.entity.WebsiteProject;
import com.awg.website.vo.OfficeVo;
import com.awg.website.vo.SaveOfficeListVo;
import com.awg.website.vo.WebsiteSortVo;
import com.awg.wechat.externalService.IWeappExternalService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.container.ListChange;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @since 2024-04-11
 */
@Service
@Slf4j
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {


    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductDataMapper productDataMapper;

    @Resource
    private MaterialMapper materialMapper;

    @Resource
    private MaterialImageRelationMapper materialImageRelationMapper;

    @Resource
    private CopywriterUserMapper copywriterUserMapper;

    @Resource
    private DistrictInfoMapper districtInfoMapper;

    @Resource
    private CopywriterAssignmentRecordMapper copywriterAssignmentRecordMapper;

    @Resource
    private IBusinessExternalService businessExternalService;

    @Resource
    private ProductCrossMapper productCrossMapper;

    @Resource
    private MaterialImageMapper materialImageMapper;

    @Resource
    private ProductBannerMapper productBannerMapper;

    @Resource
    private ProductPdfMapper productPdfMapper;

    @Resource
    private MaterialGroupMapper materialGroupMapper;

    @Resource
    private ProductSecondaryCategoryMapper productSecondaryCategoryMapper;

    @Resource
    private ProductKeywordMapper productKeywordMapper;

    @Resource
    private IProductLogExternalService productLogExternalService;

    @Resource
    private IPlvUploadRecordExternalService polyvUploadRecordService;

    @Resource
    private IUserInfoExternalService userInfoExternalService;

    @Resource
    private IUserExternalService userExternalService;

    @Resource
    private IOrgExternalService orgExternalService;

    @Resource
    private ICouponExternalService couponExternalService;

    @Resource
    private ProductGiftCouponRelationMapper productGiftCouponRelationMapper;

    @Resource
    private ProductEditorRelationMapper productEditorRelationMapper;

    @Resource
    private IWeappExternalService weappExternalService;

    @Resource
    private IWatermarkImageExternalService watermarkImageExternalService;


    @Resource
    @Lazy
    IProductSynchronizationService iProductSynchronizationService;


    @Resource
    NewIProductService newIProductService;

    @Resource
    NewProductDataService newProductDataService;

    @Resource
    private Javers javers;



    /**
     * <p>
     * 获取商品列表
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public BasePageResult<ProductItemDto> getProductList(QueryProductVo vo, UserLoginInfoEo userLoginInfoEo, Integer category) {

        vo.setOrgId(userLoginInfoEo.getOrgId());

        List<Integer> keywordIdQueryList = new ArrayList<>();
        if (StringUtils.isNotBlank(vo.getKeywordIds())) {
            keywordIdQueryList = Arrays.asList(vo.getKeywordIds().split(",")).stream().map(Integer::parseInt).distinct().collect(Collectors.toList());
        }
        vo.setKeywordIdList(keywordIdQueryList);

        Page<ProductItemDto> page = new Page<>(vo.getPageNo(), vo.getPageSize());

        Page<ProductItemDto> result = productCrossMapper.getProductList(page, vo, category);

        for (ProductItemDto item : result.getRecords()) {
            List<Integer> keywordIdList = new ArrayList<>();
            List<Integer> keywordIdListNew = new ArrayList<>();
            List<ProductKeyword> productKeywordList = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getKeywordIds())) {
                keywordIdList = Arrays.asList(item.getKeywordIds().split(",")).stream().map(Integer::parseInt).distinct().collect(Collectors.toList());

                productKeywordList = productKeywordMapper.selectList(Wrappers.<ProductKeyword>lambdaQuery()
                        .eq(ProductKeyword::getIsDelete, TrueFalseEnum.FALSE.getCode())
                        .in(ProductKeyword::getId, keywordIdList)
                );
            }

            for (ProductKeyword productKeyword : productKeywordList) {
                keywordIdListNew.add(productKeyword.getId());
            }

            item.setKeywordList(productKeywordList);
            item.setKeywordIds(StringUtils.join(keywordIdListNew, ","));
            // 处理封面和校徽URL
            String cover = item.getCover();
            item.setCover(StringUtils.isNotBlank(cover) ? FileBaseUtil.getFileUrl(cover) : "");
            item.setSchoolLogo(StringUtils.isNotBlank(item.getSchoolLogo()) ? FileBaseUtil.getFileUrl(item.getSchoolLogo()) : "");
        }


        return new BasePageResult<>(result.getTotal(), result.getRecords(), vo);
    }

    @Override
    public Integer getOnlyProductEditorFlag(Integer userInfoId, String uri, Integer orgId) {
        boolean isProductEditorUri = userExternalService.authValidatorByUserInfoId(userInfoId, uri, orgId, false);
        Integer productEditorUserFlag = userExternalService.getUserProductEditorUserFlag(userInfoId);

        if (isProductEditorUri == false) {
            return 1;
        }

        return 0;
    }

    @Override
    public boolean checkProductPermission(Integer userInfoId, String uri, Integer orgId, String productNo) {
        boolean isProductEditorUri = userExternalService.authValidatorByUserInfoId(userInfoId, uri, orgId, false);

        ProductEditorRelation productEditorRelation = productEditorRelationMapper.selectOne(Wrappers.<ProductEditorRelation>lambdaQuery()
                .eq(ProductEditorRelation::getUserInfoId, userInfoId)
                .eq(ProductEditorRelation::getProductNo, productNo)
                .eq(ProductEditorRelation::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .last("limit 1")
        );

        if (isProductEditorUri || productEditorRelation != null) {
            return true;
        }

        return false;
    }

    /**
     * <p>
     * 获取商品选择列表
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public BasePageResult<ProductSelectDto> getProductSelectList(QueryProductVo vo, UserLoginInfoEo userLoginInfoEo) {
        Page<ProductSelectDto> page = new Page<>(vo.getPageNo(), vo.getPageSize());

        Page<ProductSelectDto> result = productCrossMapper.getProductSelectList(page, vo);

        return new BasePageResult<>(result.getTotal(), result.getRecords(), vo);
    }

    /**
     * <p>
     * 获取商品列表(迷你版，小程序端)
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public BasePageResult<ProductMinDto> getProductMiniList(QueryProductVo vo) {

        List<Integer> keywordIdQueryList = new ArrayList<>();
        if (StringUtils.isNotBlank(vo.getKeywordIds())) {
            keywordIdQueryList = Arrays.asList(vo.getKeywordIds().split(",")).stream().map(Integer::parseInt).distinct().collect(Collectors.toList());
        }
        vo.setKeywordIdList(keywordIdQueryList);

        Page<ProductMinDto> page = new Page<>(vo.getPageNo(), vo.getPageSize());

        Page<ProductMinDto> result = productCrossMapper.getProductMiniList(page, vo);

        for (ProductMinDto item : result.getRecords()) {
            List<Integer> keywordIdList = new ArrayList<>();
            List<Integer> keywordIdListNew = new ArrayList<>();
            List<ProductKeyword> productKeywordList = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getKeywordIds())) {
                keywordIdList = Arrays.asList(item.getKeywordIds().split(",")).stream().map(Integer::parseInt).distinct().collect(Collectors.toList());

                productKeywordList = productKeywordMapper.selectList(Wrappers.<ProductKeyword>lambdaQuery()
                        .eq(ProductKeyword::getIsDelete, TrueFalseEnum.FALSE.getCode())
                        .in(ProductKeyword::getId, keywordIdList)
                );
            }

            for (ProductKeyword productKeyword : productKeywordList) {
                keywordIdListNew.add(productKeyword.getId());
            }

            item.setKeywordList(productKeywordList);
            item.setKeywordIds(StringUtils.join(keywordIdListNew, ","));
            // 处理封面和校徽URL
            String cover = item.getCover();
            if (StringUtils.isBlank(cover)) {
                // 获取banner列表
                List<ProductBannerDto> productBannerList = productCrossMapper.getProductBannerDtoList(item.getProductNo(), item.getProductVid());
                if (productBannerList != null && !productBannerList.isEmpty()) {
                    ProductBannerDto firstBanner = productBannerList.get(0);
                    if (firstBanner.getType() == 0) { // 图片类型
                        cover = firstBanner.getPath();
                    } else if (firstBanner.getType() == 1) { // 视频类型
                        cover = firstBanner.getCoverUrl();
                    }

                    // 处理封面宽高
                    if (cover.contains(".videocc.net/")) {
                        item.setCoverWidth(firstBanner.getWidth());
                        item.setCoverHeight(firstBanner.getHeight());
                    }
                }
            }
            item.setCover(StringUtils.isNotBlank(cover) ? FileBaseUtil.getFileUrl(cover) : "");
            item.setSchoolLogo(StringUtils.isNotBlank(item.getSchoolLogo()) ? FileBaseUtil.getFileUrl(item.getSchoolLogo()) : "");
        }

        return new BasePageResult<>(result.getTotal(), result.getRecords(), vo);
    }

    /**
     * <p>
     * 获取商品详情-原始信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public ProductInfoDto getProductRawDetail(String productNo, UserLoginInfoEo userLoginInfoEo, ProductDetailVo productDetailVoR, boolean isLogin,
                                              Integer productVid, Integer category, Integer watermarkOrgId, boolean checkDelete) {
        // 编号不能为空
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        ProductDetailVo productDetailVo = productDetailVoR;
        if (productDetailVo == null) {
            productDetailVo = new ProductDetailVo();
            productDetailVo.setCouponProcessing(0);
            productDetailVo.setOrgId(0);
        }
        if (productDetailVo.getCouponProcessing() == null) {
            productDetailVo.setCouponProcessing(0);
        }

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(checkDelete, Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        // 要查找的商品版本
        Integer queryVid = productVid == null ? product.getProductVid() : productVid;

        // 准备返回对象
        ProductInfoDto productInfoDto = new ProductInfoDto();


        // 获取商品数据
        ProductData productData = null;
        if (category != null) {
            productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                    .eq(ProductData::getProductNo, productNo)
                    .eq(ProductData::getCategory, category)
                    .eq(ProductData::getProductVid, queryVid)
            );
        } else {
            productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                    .eq(ProductData::getProductNo, productNo)
                    .eq(ProductData::getProductVid, queryVid)
            );
        }

        AssertUtils.isNull(productData, "商品数据不存在");

        //庆
        //设置上一个版本id
        productInfoDto.setLastVid(productData.getLastVid());

        productInfoDto.setProductNo(product.getProductNo().toString());
        productInfoDto.setProductVid(queryVid);
        productInfoDto.setOrgId(product.getOrgId());
        productInfoDto.setCategory(productData.getCategory());
        productInfoDto.setEducationalStage(productData.getEducationalStage());
        // 处理地区信息筛选
        if (productDetailVo.getCouponProcessing() == 1 && userLoginInfoEo != null
                && productData.getDistrictId() != null && productData.getDistrictId() > 0) {
            Integer districtId = productData.getDistrictId();
            DistrictInfo districtInfo = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                    .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
                    .eq(DistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
                    .eq(DistrictInfo::getDisplayFlag, 1)
                    .and(wrapper -> wrapper.eq(DistrictInfo::getId, districtId)
                            .or().eq(DistrictInfo::getFromPlatformId, districtId)));
            if (districtInfo != null) {
                // 检查父地区是否存在
                if (districtInfo.getPid() > 0) {
                    Integer parentDistrictId = districtInfo.getPid();
                    DistrictInfo parentDistrict = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                            .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
                            .eq(DistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
                            .eq(DistrictInfo::getDisplayFlag, 1)
                            .and(wrapper -> wrapper.eq(DistrictInfo::getId, parentDistrictId)
                                    .or().eq(DistrictInfo::getFromPlatformId, parentDistrictId)));
                    if (parentDistrict == null) {
                        districtInfo = null;
                    }
                }

                if (districtInfo != null) {
                    productInfoDto.setDistrictId(productData.getDistrictId());
                    productInfoDto.setDistrictName(getProductDistrictName(productData.getDistrictId()));
                } else {
                    productInfoDto.setDistrictId(null);
                    productInfoDto.setDistrictName("");
                }
            } else {
                productInfoDto.setDistrictId(null);
                productInfoDto.setDistrictName("");
            }
        } else {
            productInfoDto.setDistrictId(productData.getDistrictId());
            productInfoDto.setDistrictName(getProductDistrictName(productData.getDistrictId()));
        }
        productInfoDto.setName(productData.getName());
        productInfoDto.setPrice(productData.getPrice());

        BigDecimal promoPrice = productData.getPromoPrice();
        if (promoPrice != null) {
            promoPrice = promoPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        BigDecimal secondDiscountPrice = productData.getSecondDiscountPrice();
        if (secondDiscountPrice != null) {
            secondDiscountPrice = secondDiscountPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        BigDecimal thirdDiscountPrice = productData.getThirdDiscountPrice();
        if (thirdDiscountPrice != null) {
            thirdDiscountPrice = thirdDiscountPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        productInfoDto.setPromoPrice(promoPrice != null ? promoPrice.toString() : "");
        productInfoDto.setSecondDiscountPrice(secondDiscountPrice != null ? secondDiscountPrice.toString() : "");
        // 设置封面和校徽URL
        productInfoDto.setCover(StringUtils.isNotBlank(productData.getCover()) ? FileBaseUtil.getFileUrl(productData.getCover()) : "");
        productInfoDto.setSchoolLogo(StringUtils.isNotBlank(productData.getSchoolLogo()) ? FileBaseUtil.getFileUrl(productData.getSchoolLogo()) : "");
        productInfoDto.setThirdDiscountPrice(thirdDiscountPrice != null ? thirdDiscountPrice.toString() : "");

        productInfoDto.setSourceNo(productData.getSourceNo().toString());
        productInfoDto.setSourceType(productData.getSourceType());
        productInfoDto.setDeliveryType(productData.getDeliveryType());
        productInfoDto.setPlatformDeliveryPrice(productData.getPlatformDeliveryPrice());

        productInfoDto.setSalesConsultationType(productData.getSalesConsultationType());
        productInfoDto.setPurchaseButtonText(productData.getPurchaseButtonText());
        productInfoDto.setPromotionButtonText(productData.getPromotionButtonText());
        productInfoDto.setConsultationButtonText(productData.getConsultationButtonText());
        productInfoDto.setConsultantWechat(productData.getConsultantWechat());
        productInfoDto.setConsultantQrcode(productData.getConsultantQrcode());


        productInfoDto.setDescription(productData.getDescription());
//        productInfoDto.setNotes(productData.getNotes());
        productInfoDto.setFeeDescription(productData.getFeeDescription());
        productInfoDto.setDiscountDescription(productData.getDiscountDescription());
        productInfoDto.setTermsOfService(productData.getTermsOfService());

        String relatedBusinessNo = productData.getRelatedBusinessNo();
        if (productDetailVo.getCouponProcessing() == 1 && userLoginInfoEo != null) {
            if (!businessExternalService.isBusinessValidInOrg(relatedBusinessNo, userLoginInfoEo.getOrgId())) {
                relatedBusinessNo = "";
            }
        }
        productInfoDto.setRelatedBusinessNo(relatedBusinessNo);

        String relatedChildBusinessNo = productData.getRelatedChildBusinessNo();
        if (productDetailVo.getCouponProcessing() == 1 && userLoginInfoEo != null) {
            if (!businessExternalService.isBusinessValidInOrg(relatedChildBusinessNo, userLoginInfoEo.getOrgId())) {
                relatedChildBusinessNo = "";
            }
        }
        productInfoDto.setRelatedChildBusinessNo(relatedChildBusinessNo);
        productInfoDto.setAvailabilityStatus(product.getAvailabilityStatus());

        productInfoDto.setMultipleDiscountFlag(productData.getMultipleDiscountFlag());
        productInfoDto.setCouponGiftFlag(productData.getCouponGiftFlag());

        // 处理商品描述-notes
        Long watermarkImageNo = orgExternalService.getWatermarkNo(watermarkOrgId);
        String description = productData.getNotes();
        List<String> descriptionUrls = extractImages(description);
        for (String descriptionUrl : descriptionUrls) {
            String dFileNo = extractImagesFileNo(descriptionUrl);
            if (StringUtils.isNotBlank(dFileNo)) {
                String watermarkedFilePath = watermarkImageExternalService.getWatermarkedFilePath(
                        watermarkImageNo, Long.valueOf(dFileNo), 0
                );
                description = description.replace(descriptionUrl, FileBaseUtil.getFileUrl(watermarkedFilePath));
            }
        }
        productInfoDto.setNotes(description);

        // 库存相关
        productInfoDto.setStock(product.getResettableStock());
        productInfoDto.setPromoStock(product.getPromoStock() == null ? "" : product.getPromoStock().toString());
        productInfoDto.setPromoOpenFlag(product.getPromoOpenFlag());
        productInfoDto.setStockOpenFlag(product.getStockOpenFlag());

        // 获取关联业务名称
        if (StringUtils.isNotBlank(productInfoDto.getRelatedBusinessNo())) {
            String relatedBusinessName = businessExternalService.getBusinessNameOrProcessNameByNo(productInfoDto.getRelatedBusinessNo(), null);
            productInfoDto.setRelatedBusinessName(relatedBusinessName);
        }

        // 获取关联子级业务名称
        if (StringUtils.isNotBlank(productInfoDto.getRelatedChildBusinessNo())) {
            String relatedChildBusinessName = businessExternalService.getBusinessNameOrProcessNameByNo(productInfoDto.getRelatedChildBusinessNo(), null);
            productInfoDto.setRelatedChildBusinessName(relatedChildBusinessName);
        }

        // 登录才显示材料[因前端登录后重新渲染详情遇到了问题，所以目前先固定返回]
        if (true) {
            // 获取必须材料和可选材料
            productInfoDto.setRequiredMaterialList(getMaterialList(
                    Long.valueOf(productNo), queryVid, 0, null, watermarkOrgId
            ));
            productInfoDto.setOptionalMaterialList(getMaterialList(
                    Long.valueOf(productNo), queryVid, 2, null, watermarkOrgId
            ));

            // 获取分组材料
            List<MaterialGroup> materialGroupList = materialGroupMapper.selectList(Wrappers.<MaterialGroup>lambdaQuery()
                    .eq(MaterialGroup::getProductNo, productNo)
                    .eq(MaterialGroup::getProductVid, queryVid)
                    .eq(MaterialGroup::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );

            List<MaterialGroupDto> materialGroupDtoList = new ArrayList<>();
            for (MaterialGroup materialGroup : materialGroupList) {
                MaterialGroupDto materialGroupDto = new MaterialGroupDto();
                materialGroupDto.setMaterialGroupNo(materialGroup.getMaterialGroupNo().toString());
                materialGroupDto.setGroupName(materialGroup.getGroupName());

                materialGroupDto.setMaterialList(getMaterialList(
                        Long.valueOf(productNo), queryVid, 1, materialGroup.getMaterialGroupNo(), watermarkOrgId
                ));
                materialGroupDtoList.add(materialGroupDto);
            }
            productInfoDto.setMaterialGroupList(materialGroupDtoList);
        }

        List<Integer> keywordIdList = new ArrayList<>();
        List<Integer> keywordIdListNew = new ArrayList<>();
        List<ProductKeyword> productKeywordList = new ArrayList<>();
        productInfoDto.setKeywordIds(productData.getKeywordIds());
        if (StringUtils.isNotBlank(productInfoDto.getKeywordIds())) {
            keywordIdList = Arrays.asList(productInfoDto.getKeywordIds().split(",")).stream().map(Integer::parseInt).distinct().collect(Collectors.toList());

            // 当couponProcessing为1时，排除当前机构下displayFlag为0的关键词
            if (productDetailVo.getCouponProcessing() == 1 && userLoginInfoEo != null) {
                List<Integer> finalKeywordIdList = keywordIdList;
                productKeywordList = productKeywordMapper.selectList(Wrappers.<ProductKeyword>lambdaQuery()
                        .eq(ProductKeyword::getIsDelete, TrueFalseEnum.FALSE.getCode())
                        .eq(ProductKeyword::getOrgId, userLoginInfoEo.getOrgId())
                        .eq(ProductKeyword::getDisplayFlag, 1)
                        .and(wrapper -> wrapper.in(ProductKeyword::getId, finalKeywordIdList)
                                .or().in(ProductKeyword::getFromPlatformId, finalKeywordIdList))
                );
            } else {
                productKeywordList = productKeywordMapper.selectList(Wrappers.<ProductKeyword>lambdaQuery()
                        .eq(ProductKeyword::getIsDelete, TrueFalseEnum.FALSE.getCode())
                        .in(ProductKeyword::getId, keywordIdList)
                );
            }
        }

        for (ProductKeyword productKeyword : productKeywordList) {
            keywordIdListNew.add(productKeyword.getFromPlatformId() > 0 ? productKeyword.getFromPlatformId() : productKeyword.getId());
        }

        productInfoDto.setKeywordList(productKeywordList);
        productInfoDto.setKeywordIds(StringUtils.join(keywordIdListNew, ","));

        // 处理二级分类筛选
        if (productDetailVo.getCouponProcessing() == 1 && userLoginInfoEo != null && productData.getSecondaryCategory() > 0) {
            Integer secondaryCategoryNo = productData.getSecondaryCategory();
            ProductSecondaryCategory secondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                    .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
                    .eq(ProductSecondaryCategory::getOrgId, userLoginInfoEo.getOrgId())
                    .eq(ProductSecondaryCategory::getDisplayFlag, 1)
                    .and(wrapper -> wrapper.eq(ProductSecondaryCategory::getId, secondaryCategoryNo)
                            .or().eq(ProductSecondaryCategory::getFromPlatformId, secondaryCategoryNo)));
            if (secondaryCategory != null) {
                productInfoDto.setSecondaryCategory(secondaryCategoryNo);
                productInfoDto.setSecondaryCategoryName(secondaryCategory.getName());
            } else {
                productInfoDto.setSecondaryCategory(0);
                productInfoDto.setSecondaryCategoryName("");
            }
        } else {
            productInfoDto.setSecondaryCategory(productData.getSecondaryCategory());
            if (productData.getSecondaryCategory() > 0) {
                ProductSecondaryCategory productSecondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                        .eq(ProductSecondaryCategory::getId, productData.getSecondaryCategory())
                        .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
                );
                productInfoDto.setSecondaryCategoryName(productSecondaryCategory == null ? "" : productSecondaryCategory.getName());
            }
        }

        // 获取文案人员列表
        List<CopywriterUser> copywriterUserList = copywriterUserMapper.selectList(Wrappers.<CopywriterUser>lambdaQuery()
                .eq(CopywriterUser::getProductNo, productNo)
                .eq(CopywriterUser::getProductVid, queryVid)
                .eq(CopywriterUser::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        List<Integer> copywriterUserIdList = copywriterUserList.stream().map(CopywriterUser::getUserInfoId).collect(Collectors.toList());
        productInfoDto.setCopywriterList(copywriterUserIdList);

        List<ProductBannerDto> productBannerList = productCrossMapper.getProductBannerDtoList(productNo, queryVid);
        for (ProductBannerDto productBannerDto : productBannerList) {
            productBannerDto.setPath(FileBaseUtil.getFileUrl(productBannerDto.getPath()));
        }
        productInfoDto.setProductBannerList(productBannerList);

        List<ProductPdf> productPdfList = productPdfMapper.selectList(Wrappers.<ProductPdf>lambdaQuery()
                .eq(ProductPdf::getProductNo, productNo)
                .eq(ProductPdf::getProductVid, queryVid)
                .eq(ProductPdf::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        List<ProductPdfDto> productPdfDtoList = new ArrayList<>();
        for (ProductPdf productPdf : productPdfList) {
            ProductPdfDto productPdfDto = new ProductPdfDto();
            productPdfDto.setName(productPdf.getName());
            productPdfDto.setFileUrl(FileBaseUtil.getFileUrl(productPdf.getPath()));

            productPdfDtoList.add(productPdfDto);
        }
        productInfoDto.setPdfList(productPdfDtoList);

        // 获取赠送优惠券列表
        List<ProductGiftCouponRelation> productGiftCouponRelations = productGiftCouponRelationMapper.selectList(Wrappers.<ProductGiftCouponRelation>lambdaQuery()
                .eq(ProductGiftCouponRelation::getProductNo, productNo)
                .eq(ProductGiftCouponRelation::getProductVid, queryVid)
                .eq(ProductGiftCouponRelation::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        List<CouponTemplateEo> couponList = new ArrayList<>();
        for (ProductGiftCouponRelation productGiftCouponRelation : productGiftCouponRelations) {
            CouponTemplateEo couponTemplateEo = couponExternalService.getCouponTemplateEo(
                    productGiftCouponRelation.getCouponTemplateNo().toString(), productDetailVo.getCouponProcessing(), productDetailVo.getOrgId());
            couponList.add(couponTemplateEo);
        }
        productInfoDto.setCouponList(couponList);

//        // 获取B端佣金信息
//        CommissionInfo bCommissionInfo = commissionInfoMapper.selectOne(Wrappers.<CommissionInfo>lambdaQuery()
//                .eq(CommissionInfo::getProductNo, productNo)
//                .eq(CommissionInfo::getProductVid, queryVid)
//                .eq(CommissionInfo::getType, 1)
//                .eq(CommissionInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
//        );
//        AssertUtils.isNull(bCommissionInfo, "数据异常，请联系管理员");
//        CommissionDto bCommissionDto = new CommissionDto();
//        bCommissionDto.setMethod(bCommissionInfo.getMethod());
//        bCommissionDto.setCommissionValue(bCommissionInfo.getCommissionValue());
//        productInfoDto.setBCommission(bCommissionDto);

//        // 获取C端佣金信息
//        CommissionInfo cCommissionInfo = commissionInfoMapper.selectOne(Wrappers.<CommissionInfo>lambdaQuery()
//                .eq(CommissionInfo::getProductNo, productNo)
//                .eq(CommissionInfo::getProductVid, queryVid)
//                .eq(CommissionInfo::getType, 2)
//                .eq(CommissionInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
//        );
//        AssertUtils.isNull(cCommissionInfo, "数据异常，请联系管理员");
//        CommissionDto cCommissionDto = new CommissionDto();
//        cCommissionDto.setMethod(cCommissionInfo.getMethod());
//        cCommissionDto.setCommissionValue(cCommissionInfo.getCommissionValue());
//        productInfoDto.setCCommission(cCommissionDto);

        return productInfoDto;
    }

    /**
     * <p>
     * 获取商品详情
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public ProductInfoDto getProductDetail(String productNo, UserLoginInfoEo userLoginInfoEo, ProductDetailVo productDetailVo, boolean isLogin,
                                           Integer productVid, Integer category, boolean checkDelete) {
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(checkDelete, Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");


        Integer watermarkOrgId = userLoginInfoEo == null ? product.getOrgId() : userLoginInfoEo.getOrgId();

        ProductInfoDto productInfoDto = getProductRawDetail(productNo, userLoginInfoEo, productDetailVo, isLogin, productVid, category, watermarkOrgId, checkDelete);

        if (
                StringUtils.isNotBlank(productInfoDto.getSourceNo())
                        && Long.valueOf(productInfoDto.getSourceNo()) > 0
                        && Long.valueOf(productInfoDto.getDeliveryType()) < 2
        ) {

            // 找出商品信息
            Product platformProduct = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                            .eq(Product::getProductNo, productInfoDto.getSourceNo())
                            .eq(Product::getOrgId, 1)
//                    .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(platformProduct, "平台商品不存在");

            ProductInfoDto platformProductInfo =
                    getProductRawDetail(productInfoDto.getSourceNo(), userLoginInfoEo, productDetailVo, isLogin,
                            platformProduct.getProductVid(), platformProduct.getCategory(), watermarkOrgId, false);

            productInfoDto.setSecondaryCategory(platformProductInfo.getSecondaryCategory());
            productInfoDto.setSecondaryCategoryName(platformProductInfo.getSecondaryCategoryName());
            productInfoDto.setEducationalStage(platformProductInfo.getEducationalStage());
            productInfoDto.setDistrictId(platformProductInfo.getDistrictId());
            productInfoDto.setDistrictName(platformProductInfo.getDistrictName());

            productInfoDto.setName(platformProductInfo.getName());
            productInfoDto.setDescription(platformProductInfo.getDescription());
            productInfoDto.setPlatformDeliveryPrice(platformProductInfo.getPlatformDeliveryPrice());

            productInfoDto.setKeywordIds(platformProductInfo.getKeywordIds());
            productInfoDto.setKeywordList(platformProductInfo.getKeywordList());
            productInfoDto.setRelatedBusinessNo(platformProductInfo.getRelatedBusinessNo());
            productInfoDto.setRelatedBusinessName(platformProductInfo.getRelatedBusinessName());
            productInfoDto.setRelatedChildBusinessNo(platformProductInfo.getRelatedChildBusinessNo());
            productInfoDto.setRelatedChildBusinessName(platformProductInfo.getRelatedChildBusinessName());

            productInfoDto.setNotes(platformProductInfo.getNotes());
            productInfoDto.setFeeDescription(platformProductInfo.getFeeDescription());
//            productInfoDto.setDiscountDescription(platformProductInfo.getDiscountDescription());
            productInfoDto.setTermsOfService(platformProductInfo.getTermsOfService());

            productInfoDto.setRequiredMaterialList(platformProductInfo.getRequiredMaterialList());
            productInfoDto.setOptionalMaterialList(platformProductInfo.getOptionalMaterialList());
            productInfoDto.setMaterialGroupList(platformProductInfo.getMaterialGroupList());

//            productInfoDto.setPurchaseButtonText(platformProductInfo.getPurchaseButtonText());
//            productInfoDto.setPromotionButtonText(platformProductInfo.getPromotionButtonText());
//            productInfoDto.setSalesConsultationType(platformProductInfo.getSalesConsultationType());
//            productInfoDto.setConsultationButtonText(platformProductInfo.getConsultationButtonText());
//            productInfoDto.setConsultantWechat(platformProductInfo.getConsultantWechat());
//            productInfoDto.setConsultantQrcode(platformProductInfo.getConsultantQrcode());

            if (productInfoDto.getDeliveryType().equals(1)) {
                productInfoDto.setCopywriterList(platformProductInfo.getCopywriterList());
            }

            productInfoDto.setProductBannerList(platformProductInfo.getProductBannerList());
            productInfoDto.setPdfList(platformProductInfo.getPdfList());
            // 传递封面和校徽信息
            productInfoDto.setCover(platformProductInfo.getCover());
            productInfoDto.setSchoolLogo(platformProductInfo.getSchoolLogo());
        }

        return productInfoDto;
    }

    @Override
    public ProductCompareResultVo getProductAndPlatformDetail(String productNo, UserLoginInfoEo userLoginInfoEo) {
        // 1. 参数验证
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        try {
            // 2. 获取B端商品详情
            ProductInfoDto bProductDetail = getProductDetail(productNo, null, null, true, null, null, true);
            AssertUtils.isNull(bProductDetail, "B端商品不存在");

            String sourceNo = bProductDetail.getSourceNo();
            AssertUtils.isTrue(StringUtils.isBlank(sourceNo), "商品未关联平台商品");


            // 查询平台商品获取platform_sync_vid
            NewProduct platformProduct = newIProductService.getOne(Wrappers.<NewProduct>lambdaQuery()
                    .eq(NewProduct::getProductNo, sourceNo)
            );
            Integer platformSyncVid = platformProduct != null ? platformProduct.getPlatformSyncVid() : null;

            // 3. 获取平台商品详情（当前版本和上一版本）
            UserLoginInfoEo userinfo = new UserLoginInfoEo();
            userinfo.setOrgId(bProductDetail.getOrgId());
            ProductInfoDto currentPlatformDetail = getProductDetail(sourceNo, userinfo, null, true, platformSyncVid, null, true);
            AssertUtils.isNull(currentPlatformDetail, "平台商品不存在");

            ProductInfoDto lastPlatformDetail = null;
            if (currentPlatformDetail.getLastVid() != null) {
                lastPlatformDetail = getProductDetail(sourceNo, userinfo, null, true, currentPlatformDetail.getLastVid(), null, true);
            }

            // 4. 创建对比实体
            CompareProductEntity currentPlatformEntity = new CompareProductEntity();
            CompareProductEntity lastPlatformEntity = new CompareProductEntity();

            BeanUtils.copyProperties(currentPlatformDetail, currentPlatformEntity);
            if (lastPlatformDetail != null) {
                BeanUtils.copyProperties(lastPlatformDetail, lastPlatformEntity);
            }

            // 5. 使用单例或缓存的 Javers 实例
//            Javers javers = getJaversInstance();
            Diff diff = javers.compare(currentPlatformEntity, lastPlatformEntity);

            // 6. 构建返回结果
            ProductCompareResultVo result = buildCompareResult(currentPlatformDetail, diff);

            return result;

        } catch (Exception e) {
            log.error("商品对比失败，商品编号: {}", productNo, e);
            throw new BusinessException(0, "商品对比失败: " + e.getMessage());
        }
    }

    /**
     * 获取 Javers 实例（建议使用单例或Spring Bean）
     */
    private Javers getJaversInstance() {
        // 可以考虑使用单例模式或Spring Bean管理
        return JaversBuilder.javers().build();
    }

    /**
     * 构建对比结果
     */
    private ProductCompareResultVo buildCompareResult(ProductInfoDto platformDetail, Diff diff) {
        ProductCompareResultVo result = new ProductCompareResultVo();
        result.setProductNo(platformDetail.getProductNo());
        result.setHasDifferences(diff.hasChanges());

        // 初始化所有字段，默认无差异
        initializeAllFields(result, platformDetail);

        // 处理差异字段
        if (diff.hasChanges()) {
            log.info("发现商品差异，商品编号: {}", platformDetail.getProductNo());
            Set<String> changedFields = extractChangedFields(diff);
            updateChangedFields(result, changedFields);
        } else {
            log.info("商品无差异，商品编号: {}", platformDetail.getProductNo());
        }

        return result;
    }

    /**
     * 提取变更字段
     */
//    private Set<String> extractChangedFields(Diff diff) {
//        Set<String> changedFields = new HashSet<>();
//
//        diff.getChanges().forEach(change -> {
//            String fieldName = null;
//            if (change instanceof PropertyChange) {
//                fieldName = ((PropertyChange) change).getPropertyName();
//                log.info("字段 {} 有差异", fieldName);
//            } else if (change instanceof ListChange) {
//                fieldName = ((ListChange) change).getPropertyName();
//                log.info("集合字段 {} 有差异", fieldName);
//            }
//
//            if (StringUtils.isNotBlank(fieldName)) {
//                changedFields.add(fieldName);
//            }
//
//            //额外操作
//            Optional<Object> affectedObject = change.getAffectedObject();
//            if(affectedObject!=null){
//
//            }
//        });
//
//        return changedFields;
//    }
//    private Set<String> extractChangedFields(Diff diff) {
//        Set<String> changedFields = new HashSet<>();
//
//        diff.getChanges().forEach(change -> {
//            String fieldName = null;
//
//            // 1. 普通属性变更
//            if (change instanceof org.javers.core.diff.changetype.PropertyChange) {
//                fieldName = ((org.javers.core.diff.changetype.PropertyChange) change).getPropertyName();
//                changedFields.add(fieldName);
//
//                // 处理嵌套集合：提取GlobalId路径的第一个字段
//                String globalId = change.getAffectedGlobalId().toString();
//                String[] parts = globalId.split("/");
//                if (parts.length > 1) {
//                    String outerField = parts[1];
//                    // 只取#后第一个字段，遇到/就截断
//                    int idx = outerField.indexOf('#');
//                    if (idx != -1 && idx + 1 < outerField.length()) {
//                        String afterHash = outerField.substring(idx + 1);
//                        int slashIdx = afterHash.indexOf('/');
//                        String fieldAfterHash = (slashIdx != -1) ? afterHash.substring(0, slashIdx) : afterHash;
//                        changedFields.add(fieldAfterHash);
//                    }
//                }
//            }
//            // 2. 集合本身变更（如增删元素）
//            else if (change instanceof org.javers.core.diff.changetype.container.ContainerChange) {
//                fieldName = ((org.javers.core.diff.changetype.container.ContainerChange) change).getPropertyName();
//                changedFields.add(fieldName);
//            }
//        });
//
//        return changedFields;
//    }

//    private Set<String> extractChangedFields(Diff diff) {
//        Set<String> changedFields = new HashSet<>();
//
//        diff.getChanges().forEach(change -> {
//            // 1. 普通属性变更
//            if (change instanceof org.javers.core.diff.changetype.PropertyChange) {
//                String fieldName = ((org.javers.core.diff.changetype.PropertyChange) change).getPropertyName();
//
//                String globalId = change.getAffectedGlobalId().toString();
//                // 只标记顶层字段（如 description），不标记集合内 description
//                if (!globalId.contains("/")) {
//                    // 只加顶层字段
//                    changedFields.add(fieldName);
//                }
//
//                // 处理嵌套集合：提取#后第一个字段，遇到/就截断
//                String[] parts = globalId.split("/");
//                if (parts.length > 1) {
//                    String outerField = parts[1];
//                    int idx = outerField.indexOf('#');
//                    if (idx != -1 && idx + 1 < outerField.length()) {
//                        String afterHash = outerField.substring(idx + 1);
//                        int slashIdx = afterHash.indexOf('/');
//                        String fieldAfterHash = (slashIdx != -1) ? afterHash.substring(0, slashIdx) : afterHash;
//                        changedFields.add(fieldAfterHash);
//                    }
//                }
//            }
//            // 2. 集合本身变更（如增删元素）
//            else if (change instanceof org.javers.core.diff.changetype.container.ContainerChange) {
//                String fieldName = ((org.javers.core.diff.changetype.container.ContainerChange) change).getPropertyName();
//                changedFields.add(fieldName);
//            }
//        });
//
//        return changedFields;
//    }
    private Set<String> extractChangedFields(Diff diff) {
        Set<String> changedFields = new HashSet<>();

        diff.getChanges().forEach(change -> {
            if (change instanceof org.javers.core.diff.changetype.PropertyChange) {
                String fieldName = ((org.javers.core.diff.changetype.PropertyChange) change).getPropertyName();
                String globalId = change.getAffectedGlobalId().toString();

                // 判断#后面有没有/，有则是集合/嵌套字段，否则是顶层字段
                int hashIdx = globalId.indexOf('#');
                String afterHash = (hashIdx != -1 && hashIdx + 1 < globalId.length()) ? globalId.substring(hashIdx + 1) : "";
                boolean isNested = afterHash.contains("/");

                if (!isNested) {
                    // 只加顶层字段
                    changedFields.add(fieldName);
                }

                // 处理嵌套集合：提取#后第一个字段，遇到/就截断
                if (!afterHash.isEmpty()) {
                    int slashIdx = afterHash.indexOf('/');
                    String fieldAfterHash = (slashIdx != -1) ? afterHash.substring(0, slashIdx) : afterHash;
                    changedFields.add(fieldAfterHash);
                }
            }
            // 集合本身变更
            else if (change instanceof org.javers.core.diff.changetype.container.ContainerChange) {
                String fieldName = ((org.javers.core.diff.changetype.container.ContainerChange) change).getPropertyName();
                changedFields.add(fieldName);
            }
        });

        return changedFields;
    }

    /**
     * 初始化所有字段，默认无差异
     */
    private void initializeAllFields(ProductCompareResultVo result, ProductInfoDto bProduct) {
        // 分类（1=签证类，2=申校类）
        result.setCategory(createFieldWrapper(bProduct.getCategory(), false));

        // 二级分类id
        result.setSecondaryCategory(createFieldWrapper(bProduct.getSecondaryCategory(), false));

        // 二级分类名称
        result.setSecondaryCategoryName(createFieldWrapper(bProduct.getSecondaryCategoryName(), false));

        // 教育阶段（1=中小学，2=college，3=本科，4=研究生）
        result.setEducationalStage(createFieldWrapper(bProduct.getEducationalStage(), false));

        // 地区id
        result.setDistrictId(createFieldWrapper(bProduct.getDistrictId(), false));

        // 地区名称
        result.setDistrictName(createFieldWrapper(bProduct.getDistrictName(), false));

        // 学校logo
        result.setSchoolLogo(createFieldWrapper(bProduct.getSchoolLogo(), false));

        // 商品封面
        result.setCover(createFieldWrapper(bProduct.getCover(), false));

        // 商品名称
        result.setName(createFieldWrapper(bProduct.getName(), false));

        // 商品描述
        result.setDescription(createFieldWrapper(bProduct.getDescription(), false));

        // 平台交付价格，0表示免费
        result.setPlatformDeliveryPrice(createFieldWrapper(bProduct.getPlatformDeliveryPrice(), false));

        // 交付类型，0=自己交付，1=平台交付
        result.setDeliveryType(createFieldWrapper(bProduct.getDeliveryType(), false));

        // 关键词id列表
        result.setKeywordIds(createFieldWrapper(bProduct.getKeywordIds(), false));

        // 关键词列表
        result.setKeywordList(createFieldWrapper(bProduct.getKeywordList(), false));

        // 关联项目(业务)
        result.setRelatedBusinessNo(createFieldWrapper(bProduct.getRelatedBusinessNo(), false));

        // 关联项目(业务)名称
        result.setRelatedBusinessName(createFieldWrapper(bProduct.getRelatedBusinessName(), false));

        // 关联子级项目(业务)编号
        result.setRelatedChildBusinessNo(createFieldWrapper(bProduct.getRelatedChildBusinessNo(), false));

        // 关联子级项目(业务)名称
        result.setRelatedChildBusinessName(createFieldWrapper(bProduct.getRelatedChildBusinessName(), false));

        // 注意事项
        result.setNotes(createFieldWrapper(bProduct.getNotes(), false));

        // 商品简介
        result.setFeeDescription(createFieldWrapper(bProduct.getFeeDescription(), false));

        // 服务条款
        result.setTermsOfService(createFieldWrapper(bProduct.getTermsOfService(), false));

        // 价格，0表示免费
        result.setPrice(createFieldWrapper(bProduct.getPrice(), false));

        // 必须材料列表
        result.setRequiredMaterialList(createFieldWrapper(bProduct.getRequiredMaterialList(), false));

        // 可选材料列表
        result.setOptionalMaterialList(createFieldWrapper(bProduct.getOptionalMaterialList(), false));

        // 分组材料列表（N选M材料）
        result.setMaterialGroupList(createFieldWrapper(bProduct.getMaterialGroupList(), false));

        // 商品素材列表
        result.setProductBannerList(createFieldWrapper(bProduct.getProductBannerList(), false));

        // 商品pdf
        result.setPdfList(createFieldWrapper(bProduct.getPdfList(), false));
    }

    /**
     * 创建字段包装器
     */
    private <T> PlatformNewProductInfoVo<T> createFieldWrapper(T value, Boolean showSyncButton) {
        return new PlatformNewProductInfoVo<>(value, showSyncButton);
    }

    /**
     * 更新有差异的字段
     */
    private void updateChangedFields(ProductCompareResultVo result, Set<String> changedFields) {
        // 分类（1=签证类，2=申校类）
        if (changedFields.contains("category")) {
            result.getCategory().setShowSyncButton(true);
        }

        // 二级分类id
        if (changedFields.contains("secondaryCategory")) {
            result.getSecondaryCategory().setShowSyncButton(true);
        }

        // 二级分类名称
        if (changedFields.contains("secondaryCategoryName")) {
            result.getSecondaryCategoryName().setShowSyncButton(true);
        }

        // 教育阶段（1=中小学，2=college，3=本科，4=研究生）
        if (changedFields.contains("educationalStage")) {
            result.getEducationalStage().setShowSyncButton(true);
        }

        // 地区id
        if (changedFields.contains("districtId")) {
            result.getDistrictId().setShowSyncButton(true);
        }

        // 地区名称
        if (changedFields.contains("districtName")) {
            result.getDistrictName().setShowSyncButton(true);
        }

        // 学校logo
        if (changedFields.contains("schoolLogo")) {
            result.getSchoolLogo().setShowSyncButton(true);
        }

        // 商品封面
        if (changedFields.contains("cover")) {
            result.getCover().setShowSyncButton(true);
        }

        // 商品名称
        if (changedFields.contains("name")) {
            result.getName().setShowSyncButton(true);
        }

        // 商品描述
        if (changedFields.contains("description")) {
            result.getDescription().setShowSyncButton(true);
        }

        // 平台交付价格，0表示免费
        if (changedFields.contains("platformDeliveryPrice")) {
            result.getPlatformDeliveryPrice().setShowSyncButton(true);
        }

        // 交付类型，0=自己交付，1=平台交付
        if (changedFields.contains("deliveryType")) {
            result.getDeliveryType().setShowSyncButton(true);
        }

        // 关键词id列表
        if (changedFields.contains("keywordIds")) {
            result.getKeywordIds().setShowSyncButton(true);
        }

        // 关键词列表
        if (changedFields.contains("keywordList")) {
            result.getKeywordList().setShowSyncButton(true);
        }

        // 关联项目(业务)
        if (changedFields.contains("relatedBusinessNo")) {
            result.getRelatedBusinessNo().setShowSyncButton(true);
        }

        // 关联项目(业务)名称
        if (changedFields.contains("relatedBusinessName")) {
            result.getRelatedBusinessName().setShowSyncButton(true);
        }

        // 关联子级项目(业务)编号
        if (changedFields.contains("relatedChildBusinessNo")) {
            result.getRelatedChildBusinessNo().setShowSyncButton(true);
        }

        // 关联子级项目(业务)名称
        if (changedFields.contains("relatedChildBusinessName")) {
            result.getRelatedChildBusinessName().setShowSyncButton(true);
        }

        // 注意事项
        if (changedFields.contains("notes")) {
            result.getNotes().setShowSyncButton(true);
        }

        // 商品简介
        if (changedFields.contains("feeDescription")) {
            result.getFeeDescription().setShowSyncButton(true);
        }

        // 服务条款
        if (changedFields.contains("termsOfService")) {
            result.getTermsOfService().setShowSyncButton(true);
        }

        // 价格，0表示免费
        if (changedFields.contains("price")) {
            result.getPrice().setShowSyncButton(true);
        }

        // 必须材料列表
        if (changedFields.contains("requiredMaterialList")) {
            result.getRequiredMaterialList().setShowSyncButton(true);
        }
//        if (changedFields.contains("materialImageList")) {
//            result.getRequiredMaterialList().setShowSyncButton(true);
//        }


        // 可选材料列表
        if (changedFields.contains("optionalMaterialList")) {
            result.getOptionalMaterialList().setShowSyncButton(true);
        }
//        if (changedFields.contains("materialImageList")) {
//            result.getOptionalMaterialList().setShowSyncButton(true);
//        }

        // 分组材料列表（N选M材料）
        if (changedFields.contains("materialGroupList")) {
            result.getMaterialGroupList().setShowSyncButton(true);
        }
//        if (changedFields.contains("materialList")) {
//            result.getMaterialGroupList().setShowSyncButton(true);
//        }
//        if (changedFields.contains("materialImageList")) {
//            result.getMaterialGroupList().setShowSyncButton(true);
//        }




        // 商品素材列表
        if (changedFields.contains("productBannerList")) {
            result.getProductBannerList().setShowSyncButton(true);
        }

        // 商品pdf
        if (changedFields.contains("pdfList")) {
            result.getPdfList().setShowSyncButton(true);
        }
    }

    /**
     * <p>
     * 获取商品详情(迷你版，小程序端使用)
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public ProductInfoMiniDto getProductDetailMini(String productNo, boolean isLogin, Integer productVid, Integer category) {
        String productNoNew = shortCodeToProductNo(productNo);

        ProductInfoMiniDto productInfoMiniDto = new ProductInfoMiniDto();
        ProductInfoDto productInfoDto = getProductDetail(productNoNew, null, null, isLogin, productVid, category, true);
        BeanUtils.copyProperties(productInfoDto, productInfoMiniDto);

        return productInfoMiniDto;
    }

    /**
     * <p>
     * 获取商品赠送优惠券信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public List<GiftCouponInfoDto> giftCouponInfo(String productNo, Integer category) {
        // 编号不能为空
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        Integer categoryPro = category == null ? product.getCategory() : category;

        ProductData productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                .eq(ProductData::getProductNo, productNo)
                .eq(ProductData::getCategory, categoryPro)
                .eq(ProductData::getProductVid, product.getProductVid())
        );

        AssertUtils.isNull(productData, "商品不存在");

        List<GiftCouponInfoDto> giftCouponInfoDtoList = new ArrayList<>();

        // 获取赠送优惠券列表
        List<ProductGiftCouponRelation> productGiftCouponRelations = productGiftCouponRelationMapper.selectList(Wrappers.<ProductGiftCouponRelation>lambdaQuery()
                .eq(ProductGiftCouponRelation::getProductNo, productNo)
                .eq(ProductGiftCouponRelation::getProductVid, product.getProductVid())
                .eq(ProductGiftCouponRelation::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        for (ProductGiftCouponRelation productGiftCouponRelation : productGiftCouponRelations) {
            CouponTemplateEo couponTemplateEo = couponExternalService.getCouponTemplateEo(
                    productGiftCouponRelation.getCouponTemplateNo().toString(), 0, 0);

            GiftCouponInfoDto giftCouponInfoDto = new GiftCouponInfoDto();
            BeanUtils.copyProperties(couponTemplateEo, giftCouponInfoDto);
            giftCouponInfoDto.setDistributionCount(
                    couponExternalService.getCouponTemplateDistributionCount(
                            productGiftCouponRelation.getCouponTemplateNo().toString()
                    )
            );
            giftCouponInfoDto.setUsageCount(
                    couponExternalService.getCouponTemplateUsageCount(
                            productGiftCouponRelation.getCouponTemplateNo().toString()
                    )
            );


            giftCouponInfoDtoList.add(giftCouponInfoDto);
        }

        return giftCouponInfoDtoList;
    }

    /**
     * <p>
     * 获取商品小程序码
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getProductQrcodeUrl(GetProductQrcodeVo vo) {
        String productNo = shortCodeToProductNo(vo.getProductNo());
        // 编号不能为空
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        String shortCode = product.getShortCode();
        if (StringUtils.isBlank(shortCode)) {
            shortCode = RandomString.getRandomCode(5);

            productMapper.update(null, Wrappers.<Product>lambdaUpdate()
                    .set(Product::getShortCode, shortCode)
                    .eq(Product::getId, product.getId())
            );
        }

        String page = "packageA/productDetail/index";
        String inviteCode = vo.getInviteCode();
        String parameters = "p=" + shortCode;

        if (!StringUtils.isBlank(inviteCode)) {
            parameters = parameters + "&i=" + inviteCode;
        }

        // 生成小程序码
        String inviteCodeUrl = weappExternalService.createMerchantWeappQrcode(
                false, page, parameters, product.getOrgId(), null, 1280, null);

        return inviteCodeUrl;
    }

    /**
     * <p>
     * 创建商品信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProduct(ProductVo vo, UserLoginInfoEo userLoginInfoEo) {
        Product product = new Product();
        product.setProductNo(IdWorker.getRandomLongId());
        product.setOrgId(userLoginInfoEo.getOrgId());

        // 不能重复导入
        if (StringUtils.isNotBlank(vo.getSourceNo()) && Long.valueOf(vo.getSourceNo()) > 0) {
            ProductData productDataOld = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                    .eq(ProductData::getSourceNo, vo.getSourceNo())
                    .eq(ProductData::getOrgId, userLoginInfoEo.getOrgId())
                    .eq(ProductData::getIsDelete, TrueFalseEnum.FALSE.getCode())
                    .orderByDesc(ProductData::getId)
                    .last("limit 1")
            );

            if (productDataOld != null) {
                Long productDataOldCount = productMapper.selectCount(Wrappers.<Product>lambdaQuery()
                        .eq(Product::getProductNo, productDataOld.getProductNo())
                        .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                        .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
                );

//                AssertUtils.isTrue(productDataOldCount > 0, "已有对应的导入商品，不能重复导入");
            }
        }

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(2)) && (vo.getDistrictId() != null && vo.getDistrictId() > 0)) {
            DistrictInfo districtInfo = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                    .eq(DistrictInfo::getId, vo.getDistrictId())
                    .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(districtInfo, "地区不存在或者已被删除");
        }

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(3)) && (vo.getSecondaryCategory() != null && vo.getSecondaryCategory() > 0)) {
            ProductSecondaryCategory productSecondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                    .eq(ProductSecondaryCategory::getId, vo.getSecondaryCategory())
                    .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(productSecondaryCategory, "二级分类不存在或者已被删除");
        }

        // 处理上下架
        if (vo.getAvailabilityStatus() != null) {
            product.setAvailabilityStatus(vo.getAvailabilityStatus());

            // 添加日志
            ProductLogEo productLogEo = new ProductLogEo();
            productLogEo.setActionCode(105);
            productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
            productLogEo.setOldValue(null);
            productLogEo.setNewValue(product.getAvailabilityStatus().toString());
            productLogExternalService.addProductLog(product.getProductNo(), productLogEo);
        }


        // 填入库存信息
        fillProductStockData(vo, product, userLoginInfoEo);

        Integer productVid = fillProductData(vo, product, userLoginInfoEo, false);

        product.setProductVid(productVid);
        product.setCategory(vo.getCategory());
        product.setSort(getMinSort() - 1);
        product.setShortCode(RandomString.getRandomCode(5));

        productMapper.insert(product);

        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();
        productLogEo.setActionCode(101);
        productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        productLogEo.setNewValue(productVid.toString());
        productLogExternalService.addProductLog(product.getProductNo(), productLogEo);

        return product.getProductNo().toString();
    }


    /**
     * <p>
     * 编辑商品信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    public String qing(ProductVo vo, UserLoginInfoEo userLoginInfoEo) {

        AssertUtils.isTrue(StringUtils.isBlank(vo.getProductNo()), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, vo.getProductNo())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        Integer oldProductVid = product.getProductVid();

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(2)) && (vo.getDistrictId() != null && vo.getDistrictId() > 0)) {
            DistrictInfo districtInfo = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                    .eq(DistrictInfo::getId, vo.getDistrictId())
                    .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(districtInfo, "地区不存在或者已被删除");
        }

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(3)) && (vo.getSecondaryCategory() != null && vo.getSecondaryCategory() > 0)) {
            ProductSecondaryCategory productSecondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                    .eq(ProductSecondaryCategory::getId, vo.getSecondaryCategory())
                    .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(productSecondaryCategory, "二级分类不存在或者已被删除");
        }

        // 处理上下架
        if (vo.getAvailabilityStatus() != null) {
            Integer oldAvailabilityStatus = product.getAvailabilityStatus();
            if (!oldAvailabilityStatus.equals(vo.getAvailabilityStatus())) {
                product.setAvailabilityStatus(vo.getAvailabilityStatus());

                // 添加日志
                ProductLogEo productLogEo = new ProductLogEo();
                productLogEo.setActionCode(105);
                productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
                productLogEo.setOldValue(oldAvailabilityStatus.toString());
                productLogEo.setNewValue(product.getAvailabilityStatus().toString());
                productLogExternalService.addProductLog(product.getProductNo(), productLogEo);
            }
        }


        Integer productVid = fillProductData(vo, product, userLoginInfoEo, true);
        product.setProductVid(productVid);
        Integer oldCategory = product.getCategory();
        product.setCategory(vo.getCategory());

        // 切换分类时，排到最前面
        if (!oldCategory.equals(vo.getCategory())) {
            product.setSort(getMinSort() - 1);
        }


        productMapper.updateById(product);


        return product.getProductNo().toString();

    }


    /**
     * <p>
     * 编辑商品信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateProduct(ProductVo vo, UserLoginInfoEo userLoginInfoEo) {

        AssertUtils.isTrue(StringUtils.isBlank(vo.getProductNo()), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, vo.getProductNo())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        Integer oldProductVid = product.getProductVid();

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(2)) && (vo.getDistrictId() != null && vo.getDistrictId() > 0)) {
            DistrictInfo districtInfo = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                    .eq(DistrictInfo::getId, vo.getDistrictId())
                    .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(districtInfo, "地区不存在或者已被删除");
        }

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(3)) && (vo.getSecondaryCategory() != null && vo.getSecondaryCategory() > 0)) {
            ProductSecondaryCategory productSecondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                    .eq(ProductSecondaryCategory::getId, vo.getSecondaryCategory())
                    .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(productSecondaryCategory, "二级分类不存在或者已被删除");
        }

        // 处理上下架
        if (vo.getAvailabilityStatus() != null) {
            Integer oldAvailabilityStatus = product.getAvailabilityStatus();
            if (!oldAvailabilityStatus.equals(vo.getAvailabilityStatus())) {
                product.setAvailabilityStatus(vo.getAvailabilityStatus());

                // 添加日志
                ProductLogEo productLogEo = new ProductLogEo();
                productLogEo.setActionCode(105);
                productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
                productLogEo.setOldValue(oldAvailabilityStatus.toString());
                productLogEo.setNewValue(product.getAvailabilityStatus().toString());
                productLogExternalService.addProductLog(product.getProductNo(), productLogEo);
            }
        }

        // 填入库存信息
        fillProductStockData(vo, product, userLoginInfoEo);

        Integer productVid = fillProductData(vo, product, userLoginInfoEo, true);
        //qing
        //设置上一个商品版本id
        newProductDataService.update(Wrappers.<NewProductData>lambdaUpdate()
                .set(NewProductData::getLastVid, product.getProductVid())
                .eq(NewProductData::getProductNo, product.getProductNo())
                .eq(NewProductData::getProductVid, productVid)
        );




        product.setProductVid(productVid);
        Integer oldCategory = product.getCategory();
        product.setCategory(vo.getCategory());

        // 切换分类时，排到最前面
        if (!oldCategory.equals(vo.getCategory())) {
            product.setSort(getMinSort() - 1);
        }

        //qing
        //平台审校商品并且是已经同步状态
        if (product.getCategory().equals(ProductCategoryEnum.SCHOOL_APPLICATION.getCode())
                && userLoginInfoEo.getOrgType().equals(OrgTypeEnum.PLATFORM.getCode())
                && product.getIsSync().equals(ProductSyncStatusEnum.SYNCED.getCode())) {
            product.setIsSync(ProductSyncStatusEnum.UPDATED_TO_B_END.getCode());
        }
        //qing
        //B端审校商品且是由平台同步过来的商品
        if (product.getCategory().equals(ProductCategoryEnum.SCHOOL_APPLICATION.getCode())
                && userLoginInfoEo.getOrgType().equals(OrgTypeEnum.BUSINESS.getCode())
                //商品数据来自平台同步
                && product.getSyncSource().equals(TrueFalseEnum.TRUE.getCode())) {
            if (vo.getType() == 1) {
                // 调用newProductDataService更新修改标志
//                newProductDataService.updateModifiedFlag(product.getProductNo().toString(), product.getProductVid(), 0);
                newIProductService.update(
                        Wrappers.<NewProduct>lambdaUpdate()
                                .set(NewProduct::getShowUpdateBtn, 0)
                                .eq(NewProduct::getProductNo, product.getProductNo().toString())
                                );
            }
            //将当前B端商品修改状态设置为已修改
            newIProductService.update(
                    Wrappers.<NewProduct>lambdaUpdate()
                            .set(NewProduct::getIsModified, 1)
                            .eq(NewProduct::getProductNo, product.getProductNo().toString())
            );
        }

        productMapper.updateById(product);

        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();
        productLogEo.setActionCode(102);
        productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        productLogEo.setOldValue(oldProductVid.toString());
        productLogEo.setNewValue(productVid.toString());
        productLogExternalService.addProductLog(product.getProductNo(), productLogEo);

        return product.getProductNo().toString();

    }


    /**
     * <p>
     * 商品详情，刷新材料库材料
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10 todo商品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshProductMaterial(String productNo, String relatedBusinessNo) {
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        Integer oldProductVid = product.getProductVid();

        // 查出商品详情
        ProductInfoDto productInfoDto = getProductDetail(productNo, null, null, true, null, null, true);

        // 商品不存在
        AssertUtils.isNull(productInfoDto, "商品不存在");

        String fillRelatedBusinessNo = productInfoDto.getRelatedBusinessNo();
        if (relatedBusinessNo != null) {
            fillRelatedBusinessNo = relatedBusinessNo;
        }

        // 准备参数
        ProductVo vo = new ProductVo();
        vo.setProductNo(productNo);
        vo.setName(productInfoDto.getName());
        vo.setDescription(productInfoDto.getDescription());
        vo.setNotes(productInfoDto.getNotes());
        vo.setFeeDescription(productInfoDto.getFeeDescription());
        vo.setDiscountDescription(productInfoDto.getDiscountDescription());
        vo.setTermsOfService(productInfoDto.getTermsOfService());
        vo.setRelatedBusinessNo(fillRelatedBusinessNo);
        vo.setRelatedChildBusinessNo(productInfoDto.getRelatedChildBusinessNo());
        vo.setPrice(productInfoDto.getPrice());

        BigDecimal promoPrice = null;
        if (!StringUtils.isBlank(productInfoDto.getPromoPrice())) {
            promoPrice = new BigDecimal(productInfoDto.getPromoPrice());
            promoPrice = promoPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        vo.setPromoPrice(promoPrice);

        BigDecimal secondDiscountPrice = null;
        if (!StringUtils.isBlank(productInfoDto.getSecondDiscountPrice())) {
            secondDiscountPrice = new BigDecimal(productInfoDto.getSecondDiscountPrice());
            secondDiscountPrice = secondDiscountPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        vo.setSecondDiscountPrice(secondDiscountPrice);

        BigDecimal thirdDiscountPrice = null;
        if (!StringUtils.isBlank(productInfoDto.getThirdDiscountPrice())) {
            thirdDiscountPrice = new BigDecimal(productInfoDto.getThirdDiscountPrice());
            thirdDiscountPrice = thirdDiscountPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        vo.setThirdDiscountPrice(thirdDiscountPrice);

        vo.setAvailabilityStatus(product.getAvailabilityStatus());
        vo.setCategory(productInfoDto.getCategory());
        vo.setEducationalStage(productInfoDto.getEducationalStage());
        vo.setDistrictId(productInfoDto.getDistrictId());
        // 过滤文案人员列表，仅保留属于当前公司的人员
        if (productInfoDto.getSourceType() == 1 && productInfoDto.getDeliveryType() == 1) {
            List<Integer> filteredCopywriters = new ArrayList<>();
            if (productInfoDto.getCopywriterList() != null) {
                for (Integer copywriterId : productInfoDto.getCopywriterList()) {
                    UserMinEo userMinEo = userInfoExternalService.getUserMinInfoByUserInfoId(copywriterId);
                    if (userMinEo != null && product.getOrgId().equals(userMinEo.getOrgId())) {
                        filteredCopywriters.add(copywriterId);
                    }
                }
                vo.setCopywriterList(filteredCopywriters);
            }
        } else {
            vo.setCopywriterList(productInfoDto.getCopywriterList());
        }

        vo.setMultipleDiscountFlag(productInfoDto.getMultipleDiscountFlag());
        vo.setCouponGiftFlag(productInfoDto.getCouponGiftFlag());
        vo.setKeywordIds(productInfoDto.getKeywordIds());
        vo.setSecondaryCategory(productInfoDto.getSecondaryCategory());
        // 处理新增封面和校徽字段
        vo.setCover(productInfoDto.getCover());
        vo.setSchoolLogo(productInfoDto.getSchoolLogo());

        vo.setSalesConsultationType(productInfoDto.getSalesConsultationType());
        vo.setPurchaseButtonText(productInfoDto.getPurchaseButtonText());
        vo.setPromotionButtonText(productInfoDto.getPromotionButtonText());
        vo.setConsultationButtonText(productInfoDto.getConsultationButtonText());
        vo.setConsultantWechat(productInfoDto.getConsultantWechat());
        vo.setConsultantQrcode(productInfoDto.getConsultantQrcode());

        vo.setSourceNo(productInfoDto.getSourceNo());

        vo.setDeliveryType(productInfoDto.getSourceType() == 1 ? 2 : productInfoDto.getDeliveryType());
        vo.setPlatformDeliveryPrice(productInfoDto.getPlatformDeliveryPrice());

        // 验证价格和名称
        // 处理 null 情况，若其中一个为 null 而另一个不为 null 则报错
        AssertUtils.isTrue(
                (productInfoDto.getPrice() == null && vo.getPrice() != null) ||
                        (productInfoDto.getPrice() != null && vo.getPrice() == null) ||
                        (productInfoDto.getPrice() != null && vo.getPrice() != null && productInfoDto.getPrice().compareTo(vo.getPrice()) != 0),
                "商品价格异常"
        );
        AssertUtils.isTrue(!productInfoDto.getName().equals(vo.getName()), "商品名称异常");

        // 填入必须材料
        List<MaterialVo> requiredMaterialList = materialDtoToMaterialVo(productInfoDto.getRequiredMaterialList());
        vo.setRequiredMaterialList(requiredMaterialList);

        // 填入可选材料
        List<MaterialVo> optionalMaterialList = materialDtoToMaterialVo(productInfoDto.getOptionalMaterialList());
        vo.setOptionalMaterialList(optionalMaterialList);

        // 填入分组材料
        List<MaterialGroupVo> materialGroupList = new ArrayList<>();
        for (MaterialGroupDto materialGroupDto : productInfoDto.getMaterialGroupList()) {
            MaterialGroupVo materialGroupVo = new MaterialGroupVo();
            materialGroupVo.setGroupName(materialGroupDto.getGroupName());
            materialGroupVo.setMaterialGroupNo(materialGroupDto.getMaterialGroupNo());
            materialGroupVo.setMaterialList(materialDtoToMaterialVo(materialGroupDto.getMaterialList()));
            materialGroupList.add(materialGroupVo);
        }
        vo.setMaterialGroupList(materialGroupList);

        // 填入赠送优惠券
        List<CouponTemplateVo> couponTemplateVoList = new ArrayList<>();
        for (CouponTemplateEo couponTemplateEo : productInfoDto.getCouponList()) {
            CouponTemplateVo couponTemplateVo = new CouponTemplateVo();
            BeanUtils.copyProperties(couponTemplateEo, couponTemplateVo);
            couponTemplateVoList.add(couponTemplateVo);
        }
        vo.setCouponList(couponTemplateVoList);

        List<ProductBannerVo> productBannerVoList = new ArrayList<>();
        for (ProductBannerDto productBannerDto : productInfoDto.getProductBannerList()) {
            ProductBannerVo productBannerVo = new ProductBannerVo();
            BeanUtils.copyProperties(productBannerDto, productBannerVo);
            productBannerVoList.add(productBannerVo);
        }
        vo.setProductBannerList(productBannerVoList);

        List<ProductPdfVo> productPdfVoList = new ArrayList<>();
        for (ProductPdfDto productPdfDto : productInfoDto.getPdfList()) {
            ProductPdfVo productPdfVo = new ProductPdfVo();
            BeanUtils.copyProperties(productPdfDto, productPdfVo);
            productPdfVoList.add(productPdfVo);
        }
        vo.setPdfList(productPdfVoList);


        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setUserInfoId(0);
        userLoginInfoEo.setOrgId(product.getOrgId());
        userLoginInfoEo.setOrgType(orgExternalService.getOrgType(product.getOrgId()));
        Integer productVid = fillProductData(vo, product, userLoginInfoEo, true);
        product.setProductVid(productVid);
        productMapper.updateById(product);

        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();
        productLogEo.setActionCode(102);
        productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        productLogEo.setOldValue(oldProductVid.toString());
        productLogEo.setNewValue(productVid.toString());
        productLogExternalService.addProductLog(product.getProductNo(), productLogEo);
    }

    /**
     * <p>
     * 填入商品库存数据
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    private void fillProductStockData(ProductVo vo, Product product, UserLoginInfoEo userLoginInfoEo) {
        //商品库存
        if (vo.getChangeStock()) {
            // 填入可重置库存
            AssertUtils.isNull(vo.getStock(), "库存不能为空");
            productChangeLogAdd(
                    userLoginInfoEo.getUserInfoId(),
                    "resettableStock",
                    product.getResettableStock() != null ? product.getResettableStock().toString() : "0",
                    vo.getStock().toString(),
                    product.getProductNo()
            );
            product.setResettableStock(vo.getStock());

            // 填入初始库存
            productChangeLogAdd(
                    userLoginInfoEo.getUserInfoId(),
                    "initialStock",
                    product.getInitialStock() != null ? product.getInitialStock().toString() : "0",
                    vo.getStock().toString(),
                    product.getProductNo()
            );
            product.setInitialStock(vo.getStock());
        }

        // 修改促销库存
        if (vo.getChangePromoStock()) {

            Integer promoStock = null;

            if (!StringUtils.isBlank(vo.getPromoStock())) {
                promoStock = Integer.valueOf(vo.getPromoStock());

                // 打开后，促销库存不能是负数
                if (vo.getStockOpenFlag() != null && vo.getStockOpenFlag().equals(1) && vo.getPromoOpenFlag() != null && vo.getPromoOpenFlag().equals(1) && promoStock < 0) {
                    AssertUtils.isTrue(true, "促销库存不能是负数");
                }
            }

            // 填入促销库存
            productChangeLogAdd(
                    userLoginInfoEo.getUserInfoId(),
                    "promoStock",
                    product.getPromoStock() != null ? product.getPromoStock().toString() : "",
                    vo.getPromoStock(),
                    product.getProductNo()
            );
            product.setPromoStock(promoStock);
        }

        // 修改库存开关标志
        if (vo.getStockOpenFlag() != null && (!vo.getStockOpenFlag().equals(product.getStockOpenFlag()))) {
            productChangeLogAdd(
                    userLoginInfoEo.getUserInfoId(),
                    "stockOpenFlag",
                    product.getStockOpenFlag() != null ? product.getStockOpenFlag().toString() : "0",
                    vo.getStockOpenFlag().toString(),
                    product.getProductNo()
            );
            product.setStockOpenFlag(vo.getStockOpenFlag());
        }

        // 修改促销开关标志
        if (vo.getPromoOpenFlag() != null && (!vo.getPromoOpenFlag().equals(product.getPromoOpenFlag()))) {
            productChangeLogAdd(
                    userLoginInfoEo.getUserInfoId(),
                    "promoOpenFlag",
                    product.getPromoOpenFlag() != null ? product.getPromoOpenFlag().toString() : "0",
                    vo.getPromoOpenFlag().toString(),
                    product.getProductNo()
            );
            product.setPromoOpenFlag(vo.getPromoOpenFlag());
        }
    }

    /**
     * <p>
     * 记录商品字段修改日志
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    private void productChangeLogAdd(Integer operatorId, String name, String oldValue, String newValue, Long productNo) {
        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();
        productLogEo.setActionCode(106);
        productLogEo.setOperatorId(operatorId);
        productLogEo.setNewValue(newValue);
        productLogEo.setOldValue(oldValue);
        productLogEo.setRemarks(name);
        productLogExternalService.addProductLog(productNo, productLogEo);
    }

    /**
     * <p>
     * 获取商品地区名称
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    private String getProductDistrictName(Integer districtId) {
        String districtName = "";

        if (districtId == null || districtId <= 0) {
            return districtName;
        }

        DistrictInfo districtInfo = districtInfoMapper.selectById(districtId);
        DistrictInfo districtInfoPid = null;
        if (districtInfo != null) {
            if (districtInfo.getPid() > 0) {
                districtInfoPid = districtInfoMapper.selectById(districtInfo.getPid());
            }

            if (districtInfoPid != null) {
                districtName = districtInfoPid.getName() + "-";
            }
            districtName = districtName + districtInfo.getName();
        }

        return districtName;
    }

    /**
     * <p>
     * 填入商品数据
     * </p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer fillProductData(ProductVo vo, Product product, UserLoginInfoEo userLoginInfoEo, boolean isUpdate) {

        // 商品编号
        Long productNo = product.getProductNo();
        AssertUtils.isNull(productNo, "商品编号不能为空");

        // 生成版本id
        Integer productVid = Integer.valueOf(RandomString.getRandom(8));

        // 新建商品数据
        ProductData productData = new ProductData();
        productData.setProductNo(productNo);
        productData.setProductVid(productVid);
        productData.setOrgId(userLoginInfoEo.getOrgId());

        // 价格需要大于0
        if (vo.getPrice() != null) {
            AssertUtils.isTrue(vo.getPrice().compareTo(BigDecimal.ZERO) < 0, "价格不能小于0");
        }

        if (vo.getPromoPrice() != null) {
            AssertUtils.isTrue(vo.getPromoPrice().compareTo(BigDecimal.ZERO) < 0, "促销价格不能小于0");
        }

        if (vo.getSecondDiscountPrice() != null) {
            AssertUtils.isTrue(vo.getSecondDiscountPrice().compareTo(BigDecimal.ZERO) < 0, "第二个优惠价不能小于0");
        }

        if (vo.getThirdDiscountPrice() != null) {
            AssertUtils.isTrue(vo.getThirdDiscountPrice().compareTo(BigDecimal.ZERO) < 0, "第三个优惠价不能小于0");
        }

//        // 取出BC端返佣
//        CommissionVo bCommission = vo.getBCommission();
//        CommissionVo cCommission = vo.getCCommission();
//
//        // BC返佣不能少于0
//        AssertUtils.isTrue(bCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "B端返佣值不能小于0");
//        AssertUtils.isTrue(cCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "C端返佣值不能小于0");
//
//        // 返佣如果是百分比的话不能超过100
//        if (bCommission.getMethod().equals(1)) {
//            AssertUtils.isTrue(bCommission.getCommissionValue().compareTo(new BigDecimal(100)) > 0, "B端返佣百分比不能超过100");
//        }
//        if (cCommission.getMethod().equals(1)) {
//            AssertUtils.isTrue(cCommission.getCommissionValue().compareTo(new BigDecimal(100)) > 0, "C端返佣百分比不能超过100");
//        }
//
//        // 根据商品价格计算返佣金额
//        BigDecimal productPrice = vo.getPrice();
//        BigDecimal bCommissionAmount = null;
//        if (bCommission.getMethod().equals(1)) {
//            bCommissionAmount = productPrice.multiply(
//                    bCommission.getCommissionValue().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)
//            );
//        }
//        if (bCommission.getMethod().equals(2)) {
//            bCommissionAmount = bCommission.getCommissionValue();
//        }
//        BigDecimal cCommissionAmount = null;
//        if (cCommission.getMethod().equals(1)) {
//            cCommissionAmount = productPrice.multiply(
//                    cCommission.getCommissionValue().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)
//            );
//        }
//        if (cCommission.getMethod().equals(2)) {
//            cCommissionAmount = cCommission.getCommissionValue();
//        }
//
//        AssertUtils.isNull(bCommissionAmount, "B端返佣金额不能为空");
//        AssertUtils.isNull(cCommissionAmount, "C端返佣金额不能为空");
//
//        // 保留两位小数
//        bCommissionAmount = bCommissionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
//        cCommissionAmount = cCommissionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
//
//        // B端返佣金额不能少于C端返佣金额【BC两个其中一个不为0的情况下】
//        if(bCommissionAmount.compareTo(BigDecimal.ZERO) > 0 || cCommissionAmount.compareTo(BigDecimal.ZERO) > 0){
//            AssertUtils.isTrue(bCommissionAmount.compareTo(cCommissionAmount) <= 0, "B端返佣金额必须大于C端返佣金额");
//        }

        // 填入商品数据
        productData.setName(vo.getName());

        if (vo.getPrice() != null) {
            productData.setPrice(vo.getPrice());
        } else {
            productData.setPrice(null);
        }

        if (vo.getPromoPrice() != null) {
            productData.setPromoPrice(vo.getPromoPrice());
        } else {
            productData.setPromoPrice(null);
        }

        // 第二个第三个优惠价
        if (vo.getSecondDiscountPrice() != null) {
            productData.setSecondDiscountPrice(vo.getSecondDiscountPrice());
        } else {
            productData.setSecondDiscountPrice(null);
        }

        if (vo.getThirdDiscountPrice() != null) {
            productData.setThirdDiscountPrice(vo.getThirdDiscountPrice());
        } else {
            productData.setThirdDiscountPrice(null);
        }

        if (vo.getPlatformDeliveryPrice() != null && userLoginInfoEo.getOrgType().equals(3)) {
            AssertUtils.isTrue(vo.getPlatformDeliveryPrice().compareTo(BigDecimal.ZERO) < 0, "交付价格不能小于0");
            productData.setPlatformDeliveryPrice(vo.getPlatformDeliveryPrice());
        } else {
            productData.setPlatformDeliveryPrice(vo.getPrice());
        }

        if (StringUtils.isNotBlank(vo.getSourceNo()) && userLoginInfoEo.getOrgType().equals(5) && Long.valueOf(vo.getSourceNo()) > 0) {

            // 找出商品信息
            Product platformProduct = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                            .eq(Product::getProductNo, vo.getSourceNo())
                            .eq(Product::getOrgId, 1)
//                    .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(platformProduct, "平台商品不存在");

            productData.setSourceNo(Long.valueOf(vo.getSourceNo()));
            productData.setSourceType(1);
            productData.setDeliveryType(2);
        } else {
            productData.setSourceNo(0L);
            productData.setSourceType(0);
            productData.setDeliveryType(0);
        }


        productData.setRelatedBusinessNo(vo.getRelatedBusinessNo());
        productData.setDescription(vo.getDescription());
        productData.setNotes(vo.getNotes() == null ? "" : vo.getNotes());
        productData.setFeeDescription(vo.getFeeDescription() == null ? "" : vo.getFeeDescription());
        productData.setDiscountDescription(vo.getDiscountDescription() == null ? "" : vo.getDiscountDescription());
        productData.setTermsOfService(vo.getTermsOfService() == null ? "" : vo.getTermsOfService());
        productData.setOperatorId(userLoginInfoEo.getUserInfoId());
        productData.setCategory(vo.getCategory());
        productData.setEducationalStage(vo.getEducationalStage());
        productData.setDistrictId(vo.getDistrictId());
        productData.setRelatedChildBusinessNo(vo.getRelatedChildBusinessNo());

        if (vo.getCategory().equals(1)) {
            productData.setDistrictId(null);
        }

        productData.setSalesConsultationType(vo.getSalesConsultationType());
        productData.setPurchaseButtonText(vo.getPurchaseButtonText());
        productData.setPromotionButtonText(vo.getPromotionButtonText());
        productData.setConsultationButtonText(vo.getConsultationButtonText());
        productData.setConsultantWechat(vo.getConsultantWechat());
        productData.setConsultantQrcode(vo.getConsultantQrcode());

        // 处理新增封面和校徽字段
        // 处理封面和校徽字段
        String coverUrl = StringUtils.isNotBlank(vo.getCover()) ? vo.getCover() : null;
        productData.setCover(StringUtils.isNotBlank(coverUrl) ? FileBaseUtil.getRelativeUrl(coverUrl) : null);
        productData.setSchoolLogo(StringUtils.isNotBlank(vo.getSchoolLogo()) ? FileBaseUtil.getRelativeUrl(vo.getSchoolLogo()) : null);

        // 获取封面宽高
        String finalCoverUrl = coverUrl;
        if (StringUtils.isBlank(finalCoverUrl) && vo.getProductBannerList() != null && !vo.getProductBannerList().isEmpty()) {
            ProductBannerVo firstBanner = vo.getProductBannerList().get(0);
            if (firstBanner.getType() == 0) { // 图片类型
                finalCoverUrl = firstBanner.getPath();
            } else if (firstBanner.getType() == 1) { // 视频类型
                finalCoverUrl = firstBanner.getCoverUrl();
            }
        }

        // 判断是否包含.videocc.net/域名
        if (StringUtils.isNotBlank(finalCoverUrl) && !finalCoverUrl.contains(".videocc.net/")) {
            ImageInfoEo imageInfo = FileBaseUtil.getCosImageDimension(finalCoverUrl);
            if (imageInfo != null) {
                productData.setCoverWidth(imageInfo.getWidth());
                productData.setCoverHeight(imageInfo.getHeight());
            }
        }

        productData.setMultipleDiscountFlag(vo.getMultipleDiscountFlag());
        productData.setCouponGiftFlag(vo.getCouponGiftFlag());
        productData.setKeywordIds(vo.getKeywordIds() == null ? "" : vo.getKeywordIds());
        productData.setSecondaryCategory(vo.getSecondaryCategory() == null ? 0 : vo.getSecondaryCategory());

        productDataMapper.insert(productData);

        // 填入banner
        List<ProductBannerVo> productBannerList = vo.getProductBannerList();
        if (productBannerList != null && productBannerList.size() > 0) {
            // 记录排序
            int bannerSort = 0;

            for (ProductBannerVo productBannerVo : productBannerList) {
                ProductBanner productBanner = new ProductBanner();
                productBanner.setProductNo(productNo);
                productBanner.setProductVid(productVid);
                productBanner.setType(productBannerVo.getType());
                productBanner.setSort(bannerSort++);
                productBanner.setDescription(productBannerVo.getDescription());
                productBanner.setUploadRecordNo(StringUtils.isBlank(productBannerVo.getUploadRecordNo()) ? null : Long.valueOf(productBannerVo.getUploadRecordNo()));
                productBanner.setPath(UrlUtil.getRelativeUrl(productBannerVo.getPath()));

                if (!StringUtils.isBlank(productBannerVo.getUploadRecordNo())) {
                    polyvUploadRecordService.fillCoverUrl(productBannerVo.getUploadRecordNo(), productBannerVo.getCoverUrl());
                }

                productBannerMapper.insert(productBanner);
            }
        }

        // 填入pdf
        List<ProductPdfVo> pdfList = vo.getPdfList();
        if (pdfList != null && pdfList.size() > 0) {
            // 记录排序
            int pdfSort = 0;

            for (ProductPdfVo productPdfVo : pdfList) {
                ProductPdf productPdf = new ProductPdf();
                productPdf.setProductNo(productNo);
                productPdf.setProductVid(productVid);
                productPdf.setSort(pdfSort++);
                productPdf.setName(productPdfVo.getName());
                productPdf.setPath(UrlUtil.getRelativeUrl(productPdfVo.getFileUrl()));

                productPdfMapper.insert(productPdf);
            }
        }

        // 创建材料分组
        List<MaterialGroupVo> materialGroupList = vo.getMaterialGroupList();
        if (materialGroupList != null && materialGroupList.size() > 0) {
            // 记录排序
            int sort = 0;
            for (MaterialGroupVo materialGroupVo : materialGroupList) {
                MaterialGroup materialGroup = new MaterialGroup();
                materialGroup.setProductNo(productNo);
                materialGroup.setProductVid(productVid);
                materialGroup.setMaterialGroupNo(IdWorker.getRandomLongId());
                materialGroup.setGroupName(materialGroupVo.getGroupName());
                materialGroup.setSort(sort++);

                // 编辑时，材料编号从旧的里面获得
                if (isUpdate && (!StringUtils.isBlank(materialGroupVo.getMaterialGroupNo()))) {

                    MaterialGroup oldMaterialGroup = materialGroupMapper.selectOne(Wrappers.<MaterialGroup>lambdaQuery()
                                    .eq(MaterialGroup::getMaterialGroupNo, materialGroupVo.getMaterialGroupNo())
//                            .eq(MaterialGroup::getProductNo, productNo)
                                    .last("limit 1")
                    );
                    AssertUtils.isNull(oldMaterialGroup, "未找到对应的材料分组信息");

                    materialGroup.setMaterialGroupNo(Long.valueOf(materialGroupVo.getMaterialGroupNo()));
                }

                materialGroupMapper.insert(materialGroup);

                // 填入材料列表
                fillMaterialList(
                        materialGroupVo.getMaterialList(), productNo, productVid, userLoginInfoEo.getOrgId()
                        , 1, materialGroup.getMaterialGroupNo(), isUpdate
                );
            }
        }

        // 创建必须和可选材料列表
        fillMaterialList(vo.getRequiredMaterialList(), productNo, productVid, userLoginInfoEo.getOrgId()
                , 0, null, isUpdate);
        fillMaterialList(vo.getOptionalMaterialList(), productNo, productVid, userLoginInfoEo.getOrgId()
                , 2, null, isUpdate);

        // 填入文案人员
        List<Integer> copywriterList = vo.getCopywriterList();
        if (copywriterList != null && copywriterList.size() > 0) {
            for (Integer copywriterId : copywriterList) {
                CopywriterUser copywriterUser = new CopywriterUser();
                copywriterUser.setProductNo(productNo);
                copywriterUser.setProductVid(productVid);
                copywriterUser.setUserInfoId(copywriterId);

                copywriterUserMapper.insert(copywriterUser);
            }
        }

        // 赠送优惠券信息
        List<CouponTemplateVo> couponTemplateVoList = vo.getCouponList();
        if (couponTemplateVoList != null && couponTemplateVoList.size() > 0) {
            for (CouponTemplateVo couponTemplateVo : couponTemplateVoList) {
                String couponTemplateNo = "";
                CouponTemplateEo couponTemplateEo = new CouponTemplateEo();
                BeanUtils.copyProperties(couponTemplateVo, couponTemplateEo);
                if ((!isUpdate) || StringUtils.isBlank(couponTemplateVo.getCouponTemplateNo())) {
                    couponTemplateNo = couponExternalService.couponTemplateAdd(couponTemplateEo, product.getOrgId());
                } else {
                    couponTemplateNo = couponExternalService.couponTemplateUpdate(couponTemplateEo, product.getOrgId());
                }

                ProductGiftCouponRelation productGiftCouponRelation = new ProductGiftCouponRelation();
                productGiftCouponRelation.setProductNo(productNo);
                productGiftCouponRelation.setProductVid(productVid);
                productGiftCouponRelation.setCouponTemplateNo(Long.valueOf(couponTemplateNo));

                productGiftCouponRelationMapper.insert(productGiftCouponRelation);
            }
        }


        // 填入B端佣金信息
//        AssertUtils.isNull(bCommission, "佣金信息不能为空");
//        AssertUtils.isTrue(bCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "佣金值不能小于0");
//
//        CommissionInfo commissionInfoB = new CommissionInfo();
//        commissionInfoB.setProductNo(productNo);
//        commissionInfoB.setProductVid(productVid);
//        commissionInfoB.setType(1);
//        commissionInfoB.setCommissionValue(bCommission.getCommissionValue());
//        commissionInfoB.setMethod(bCommission.getMethod());
//        commissionInfoMapper.insert(commissionInfoB);
//
//        // 填入C端佣金信息
//        AssertUtils.isNull(cCommission, "佣金信息不能为空");
//        AssertUtils.isTrue(cCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "佣金值不能小于0");
//
//        CommissionInfo commissionInfoC = new CommissionInfo();
//        commissionInfoC.setProductNo(productNo);
//        commissionInfoC.setProductVid(productVid);
//        commissionInfoC.setType(2);
//        commissionInfoC.setCommissionValue(cCommission.getCommissionValue());
//        commissionInfoC.setMethod(cCommission.getMethod());
//        commissionInfoMapper.insert(commissionInfoC);

        return productVid;

    }

    /**
     * <p>
     * 删除商品
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteProduct(String productNo, UserLoginInfoEo userLoginInfoEo, Integer category) {

        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        ProductData productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                .eq(ProductData::getProductNo, productNo)
                .eq(ProductData::getProductVid, product.getProductVid())
                .eq(ProductData::getCategory, category)
        );
        AssertUtils.isNull(productData, "商品不存在");

        // 软删除
        product.setIsDelete(TrueFalseEnum.TRUE.getCode());
        product.setAvailabilityStatus(0);
        productMapper.updateById(product);


        //删除关联的次表数据
        newProductDataService.remove(Wrappers.<NewProductData>lambdaQuery()
                .eq(NewProductData::getProductNo, productNo)
        );


        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();
        productLogEo.setActionCode(103);
        productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        productLogExternalService.addProductLog(product.getProductNo(), productLogEo);

        if (userLoginInfoEo.getOrgType().equals(3) && product.getAvailabilityStatus().equals(0)) {
            ProductStatusChangeVo productStatusChangeVo = new ProductStatusChangeVo();
            productStatusChangeVo.setProductNo(productNo);
            productStatusChangeVo.setAvailabilityStatus(0);
            platformProductStatusSync(productStatusChangeVo, userLoginInfoEo, product.getCategory());
        }

        return product.getProductNo().toString();
    }

    /**
     * <p>
     * 商品排序
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sortProduct(ProductSortVo vo, UserLoginInfoEo userLoginInfoEo, Integer category) {

        // 找出被拖拽的元素
        Product draggedProduct = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, vo.getDraggedNo())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getCategory, category)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(draggedProduct, "被拖拽的商品不存在");

        // 找出目标位置原本的元素
        Product targetProduct = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, vo.getTargetNo())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getCategory, category)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(targetProduct, "目标商品不存在");

        // 更新被拖拽的id
        Integer draggedSort = draggedProduct.getSort();
        draggedProduct.setSort(targetProduct.getSort());
        productMapper.updateById(draggedProduct);

        // 找出所有商品，除了被拖拽的商品
        List<Product> productList = productMapper.selectList(Wrappers.<Product>lambdaQuery()
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getCategory, category)
                .ne(Product::getProductNo, draggedProduct.getProductNo())
                .orderByAsc(Product::getSort)
        );

        for (Product product : productList) {
            // 往下拖拽
            if (draggedSort < targetProduct.getSort()) {
                if (product.getSort() <= draggedProduct.getSort()) {
                    // 采用update 的 set去更新
                    productMapper.update(null, Wrappers.<Product>lambdaUpdate()
                            .set(Product::getSort, product.getSort() - 1)
                            .eq(Product::getId, product.getId())
                    );
                }
            }
            // 往上拖拽
            else {
                if (product.getSort() >= draggedProduct.getSort()) {
                    // 采用update 的 set去更新
                    productMapper.update(null, Wrappers.<Product>lambdaUpdate()
                            .set(Product::getSort, product.getSort() + 1)
                            .eq(Product::getId, product.getId())
                    );
                }
            }
        }
    }

    private void platformProductStatusSync(ProductStatusChangeVo vo, UserLoginInfoEo userLoginInfoEo, Integer category) {
        List<Product> productList = productMapper.getProductListBySourceNo(vo.getProductNo(), 1);
        for (Product product : productList) {
            ProductStatusChangeVo productStatusChangeVo = new ProductStatusChangeVo();
            productStatusChangeVo.setProductNo(product.getProductNo().toString());
            productStatusChangeVo.setAvailabilityStatus(vo.getAvailabilityStatus());

            UserLoginInfoEo userLoginInfoEoTemp = new UserLoginInfoEo();
            userLoginInfoEoTemp.setOrgId(product.getOrgId());
            userLoginInfoEoTemp.setUserInfoId(userLoginInfoEo.getUserInfoId());
            userLoginInfoEoTemp.setOrgType(5);

            ProductStatusChange(productStatusChangeVo, userLoginInfoEoTemp, category);
        }
    }

    /**
     * <p>
     * 商品状态改变
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String ProductStatusChange(ProductStatusChangeVo vo, UserLoginInfoEo userLoginInfoEo, Integer category) {

        AssertUtils.isTrue(StringUtils.isBlank(vo.getProductNo()), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, vo.getProductNo())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getCategory, category)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        ProductData productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                .eq(ProductData::getProductNo, vo.getProductNo())
                .eq(ProductData::getProductVid, product.getProductVid())
        );
        AssertUtils.isNull(productData, "商品不存在");

        if (productData.getSourceNo() > 0 && vo.getAvailabilityStatus().equals(1) && productData.getDeliveryType().equals(1)) {
            // 找出商品信息
            Product platformProduct = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                            .eq(Product::getProductNo, productData.getSourceNo())
                            .eq(Product::getOrgId, 1)
//                    .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(platformProduct, "平台商品不存在");
            AssertUtils.isTrue(platformProduct.getAvailabilityStatus().equals(0), "对应的平台商品已下架，请联系平台管理员");
        }

        // 修改状态
        if (vo.getAvailabilityStatus() != null) {
            Integer oldAvailabilityStatus = product.getAvailabilityStatus();
            if (!vo.getAvailabilityStatus().equals(oldAvailabilityStatus)) {
                productMapper.update(null, Wrappers.<Product>lambdaUpdate()
                        .set(Product::getAvailabilityStatus, vo.getAvailabilityStatus())
                        .eq(Product::getProductNo, vo.getProductNo())
                );

                // 添加日志
                ProductLogEo productLogEo = new ProductLogEo();
                productLogEo.setActionCode(105);
                productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
                productLogEo.setOldValue(oldAvailabilityStatus.toString());
                productLogEo.setNewValue(product.getAvailabilityStatus().toString());
                productLogExternalService.addProductLog(product.getProductNo(), productLogEo);

                if (userLoginInfoEo.getOrgType().equals(3) && vo.getAvailabilityStatus().equals(0)) {
                    platformProductStatusSync(vo, userLoginInfoEo, product.getCategory());
                }
            }
        }

        return product.getProductNo().toString();
    }

    /**
     * <p>
     * 获取全部关键词
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProductKeyword> getProductKeywordAll() {
        List<ProductKeyword> productKeywordList = productKeywordMapper.selectList(Wrappers.<ProductKeyword>lambdaQuery()
                .eq(ProductKeyword::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByAsc(ProductKeyword::getSort)
        );

        return productKeywordList;
    }

    /**
     * <p>
     * 分配文案人员
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignCopyWriter(String productNo, String orderNo, Integer memberId) {
        // 参数不能为空
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");
        AssertUtils.isNull(memberId, "会员不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        ProductData productData = productDataMapper.selectOne(Wrappers.<ProductData>lambdaQuery()
                .eq(ProductData::getProductNo, productNo)
                .eq(ProductData::getProductVid, product.getProductVid())
        );
        AssertUtils.isNull(productData, "商品不存在");

        if (productData.getDeliveryType().equals(1)) {
            product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                    .eq(Product::getProductNo, productData.getSourceNo())
                    .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(product, "商品不存在");
        }

        // 版本
        Integer productVid = product.getProductVid();

        // 获取文案人员列表
        List<CopywriterUser> copywriterUserList = copywriterUserMapper.selectList(Wrappers.<CopywriterUser>lambdaQuery()
                .eq(CopywriterUser::getProductNo, product.getProductNo())
                .eq(CopywriterUser::getProductVid, productVid)
                .eq(CopywriterUser::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        // 没有人员则不用分配直接返回
        if (copywriterUserList.size() <= 0) {
            return;
        }

        List<Integer> copywriterUserIdList = copywriterUserList.stream().map(CopywriterUser::getUserInfoId).collect(Collectors.toList());
        // 排序，从小到大
        copywriterUserIdList.sort(Comparator.comparingInt(Integer::intValue));

        // 找出分配记录里的最后一次分配是给了谁
        CopywriterAssignmentRecord record = copywriterAssignmentRecordMapper.selectOne(Wrappers.<CopywriterAssignmentRecord>lambdaQuery()
                .eq(CopywriterAssignmentRecord::getProductNo, product.getProductNo())
                .eq(CopywriterAssignmentRecord::getReuseLastFlag, 0)
                .eq(CopywriterAssignmentRecord::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByDesc(CopywriterAssignmentRecord::getCreatedAt)
                .last("limit 1")
        );

        // 是否复用之前分配的文案人员
        Boolean isReuseLastCopywriter = false;

        // 找出可以复用的文案人员id【是一个id列表】
        List<CopywriterAssignmentRecord> reuseLastList = productCrossMapper.getReuseLastCopywriterRecordList(product.getProductNo().toString(), memberId);

        // 提取出id
        List<Integer> reuseLastCopywriterList = reuseLastList.stream().map(CopywriterAssignmentRecord::getUserInfoId).collect(Collectors.toList());

        // 上次轮流分配的文案id
        Integer copywriterUserId = 0;
        Integer lastCopywriterUserId = record == null ? null : record.getUserInfoId();

        // 是否复用之前分配过的人员， 需要reuseLastCopywriterList和copywriterUserIdList都存在的id
        for (Integer reuseLastCopywriterId : reuseLastCopywriterList) {
            if (copywriterUserIdList.contains(reuseLastCopywriterId) && userInfoExternalService.checkUserEnabled(reuseLastCopywriterId)) {
                isReuseLastCopywriter = true;
                copywriterUserId = reuseLastCopywriterId;
                break;
            }
        }

        if (!isReuseLastCopywriter) {
            // 上次为空或者为0则使用第一个
            if (lastCopywriterUserId == null || lastCopywriterUserId.equals(0)) {
                // 获取本次轮询轮到的文案人员
                for (Integer sales : copywriterUserIdList) {
                    if (userInfoExternalService.checkUserEnabled(sales)) {
                        copywriterUserId = sales;
                        break;
                    }
                }
            } else {
                // 获取本次轮询轮到的文案人员
                for (Integer sales : copywriterUserIdList) {
                    if (sales > lastCopywriterUserId && userInfoExternalService.checkUserEnabled(sales)) {
                        copywriterUserId = sales;
                        break;
                    }
                }
            }
        }

        // 如果本次轮询轮到的文案人员为空，则使用第一个
        if (copywriterUserId.equals(0)) {
            copywriterUserId = copywriterUserIdList.get(0);
        }

        // 判断文案人员是否存在
        if (!userInfoExternalService.checkUserEnabled(copywriterUserId)) {
            // 不存在则直接返回，因为分配不了
            return;
        }

        // 新增文案人员分配记录
        CopywriterAssignmentRecord assignmentRecord = new CopywriterAssignmentRecord();
        assignmentRecord.setProductNo(product.getProductNo());
        assignmentRecord.setUserInfoId(copywriterUserId);
        assignmentRecord.setOrderNo(Long.valueOf(orderNo));
        assignmentRecord.setReuseLastFlag(isReuseLastCopywriter ? 1 : 0);

        copywriterAssignmentRecordMapper.insert(assignmentRecord);
    }

    /**
     * <p>
     * 材料库列表
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public BasePageResult<MaterialDto> materialLibraryList(QueryMaterialVo vo, Integer watermarkOrgId) {
        Page<MaterialDto> page = new Page<>(vo.getPageNo(), vo.getPageSize());

        Page<MaterialDto> result = productCrossMapper.materialLibraryList(page, vo);

        Long watermarkImageNo = orgExternalService.getWatermarkNo(watermarkOrgId);

        // 循环列表获取素材图片
        for (MaterialDto materialDto : result.getRecords()) {

            // 获取最新的素材
            Material material = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                    .eq(Material::getMaterialNo, materialDto.getMaterialNo())
                    .eq(Material::getMaterialLibraryFlag, 1)
                    .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
                    .orderByDesc(Material::getCreatedAt)
            );

            if (material == null) {
                continue;
            }

            materialDto.setTitle(material.getTitle());

            List<MaterialImageDto> materialImageDtoList = new ArrayList<>();

            List<MaterialImageRelation> materialImageRelationList = materialImageRelationMapper.selectList(Wrappers.<MaterialImageRelation>lambdaQuery()
                    .eq(MaterialImageRelation::getMaterialId, material.getId())
                    .eq(MaterialImageRelation::getIsDelete, TrueFalseEnum.FALSE.getCode())
                    .orderByAsc(MaterialImageRelation::getSort)
            );

            materialImageRelationList.forEach(materialImageRelation -> {
                MaterialImageDto materialImageDto = new MaterialImageDto();
                materialImageDto.setFileName(materialImageRelation.getFileName());
                materialImageDto.setFileNo(materialImageRelation.getFileNo().toString());

                // 获取文件数据
                MaterialImage materialImage = materialImageMapper.selectOne(Wrappers.<MaterialImage>lambdaQuery()
                        .eq(MaterialImage::getFileNo, materialImageRelation.getFileNo())
                        .eq(MaterialImage::getIsDelete, TrueFalseEnum.FALSE.getCode())
                );
                if (materialImage != null) {
                    String watermarkedFilePath = watermarkImageExternalService.getWatermarkedFilePath(watermarkImageNo, materialImage.getFileNo(), materialImage.getTemplateType());

                    materialImageDto.setFilePath(watermarkedFilePath);
                    materialImageDto.setFileUrl(FileBaseUtil.getFileUrl(watermarkedFilePath));
                    materialImageDto.setTemplateType(materialImage.getTemplateType());
                }

                materialImageDtoList.add(materialImageDto);
            });

            materialDto.setMaterialImageList(materialImageDtoList);
        }

        return new BasePageResult<>(result.getTotal(), result.getRecords(), vo);
    }

    /**
     * <p>
     * 材料清单库添加
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialLibraryAdd(MaterialVo vo, UserLoginInfoEo userLoginInfoEo) {

        // 找出最大的库id
        Material maxMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                .eq(Material::getMaterialLibraryFlag, 1)
                .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByDesc(Material::getLibraryId)
                .last("limit 1")
        );

        Integer libraryId = maxMaterial == null ? 1 : maxMaterial.getLibraryId() + 1;

        Material material = new Material();
        material.setProductNo(null);
        material.setProductVid(null);
        material.setOrgId(userLoginInfoEo.getOrgId());
        material.setMaterialNo(IdWorker.getRandomLongId());
        material.setTitle(vo.getTitle());
        material.setMaterialType(0);
        material.setMaterialLibraryFlag(1);
        material.setRemarks(vo.getRemarks());
        material.setSource(1);
        material.setOperatorId(userLoginInfoEo.getUserInfoId());
        material.setLibraryId(libraryId);

        // 找出最大的排序id
        Material maxSortMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                .eq(Material::getMaterialLibraryFlag, 1)
                .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByDesc(Material::getSort)
                .last("limit 1")
        );
        Integer maxSort = maxSortMaterial == null ? 1 : maxSortMaterial.getSort();
        material.setSort(maxSort + 1);

        materialMapper.insert(material);

        // 创建材料模板
        List<MaterialImageVo> materialImageList = vo.getMaterialImageList();
        if (materialImageList != null && materialImageList.size() > 0) {
            // 记录排序
            int imageSort = 0;

            for (MaterialImageVo materialImageVo : materialImageList) {
                // 创建材料图片关联关系
                MaterialImageRelation materialImageRelation = new MaterialImageRelation();
                materialImageRelation.setMaterialId(material.getId());
                materialImageRelation.setFileNo(Long.valueOf(materialImageVo.getFileNo()));
                materialImageRelation.setFileName(materialImageVo.getFileName());
                materialImageRelation.setSort(imageSort++);

                materialImageRelationMapper.insert(materialImageRelation);
            }
        }
    }

    /**
     * <p>
     * 获取订单分配的文案人员id
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    public Integer getCopyWriterIdByOrderNo(String orderNo) {
        // 参数不能为空
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        CopywriterAssignmentRecord record = copywriterAssignmentRecordMapper.selectOne(Wrappers.<CopywriterAssignmentRecord>lambdaQuery()
                .eq(CopywriterAssignmentRecord::getOrderNo, orderNo)
                .eq(CopywriterAssignmentRecord::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByDesc(CopywriterAssignmentRecord::getCreatedAt)
                .last("limit 1")
        );

        Integer copywriterUserId = null;
        if (record != null) {
            copywriterUserId = record.getUserInfoId();
        }

        return copywriterUserId;
    }

    /**
     * <p>
     * 材料清单库编辑
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialLibraryUpdate(MaterialVo vo, UserLoginInfoEo userLoginInfoEo) {

        // 编号必填
        AssertUtils.isTrue(StringUtils.isBlank(vo.getMaterialNo()), "材料编号不能为空");

        // 找出对应材料
        List<Material> oldMaterialList = materialMapper.selectList(Wrappers.<Material>lambdaQuery()
                .eq(Material::getMaterialNo, vo.getMaterialNo())
                .eq(Material::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Material::getMaterialLibraryFlag, 1)
                .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        // 不存在则报错
        AssertUtils.isTrue(oldMaterialList == null || oldMaterialList.size() == 0, "材料不存在");

        // 软删除
        Long createdAt = 0L;
        Integer libraryId = 0;
        Integer oldSort = 0;
        for (Material oldMaterial : oldMaterialList) {
            oldMaterial.setIsDelete(TrueFalseEnum.TRUE.getCode());
            materialMapper.updateById(oldMaterial);

            createdAt = oldMaterial.getCreatedAt();
            libraryId = oldMaterial.getLibraryId();
            oldSort = oldMaterial.getSort();
        }

        // 新建
        Material material = new Material();
        material.setProductNo(null);
        material.setProductVid(null);
        material.setOrgId(userLoginInfoEo.getOrgId());
        material.setMaterialNo(Long.valueOf(vo.getMaterialNo()));
        material.setTitle(vo.getTitle());
        material.setMaterialType(0);
        material.setMaterialLibraryFlag(1);
        material.setRemarks(vo.getRemarks());
        material.setSource(1);
        material.setOperatorId(userLoginInfoEo.getUserInfoId());
        material.setLibraryId(libraryId);
        material.setSort(oldSort);

        if (createdAt > 0) {
            material.setCreatedAt(createdAt);
        }

        materialMapper.insert(material);

        // 创建材料模板
        List<MaterialImageVo> materialImageList = vo.getMaterialImageList();
        if (materialImageList != null && materialImageList.size() > 0) {
            // 记录排序
            int imageSort = 0;

            for (MaterialImageVo materialImageVo : materialImageList) {
                // 创建材料图片关联关系
                MaterialImageRelation materialImageRelation = new MaterialImageRelation();
                materialImageRelation.setMaterialId(material.getId());
                materialImageRelation.setFileNo(Long.valueOf(materialImageVo.getFileNo()));
                materialImageRelation.setFileName(materialImageVo.getFileName());
                materialImageRelation.setSort(imageSort++);

                materialImageRelationMapper.insert(materialImageRelation);
            }
        }

        // 找出使用了该材料的商品，更新材料
        List<Product> productList = productCrossMapper.getProductListByMaterialNo(vo.getMaterialNo());
        if (productList != null && productList.size() > 0) {
            for (Product product : productList) {
                refreshProductMaterial(product.getProductNo().toString(), null);
            }
        }
    }

    /**
     * <p>
     * 素材库-删除
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String materialLibraryDelete(String materialNo, UserLoginInfoEo userLoginInfoEo) {

        AssertUtils.isTrue(StringUtils.isBlank(materialNo), "材料编号不能为空");

        // 找出对应材料
        List<Material> oldMaterialList = materialMapper.selectList(Wrappers.<Material>lambdaQuery()
                .eq(Material::getMaterialNo, materialNo)
                .eq(Material::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Material::getMaterialLibraryFlag, 1)
        );

        // 不存在则报错
        AssertUtils.isTrue(oldMaterialList == null || oldMaterialList.size() == 0, "材料不存在");

        // 找出使用了该材料的商品，有则报错
        List<Product> productList = productCrossMapper.getProductListByMaterialNo(materialNo);
        AssertUtils.isTrue(productList != null && productList.size() > 0, "该材料已被商品引用，请先移除引用后再删除");

        // 软删除
        for (Material oldMaterial : oldMaterialList) {
            oldMaterial.setIsDelete(TrueFalseEnum.TRUE.getCode());
            materialMapper.updateById(oldMaterial);
        }

        return materialNo;
    }

    /**
     * <p>
     * 材料库排序
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialLibrarySort(ProductSortVo vo, UserLoginInfoEo userLoginInfoEo) {
        // 找出被拖拽的元素
        Material draggedMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                .eq(Material::getMaterialNo, vo.getDraggedNo())
                .eq(Material::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Material::getMaterialLibraryFlag, 1)
                .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(draggedMaterial, "被拖拽的材料不存在");

        // 找出目标位置原本的元素
        Material targetMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                .eq(Material::getMaterialNo, vo.getTargetNo())
                .eq(Material::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Material::getMaterialLibraryFlag, 1)
                .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(targetMaterial, "目标材料不存在");

        // 更新被拖拽的id
        Integer draggedSort = draggedMaterial.getSort();
        draggedMaterial.setSort(targetMaterial.getSort());
        materialMapper.updateById(draggedMaterial);

        // 找出所有材料，除了被拖拽的材料
        List<Material> materialList = materialMapper.selectList(Wrappers.<Material>lambdaQuery()
                .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .eq(Material::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Material::getMaterialLibraryFlag, 1)
                .ne(Material::getMaterialNo, draggedMaterial.getMaterialNo())
                .orderByAsc(Material::getSort)
        );

        for (Material material : materialList) {
            // 往下拖拽
            if (draggedSort < targetMaterial.getSort()) {
                if (material.getSort() <= draggedMaterial.getSort()) {
                    // 采用update 的 set去更新
                    materialMapper.update(null, Wrappers.<Material>lambdaUpdate()
                            .set(Material::getSort, material.getSort() - 1)
                            .eq(Material::getId, material.getId())
                            .eq(Material::getMaterialLibraryFlag, 1)
                    );
                }
            }
            // 往上拖拽
            else {
                if (material.getSort() >= draggedMaterial.getSort()) {
                    // 采用update 的 set去更新
                    materialMapper.update(null, Wrappers.<Material>lambdaUpdate()
                            .set(Material::getSort, material.getSort() + 1)
                            .eq(Material::getId, material.getId())
                            .eq(Material::getMaterialLibraryFlag, 1)
                    );
                }
            }
        }
    }


    //qing

    /**
     * 平台端：批量商品同步
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchSyncResultVo batchSynchronizeProduct(BatchSynchronizeProductVo batchSyncVo, UserLoginInfoEo userLoginInfoEo) {

        log.info("开始批量同步商品，初始同步商品数量: {}, 更新同步商品数量: {}",
                batchSyncVo.getInitialSyncProductNos() != null ? batchSyncVo.getInitialSyncProductNos().size() : 0,
                batchSyncVo.getUpdateSyncProductNos() != null ? batchSyncVo.getUpdateSyncProductNos().size() : 0);

        BatchSyncResultVo result = new BatchSyncResultVo();
        int successCount = 0;
        int failCount = 0;
        List<String> failedProductNos = new ArrayList<>();

        // 2. 处理初始同步商品列表
        if (CollectionUtils.isNotEmpty(batchSyncVo.getInitialSyncProductNos())) {
            for (String productNo : batchSyncVo.getInitialSyncProductNos()) {
                try {
                    synchronizeProduct(productNo, userLoginInfoEo, ProductSyncTypeEnum.INITIAL_SYNC.getCode().toString());
                    successCount++;
                    log.info("初始同步商品 {} 成功", productNo);
                } catch (Exception e) {
                    failCount++;
                    failedProductNos.add(productNo);
                    log.error("初始同步商品 {} 失败: {}", productNo, e.getMessage(), e);
                }
            }
        }

        // 3. 处理更新同步商品列表
        if (CollectionUtils.isNotEmpty(batchSyncVo.getUpdateSyncProductNos())) {
            for (String productNo : batchSyncVo.getUpdateSyncProductNos()) {
                try {
                    synchronizeProduct(productNo, userLoginInfoEo, ProductSyncTypeEnum.UPDATE_SYNC.getCode().toString());
                    successCount++;
                    log.info("更新同步商品 {} 成功", productNo);
                } catch (Exception e) {
                    failCount++;
                    failedProductNos.add(productNo);
                    log.error("更新同步商品 {} 失败: {}", productNo, e.getMessage(), e);
                }
            }
        }

        // 设置返回结果
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setTotalCount(successCount + failCount);
        result.setAllSuccess(failCount == 0);
        result.setFailedProductNos(failedProductNos);

        log.info("批量同步商品完成，成功: {} 个，失败: {} 个", successCount, failCount);

        return result;
    }

    /**
     * 平台端：商品同步按钮点击
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synchronizeProduct(String productNo, UserLoginInfoEo userLoginInfoEo, String type) {

        // 1. 验证输入参数
        validateSyncParameters(productNo, type);

        // 2. 验证商品状态
        NewProduct product = validateAndGetProduct(productNo);

        // 3. 解析同步类型
        ProductSyncTypeEnum syncType = parseSyncType(type);

        // 4. 根据类型执行不同的同步逻辑
        switch (syncType) {
            case INITIAL_SYNC:
                handleInitialSync(productNo);
                break;
            case UPDATE_SYNC:
                handleUpdateSync(productNo);
                break;
            default:
                throw new BusinessException(200, "不支持的同步类型: " + type);
        }

        // 5. 标记商品为已同步
        markProductAsSynced(product);
    }

    /**
     * 验证同步参数
     */
    private void validateSyncParameters(String productNo, String type) {
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");
        AssertUtils.isTrue(StringUtils.isBlank(type), "同步类型不能为空");
    }

    /**
     * 验证并获取商品
     */
    private NewProduct validateAndGetProduct(String productNo) {
        NewProduct product = newIProductService.getOne(
                Wrappers.<NewProduct>lambdaQuery()
                        .eq(NewProduct::getProductNo, productNo)
        );
        AssertUtils.isNull(product, "不存在的商品编号: " + productNo);
        AssertUtils.isTrue(product.getAvailabilityStatus().equals(TrueFalseEnum.FALSE.getCode()),
                "请先上架商品");
        return product;
    }

    /**
     * 解析同步类型
     */
    private ProductSyncTypeEnum parseSyncType(String type) {
        ProductSyncTypeEnum syncType = ProductSyncTypeEnum.parse(type);
        AssertUtils.isNull(syncType, "无效的同步类型: " + type);
        return syncType;
    }

    /**
     * 处理初始同步
     */
    private void handleInitialSync(String productNo) {
        List<ProductSynchronization> syncOrgList = iProductSynchronizationService.list();
        if (CollectionUtils.isEmpty(syncOrgList)) {
            log.info("暂无配置同步的机构，跳过初始同步");
            return;
        }

        // 一次性查询商品详情，避免重复查询
        ProductInfoDto productInfoDto = getProductDetailSafely(productNo);

        for (ProductSynchronization productSynchronization : syncOrgList) {
            try {
                iProductSynchronizationService.syncSingleProductToOrg(
                        productNo,
                        productSynchronization.getOrgId(),
                        productSynchronization,
                        productInfoDto
                );
                log.info("成功同步商品 {} 到机构 {}", productNo, productSynchronization.getOrgId());
            } catch (Exception e) {
                log.error("同步商品 {} 到机构 {} 失败: {}", productNo, productSynchronization.getOrgId(), e.getMessage(), e);
                // 继续处理其他机构，不中断整个流程
            }
        }
    }

    /**
     * 处理更新同步
     */
    private void handleUpdateSync(String productNo) {
        List<PlatformSyncProductDto> platformSyncProducts = newIProductService.getPlatformSyncProducts(Long.valueOf(productNo));
        if (CollectionUtils.isEmpty(platformSyncProducts)) {
            log.info("暂无需要更新同步的B端商品");
            return;
        }

        for (PlatformSyncProductDto syncProduct : platformSyncProducts) {
            try {
                processSingleProductSync(productNo, syncProduct);
            } catch (Exception e) {
                log.error("处理商品 {} 同步失败: {}", syncProduct.getProductNo(), e.getMessage(), e);
                // 继续处理其他商品，不中断整个流程
            }
        }
    }

    /**
     * 处理单个商品的同步
     */
    private void processSingleProductSync(String productNo, PlatformSyncProductDto syncProduct) {
        if (syncProduct.getIsModified().equals(ModifiedStatusEnum.MODIFIED.getCode())) {
            handleModifiedProduct(syncProduct, productNo);
        } else {
            handleUnmodifiedProduct(productNo, syncProduct);
        }
    }

    /**
     * 处理已修改的B端商品
     */
    private void handleModifiedProduct(PlatformSyncProductDto syncProduct, String platformProductNo) {
        log.info("商品编号 {} 在B端已被修改，跳过同步更新", syncProduct.getProductNo());

        //查询当前B端商品信息
        NewProduct currentProduct = newIProductService.getOne(Wrappers.<NewProduct>lambdaQuery()
                .eq(NewProduct::getProductNo, syncProduct.getProductNo())
        );

        if (currentProduct != null) {
            //开启地区、关键字、业务显示
            iProductSynchronizationService.enableDisplayFlags(syncProduct.getOrgId());
            // 查询平台商品信息获取当前版本号
            NewProduct platformProduct = newIProductService.getOne(Wrappers.<NewProduct>lambdaQuery()
                    .eq(NewProduct::getProductNo, platformProductNo)
            );

            if (platformProduct != null) {
                // 更新平台商品的platform_sync_vid字段，保存当前版本号
                newIProductService.update(
                        Wrappers.<NewProduct>lambdaUpdate()
                                .set(NewProduct::getPlatformSyncVid, platformProduct.getProductVid())
                                .eq(NewProduct::getProductNo, platformProductNo)
                );
                log.info("已更新平台商品编号 {} 的同步版本号为: {}", platformProductNo, platformProduct.getProductVid());

            } else {
                log.warn("未找到平台商品编号 {} 的记录", platformProductNo);
            }
            // 设置B端商品的查看更新按钮显示状态
            newIProductService.update(
                    Wrappers.<NewProduct>lambdaUpdate()
                            .set(NewProduct::getShowUpdateBtn, TrueFalseEnum.TRUE.getCode())
                            .eq(NewProduct::getProductNo, syncProduct.getProductNo())
            );
            log.info("已设置商品编号 {} 的更新按钮显示状态", syncProduct.getProductNo());
        } else {
            log.warn("未找到商品编号 {} 在机构 {} 的记录", syncProduct.getProductNo(), syncProduct.getOrgId());
        }
    }

    /**
     * 处理未修改的B端商品
     */
    private void handleUnmodifiedProduct(String productNo, PlatformSyncProductDto syncProduct) {
        log.info("商品编号 {} 在B端未修改，执行同步更新", syncProduct.getProductNo());

        // 获取平台商品详细信息
        ProductInfoDto platformInfo = getProductDetailSafely(productNo);
        ProductVo platformVo = convertProductInfoDtoToProductVo(platformInfo);

        // 清理不需要同步的数据
        cleanupSyncData(platformVo, syncProduct);

        //开启地区、关键字、业务显示
        iProductSynchronizationService.enableDisplayFlags(syncProduct.getOrgId());

        // 创建用户登录信息
        UserLoginInfoEo userLoginInfo = createSyncUserLoginInfo(syncProduct.getOrgId());

        platformVo.setSourceNo(String.valueOf(syncProduct.getSourceNo()));

        // 执行同步更新
        synchronizaUpdateProduct(platformVo, userLoginInfo);
    }




    /**
     * 清理不需要同步的数据
     */
    private void cleanupSyncData(ProductVo platformVo, PlatformSyncProductDto syncProduct) {
        // 设置B端商品编号
        platformVo.setProductNo(syncProduct.getProductNo().toString());

        // 清理销售咨询信息 - 不同步平台的咨询信息
        platformVo.setSalesConsultationType(null);
        platformVo.setConsultationButtonText(null);
        platformVo.setConsultantWechat(null);
        platformVo.setConsultantQrcode(null);
        if (syncProduct.getSalesConsultationType().equals(SalesConsultationTypeEnum.CONSULTATION.getCode())) {
            platformVo.setSalesConsultationType(syncProduct.getSalesConsultationType());
            platformVo.setConsultationButtonText(syncProduct.getConsultationButtonText());
            platformVo.setConsultantWechat(syncProduct.getConsultantWechat());
            platformVo.setConsultantQrcode(syncProduct.getConsultantQrcode());
        }

        // 清理价格相关信息
        platformVo.setPromoPrice(null);
        platformVo.setPromoStock(null);
        platformVo.setPrice(null);
        platformVo.setSecondDiscountPrice(null);
        platformVo.setThirdDiscountPrice(null);
        platformVo.setDiscountDescription(null);

        // 清理其他不同步的信息
        platformVo.setTermsOfService(null);
        platformVo.setPurchaseButtonText("立即购买");
        platformVo.setCopywriterList(new ArrayList<>());
        platformVo.setStock(null);
    }

    /**
     * 创建同步用的用户登录信息
     */
    private UserLoginInfoEo createSyncUserLoginInfo(Integer orgId) {
        UserLoginInfoEo userLoginInfo = new UserLoginInfoEo();
        userLoginInfo.setOrgId(orgId);
        userLoginInfo.setOrgType(OrgTypeEnum.BUSINESS.getCode());
        userLoginInfo.setUserInfoId(1); // 通过平台同步，用户id设置为1
        return userLoginInfo;
    }

    /**
     * 安全地获取商品详情
     */
    private ProductInfoDto getProductDetailSafely(String productNo) {
        ProductInfoDto productInfoDto = getProductDetail(productNo, null, null, true, null, null, true);
        AssertUtils.isNull(productInfoDto, "商品详情不存在: " + productNo);
        return productInfoDto;
    }

    /**
     * 标记商品为已同步
     */
    private void markProductAsSynced(NewProduct product) {
        newIProductService.update(
                Wrappers.<NewProduct>lambdaUpdate()
                        .set(NewProduct::getIsSync, ProductSyncStatusEnum.SYNCED.getCode())
                        .eq(NewProduct::getProductNo, product.getProductNo())
        );
        log.info("商品 {} 已标记为同步状态", product.getProductNo());
    }


    /**
     * 同步数据-修改了部分代码
     *
     * @param productInfoDto
     * @param productNo
     * @param productSynchronization
     */
    @Override
    public void synchronizeProduct(ProductInfoDto productInfoDto, String productNo, ProductSynchronization productSynchronization) {
        productInfoDto.setSourceNo(productInfoDto.getProductNo());

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, productNo)
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");


        // 准备参数
        ProductVo vo = new ProductVo();
        //生成商品编号
        vo.setProductNo(String.valueOf(IdWorker.getRandomLongId()));
        vo.setName(productInfoDto.getName());
        vo.setDescription(productInfoDto.getDescription());
        vo.setNotes(productInfoDto.getNotes());
        vo.setFeeDescription(productInfoDto.getFeeDescription());
        vo.setDiscountDescription(productInfoDto.getDiscountDescription());
        vo.setTermsOfService(productInfoDto.getTermsOfService());
        vo.setRelatedBusinessNo(productInfoDto.getRelatedBusinessNo());
        vo.setRelatedChildBusinessNo(productInfoDto.getRelatedChildBusinessNo());
        vo.setPrice(productInfoDto.getPrice());

        BigDecimal promoPrice = null;
        if (!StringUtils.isBlank(productInfoDto.getPromoPrice())) {
            promoPrice = new BigDecimal(productInfoDto.getPromoPrice());
            promoPrice = promoPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        vo.setPromoPrice(promoPrice);

        BigDecimal secondDiscountPrice = null;
        if (!StringUtils.isBlank(productInfoDto.getSecondDiscountPrice())) {
            secondDiscountPrice = new BigDecimal(productInfoDto.getSecondDiscountPrice());
            secondDiscountPrice = secondDiscountPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        vo.setSecondDiscountPrice(secondDiscountPrice);

        BigDecimal thirdDiscountPrice = null;
        if (!StringUtils.isBlank(productInfoDto.getThirdDiscountPrice())) {
            thirdDiscountPrice = new BigDecimal(productInfoDto.getThirdDiscountPrice());
            thirdDiscountPrice = thirdDiscountPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        vo.setThirdDiscountPrice(thirdDiscountPrice);

        vo.setAvailabilityStatus(product.getAvailabilityStatus());
        vo.setCategory(productInfoDto.getCategory());
        vo.setEducationalStage(productInfoDto.getEducationalStage());
        vo.setDistrictId(productInfoDto.getDistrictId());
        // 过滤文案人员列表，仅保留属于当前公司的人员
        if (productInfoDto.getSourceType() == 1 && productInfoDto.getDeliveryType() == 1) {
            List<Integer> filteredCopywriters = new ArrayList<>();
            if (productInfoDto.getCopywriterList() != null) {
                for (Integer copywriterId : productInfoDto.getCopywriterList()) {
                    UserMinEo userMinEo = userInfoExternalService.getUserMinInfoByUserInfoId(copywriterId);
                    if (userMinEo != null && product.getOrgId().equals(userMinEo.getOrgId())) {
                        filteredCopywriters.add(copywriterId);
                    }
                }
                vo.setCopywriterList(filteredCopywriters);
            }
        } else {
            vo.setCopywriterList(productInfoDto.getCopywriterList());
        }


        vo.setKeywordIds(productInfoDto.getKeywordIds());
        vo.setSecondaryCategory(productInfoDto.getSecondaryCategory());
        // 处理新增封面和校徽字段
        vo.setCover(productInfoDto.getCover());
        vo.setSchoolLogo(productInfoDto.getSchoolLogo());


        vo.setSourceNo(productInfoDto.getSourceNo());

        vo.setDeliveryType(productInfoDto.getSourceType() == 1 ? 2 : productInfoDto.getDeliveryType());
        vo.setPlatformDeliveryPrice(productInfoDto.getPlatformDeliveryPrice());

        // 验证价格和名称
        // 处理 null 情况，若其中一个为 null 而另一个不为 null 则报错
        AssertUtils.isTrue(
                (productInfoDto.getPrice() == null && vo.getPrice() != null) ||
                        (productInfoDto.getPrice() != null && vo.getPrice() == null) ||
                        (productInfoDto.getPrice() != null && vo.getPrice() != null && productInfoDto.getPrice().compareTo(vo.getPrice()) != 0),
                "商品价格异常"
        );
        AssertUtils.isTrue(!productInfoDto.getName().equals(vo.getName()), "商品名称异常");

        // 填入必须材料
        List<MaterialVo> requiredMaterialList = materialDtoToMaterialVo(productInfoDto.getRequiredMaterialList());
        vo.setRequiredMaterialList(requiredMaterialList);

        // 填入可选材料
        List<MaterialVo> optionalMaterialList = materialDtoToMaterialVo(productInfoDto.getOptionalMaterialList());
        vo.setOptionalMaterialList(optionalMaterialList);

        // 填入分组材料
        List<MaterialGroupVo> materialGroupList = new ArrayList<>();
        for (MaterialGroupDto materialGroupDto : productInfoDto.getMaterialGroupList()) {
            MaterialGroupVo materialGroupVo = new MaterialGroupVo();
            materialGroupVo.setGroupName(materialGroupDto.getGroupName());
            materialGroupVo.setMaterialGroupNo(materialGroupDto.getMaterialGroupNo());
            materialGroupVo.setMaterialList(materialDtoToMaterialVo(materialGroupDto.getMaterialList()));
            materialGroupList.add(materialGroupVo);
        }
        vo.setMaterialGroupList(materialGroupList);


        List<ProductBannerVo> productBannerVoList = new ArrayList<>();
        for (ProductBannerDto productBannerDto : productInfoDto.getProductBannerList()) {
            ProductBannerVo productBannerVo = new ProductBannerVo();
            BeanUtils.copyProperties(productBannerDto, productBannerVo);
            productBannerVoList.add(productBannerVo);
        }
        vo.setProductBannerList(productBannerVoList);

        List<ProductPdfVo> productPdfVoList = new ArrayList<>();
        for (ProductPdfDto productPdfDto : productInfoDto.getPdfList()) {
            ProductPdfVo productPdfVo = new ProductPdfVo();
            BeanUtils.copyProperties(productPdfDto, productPdfVo);
            productPdfVoList.add(productPdfVo);
        }
        vo.setPdfList(productPdfVoList);


        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setUserInfoId(productSynchronization.getUserInfoId());
        userLoginInfoEo.setOrgId(productSynchronization.getOrgId());
        userLoginInfoEo.setOrgType(productSynchronization.getOrgType());


        //设置咨询按钮文案、咨询顾问微信号、咨询顾问二维码
        if (productSynchronization.getSynchronizationType() == 1) {
            vo.setConsultantWechat(productSynchronization.getConsultantWechat());
            vo.setConsultantQrcode(FileBaseUtil.getFileUrl(productSynchronization.getConsultantQrcode()));
//            vo.setConsultantQrcode(productSynchronization.getConsultantQrcode());
            vo.setConsultationButtonText(productSynchronization.getConsultationButtonText());
        } else {
            vo.setConsultationButtonText("立即咨询");
        }
        vo.setSalesConsultationType(productSynchronization.getSynchronizationType());


        //创建商品信息
        createData(vo, userLoginInfoEo);

    }


    /**
     * 创建商品信息-已修改部分代码
     *
     * @param vo
     * @param userLoginInfoEo
     * @return
     */
    public String createData(ProductVo vo, UserLoginInfoEo userLoginInfoEo) {
        Product product = new Product();
        product.setProductNo(IdWorker.getRandomLongId());
        product.setOrgId(userLoginInfoEo.getOrgId());


        if ((vo.getCategory().equals(1) || vo.getCategory().equals(2)) && (vo.getDistrictId() != null && vo.getDistrictId() > 0)) {
            DistrictInfo districtInfo = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                    .eq(DistrictInfo::getId, vo.getDistrictId())
                    .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(districtInfo, "地区不存在或者已被删除");
        }

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(3)) && (vo.getSecondaryCategory() != null && vo.getSecondaryCategory() > 0)) {
            ProductSecondaryCategory productSecondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                    .eq(ProductSecondaryCategory::getId, vo.getSecondaryCategory())
                    .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(productSecondaryCategory, "二级分类不存在或者已被删除");
        }

        // 处理上下架
        if (vo.getAvailabilityStatus() != null) {
            product.setAvailabilityStatus(vo.getAvailabilityStatus());

            // 添加日志
            ProductLogEo productLogEo = new ProductLogEo();
            productLogEo.setActionCode(105);
            productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
            productLogEo.setOldValue(null);
            productLogEo.setNewValue(product.getAvailabilityStatus().toString());
            productLogExternalService.addProductLog(product.getProductNo(), productLogEo);
        }

        //填充前去除购买类型的数据
        vo.setPromoPrice(null);
        vo.setPromoStock(null);
        vo.setPrice(null);
        vo.setSecondDiscountPrice(null);
        vo.setThirdDiscountPrice(null);
        vo.setDiscountDescription(null);
        vo.setCopywriterList(new ArrayList<>());

        //服务条款不同步
        vo.setTermsOfService(null);


        Integer productVid = fillProductData(vo, product, userLoginInfoEo, false);

        product.setProductVid(productVid);
        product.setCategory(vo.getCategory());
        product.setSort(getMinSort() - 1);
        product.setShortCode(RandomString.getRandomCode(5));
        //设置数据来源为平台同步
        product.setSyncSource(1);

        productMapper.insert(product);

        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();

        productLogEo.setActionCode(101);
        productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        productLogEo.setNewValue(productVid.toString());
        productLogExternalService.addProductLog(product.getProductNo(), productLogEo);

        return product.getProductNo().toString();
    }


    /**
     * <p>
     * 复制商品数据
     * </p>
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer copyProductData(ProductVo vo, Product product, UserLoginInfoEo userLoginInfoEo, boolean isUpdate) {

        // 商品编号
        Long productNo = product.getProductNo();
        AssertUtils.isNull(productNo, "商品编号不能为空");

        // 生成版本id
        Integer productVid = Integer.valueOf(RandomString.getRandom(8));

        // 新建商品数据
        ProductData productData = new ProductData();
        productData.setProductNo(productNo);
        productData.setProductVid(productVid);
        productData.setOrgId(userLoginInfoEo.getOrgId());

        //设置咨询按钮文案、咨询顾问微信号、咨询顾问二维码
        productData.setConsultantWechat(vo.getConsultantWechat());
        productData.setConsultantQrcode(vo.getConsultantQrcode());
        productData.setConsultationButtonText(vo.getConsultationButtonText());


        // 价格需要大于0
        if (vo.getPrice() != null) {
            AssertUtils.isTrue(vo.getPrice().compareTo(BigDecimal.ZERO) < 0, "价格不能小于0");
        }

        if (vo.getPromoPrice() != null) {
            AssertUtils.isTrue(vo.getPromoPrice().compareTo(BigDecimal.ZERO) < 0, "促销价格不能小于0");
        }

        if (vo.getSecondDiscountPrice() != null) {
            AssertUtils.isTrue(vo.getSecondDiscountPrice().compareTo(BigDecimal.ZERO) < 0, "第二个优惠价不能小于0");
        }

        if (vo.getThirdDiscountPrice() != null) {
            AssertUtils.isTrue(vo.getThirdDiscountPrice().compareTo(BigDecimal.ZERO) < 0, "第三个优惠价不能小于0");
        }

//        // 取出BC端返佣
//        CommissionVo bCommission = vo.getBCommission();
//        CommissionVo cCommission = vo.getCCommission();
//
//        // BC返佣不能少于0
//        AssertUtils.isTrue(bCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "B端返佣值不能小于0");
//        AssertUtils.isTrue(cCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "C端返佣值不能小于0");
//
//        // 返佣如果是百分比的话不能超过100
//        if (bCommission.getMethod().equals(1)) {
//            AssertUtils.isTrue(bCommission.getCommissionValue().compareTo(new BigDecimal(100)) > 0, "B端返佣百分比不能超过100");
//        }
//        if (cCommission.getMethod().equals(1)) {
//            AssertUtils.isTrue(cCommission.getCommissionValue().compareTo(new BigDecimal(100)) > 0, "C端返佣百分比不能超过100");
//        }
//
//        // 根据商品价格计算返佣金额
//        BigDecimal productPrice = vo.getPrice();
//        BigDecimal bCommissionAmount = null;
//        if (bCommission.getMethod().equals(1)) {
//            bCommissionAmount = productPrice.multiply(
//                    bCommission.getCommissionValue().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)
//            );
//        }
//        if (bCommission.getMethod().equals(2)) {
//            bCommissionAmount = bCommission.getCommissionValue();
//        }
//        BigDecimal cCommissionAmount = null;
//        if (cCommission.getMethod().equals(1)) {
//            cCommissionAmount = productPrice.multiply(
//                    cCommission.getCommissionValue().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)
//            );
//        }
//        if (cCommission.getMethod().equals(2)) {
//            cCommissionAmount = cCommission.getCommissionValue();
//        }
//
//        AssertUtils.isNull(bCommissionAmount, "B端返佣金额不能为空");
//        AssertUtils.isNull(cCommissionAmount, "C端返佣金额不能为空");
//
//        // 保留两位小数
//        bCommissionAmount = bCommissionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
//        cCommissionAmount = cCommissionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
//
//        // B端返佣金额不能少于C端返佣金额【BC两个其中一个不为0的情况下】
//        if(bCommissionAmount.compareTo(BigDecimal.ZERO) > 0 || cCommissionAmount.compareTo(BigDecimal.ZERO) > 0){
//            AssertUtils.isTrue(bCommissionAmount.compareTo(cCommissionAmount) <= 0, "B端返佣金额必须大于C端返佣金额");
//        }

        // 填入商品数据
        productData.setName(vo.getName());

        if (vo.getPrice() != null) {
            productData.setPrice(vo.getPrice());
        } else {
            productData.setPrice(null);
        }

        if (vo.getPromoPrice() != null) {
            productData.setPromoPrice(vo.getPromoPrice());
        } else {
            productData.setPromoPrice(null);
        }

        // 第二个第三个优惠价
        if (vo.getSecondDiscountPrice() != null) {
            productData.setSecondDiscountPrice(vo.getSecondDiscountPrice());
        } else {
            productData.setSecondDiscountPrice(null);
        }

        if (vo.getThirdDiscountPrice() != null) {
            productData.setThirdDiscountPrice(vo.getThirdDiscountPrice());
        } else {
            productData.setThirdDiscountPrice(null);
        }

        if (vo.getPlatformDeliveryPrice() != null && userLoginInfoEo.getOrgType().equals(3)) {
            AssertUtils.isTrue(vo.getPlatformDeliveryPrice().compareTo(BigDecimal.ZERO) < 0, "交付价格不能小于0");
            productData.setPlatformDeliveryPrice(vo.getPlatformDeliveryPrice());
        } else {
            productData.setPlatformDeliveryPrice(vo.getPrice());
        }

        if (StringUtils.isNotBlank(vo.getSourceNo()) && userLoginInfoEo.getOrgType().equals(5) && Long.valueOf(vo.getSourceNo()) > 0) {

            // 找出商品信息
            Product platformProduct = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                            .eq(Product::getProductNo, vo.getSourceNo())
                            .eq(Product::getOrgId, 1)
//                    .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(platformProduct, "平台商品不存在");

            productData.setSourceNo(Long.valueOf(vo.getSourceNo()));
            productData.setSourceType(1);
            productData.setDeliveryType(2);
        } else {
            productData.setSourceNo(0L);
            productData.setSourceType(0);
            productData.setDeliveryType(0);
        }


        productData.setRelatedBusinessNo(vo.getRelatedBusinessNo());
        productData.setDescription(vo.getDescription());
        productData.setNotes(vo.getNotes() == null ? "" : vo.getNotes());
        productData.setFeeDescription(vo.getFeeDescription() == null ? "" : vo.getFeeDescription());
        productData.setDiscountDescription(vo.getDiscountDescription() == null ? "" : vo.getDiscountDescription());
        productData.setTermsOfService(vo.getTermsOfService() == null ? "" : vo.getTermsOfService());
        productData.setOperatorId(userLoginInfoEo.getUserInfoId());
        productData.setCategory(vo.getCategory());
        productData.setEducationalStage(vo.getEducationalStage());
        productData.setDistrictId(vo.getDistrictId());
        productData.setRelatedChildBusinessNo(vo.getRelatedChildBusinessNo());

        if (vo.getCategory().equals(1)) {
            productData.setDistrictId(null);
        }

        productData.setSalesConsultationType(vo.getSalesConsultationType());
        productData.setPurchaseButtonText(vo.getPurchaseButtonText());
        productData.setPromotionButtonText(vo.getPromotionButtonText());
        productData.setConsultationButtonText(vo.getConsultationButtonText());
        productData.setConsultantWechat(vo.getConsultantWechat());
        productData.setConsultantQrcode(vo.getConsultantQrcode());

        // 处理新增封面和校徽字段
        // 处理封面和校徽字段
        String coverUrl = StringUtils.isNotBlank(vo.getCover()) ? vo.getCover() : null;
        productData.setCover(StringUtils.isNotBlank(coverUrl) ? FileBaseUtil.getRelativeUrl(coverUrl) : null);
        productData.setSchoolLogo(StringUtils.isNotBlank(vo.getSchoolLogo()) ? FileBaseUtil.getRelativeUrl(vo.getSchoolLogo()) : null);

        // 获取封面宽高
        String finalCoverUrl = coverUrl;
        if (StringUtils.isBlank(finalCoverUrl) && vo.getProductBannerList() != null && !vo.getProductBannerList().isEmpty()) {
            ProductBannerVo firstBanner = vo.getProductBannerList().get(0);
            if (firstBanner.getType() == 0) { // 图片类型
                finalCoverUrl = firstBanner.getPath();
            } else if (firstBanner.getType() == 1) { // 视频类型
                finalCoverUrl = firstBanner.getCoverUrl();
            }
        }

        // 判断是否包含.videocc.net/域名
        if (StringUtils.isNotBlank(finalCoverUrl) && !finalCoverUrl.contains(".videocc.net/")) {
            ImageInfoEo imageInfo = FileBaseUtil.getCosImageDimension(finalCoverUrl);
            if (imageInfo != null) {
                productData.setCoverWidth(imageInfo.getWidth());
                productData.setCoverHeight(imageInfo.getHeight());
            }
        }

        productData.setMultipleDiscountFlag(vo.getMultipleDiscountFlag());
        productData.setCouponGiftFlag(vo.getCouponGiftFlag());
        productData.setKeywordIds(vo.getKeywordIds() == null ? "" : vo.getKeywordIds());
        productData.setSecondaryCategory(vo.getSecondaryCategory() == null ? 0 : vo.getSecondaryCategory());

        productDataMapper.insert(productData);

        // 填入banner
        List<ProductBannerVo> productBannerList = vo.getProductBannerList();
        if (productBannerList != null && productBannerList.size() > 0) {
            // 记录排序
            int bannerSort = 0;

            for (ProductBannerVo productBannerVo : productBannerList) {
                ProductBanner productBanner = new ProductBanner();
                productBanner.setProductNo(productNo);
                productBanner.setProductVid(productVid);
                productBanner.setType(productBannerVo.getType());
                productBanner.setSort(bannerSort++);
                productBanner.setDescription(productBannerVo.getDescription());
                productBanner.setUploadRecordNo(StringUtils.isBlank(productBannerVo.getUploadRecordNo()) ? null : Long.valueOf(productBannerVo.getUploadRecordNo()));
                productBanner.setPath(UrlUtil.getRelativeUrl(productBannerVo.getPath()));

                if (!StringUtils.isBlank(productBannerVo.getUploadRecordNo())) {
                    polyvUploadRecordService.fillCoverUrl(productBannerVo.getUploadRecordNo(), productBannerVo.getCoverUrl());
                }

                productBannerMapper.insert(productBanner);
            }
        }

        // 填入pdf
        List<ProductPdfVo> pdfList = vo.getPdfList();
        if (pdfList != null && pdfList.size() > 0) {
            // 记录排序
            int pdfSort = 0;

            for (ProductPdfVo productPdfVo : pdfList) {
                ProductPdf productPdf = new ProductPdf();
                productPdf.setProductNo(productNo);
                productPdf.setProductVid(productVid);
                productPdf.setSort(pdfSort++);
                productPdf.setName(productPdfVo.getName());
                productPdf.setPath(UrlUtil.getRelativeUrl(productPdfVo.getFileUrl()));

                productPdfMapper.insert(productPdf);
            }
        }

        // 创建材料分组
        List<MaterialGroupVo> materialGroupList = vo.getMaterialGroupList();
        if (materialGroupList != null && materialGroupList.size() > 0) {
            // 记录排序
            int sort = 0;
            for (MaterialGroupVo materialGroupVo : materialGroupList) {
                MaterialGroup materialGroup = new MaterialGroup();
                materialGroup.setProductNo(productNo);
                materialGroup.setProductVid(productVid);
                materialGroup.setMaterialGroupNo(IdWorker.getRandomLongId());
                materialGroup.setGroupName(materialGroupVo.getGroupName());
                materialGroup.setSort(sort++);

                // 编辑时，材料编号从旧的里面获得
                if (isUpdate && (!StringUtils.isBlank(materialGroupVo.getMaterialGroupNo()))) {

                    MaterialGroup oldMaterialGroup = materialGroupMapper.selectOne(Wrappers.<MaterialGroup>lambdaQuery()
                                    .eq(MaterialGroup::getMaterialGroupNo, materialGroupVo.getMaterialGroupNo())
//                            .eq(MaterialGroup::getProductNo, productNo)
                                    .last("limit 1")
                    );
                    AssertUtils.isNull(oldMaterialGroup, "未找到对应的材料分组信息");

                    materialGroup.setMaterialGroupNo(Long.valueOf(materialGroupVo.getMaterialGroupNo()));
                }

                materialGroupMapper.insert(materialGroup);

                // 填入材料列表
                fillMaterialList(
                        materialGroupVo.getMaterialList(), productNo, productVid, userLoginInfoEo.getOrgId()
                        , 1, materialGroup.getMaterialGroupNo(), isUpdate
                );
            }
        }

        // 创建必须和可选材料列表
        fillMaterialList(vo.getRequiredMaterialList(), productNo, productVid, userLoginInfoEo.getOrgId()
                , 0, null, isUpdate);
        fillMaterialList(vo.getOptionalMaterialList(), productNo, productVid, userLoginInfoEo.getOrgId()
                , 2, null, isUpdate);

        // 填入文案人员
        List<Integer> copywriterList = vo.getCopywriterList();
        if (copywriterList != null && copywriterList.size() > 0) {
            for (Integer copywriterId : copywriterList) {
                CopywriterUser copywriterUser = new CopywriterUser();
                copywriterUser.setProductNo(productNo);
                copywriterUser.setProductVid(productVid);
                copywriterUser.setUserInfoId(copywriterId);

                copywriterUserMapper.insert(copywriterUser);
            }
        }

        // 赠送优惠券信息
        List<CouponTemplateVo> couponTemplateVoList = vo.getCouponList();
        if (couponTemplateVoList != null && couponTemplateVoList.size() > 0) {
            for (CouponTemplateVo couponTemplateVo : couponTemplateVoList) {
                String couponTemplateNo = "";
                CouponTemplateEo couponTemplateEo = new CouponTemplateEo();
                BeanUtils.copyProperties(couponTemplateVo, couponTemplateEo);
                if ((!isUpdate) || StringUtils.isBlank(couponTemplateVo.getCouponTemplateNo())) {
                    couponTemplateNo = couponExternalService.couponTemplateAdd(couponTemplateEo, product.getOrgId());
                } else {
                    couponTemplateNo = couponExternalService.couponTemplateUpdate(couponTemplateEo, product.getOrgId());
                }

                ProductGiftCouponRelation productGiftCouponRelation = new ProductGiftCouponRelation();
                productGiftCouponRelation.setProductNo(productNo);
                productGiftCouponRelation.setProductVid(productVid);
                productGiftCouponRelation.setCouponTemplateNo(Long.valueOf(couponTemplateNo));

                productGiftCouponRelationMapper.insert(productGiftCouponRelation);
            }
        }


        // 填入B端佣金信息
//        AssertUtils.isNull(bCommission, "佣金信息不能为空");
//        AssertUtils.isTrue(bCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "佣金值不能小于0");
//
//        CommissionInfo commissionInfoB = new CommissionInfo();
//        commissionInfoB.setProductNo(productNo);
//        commissionInfoB.setProductVid(productVid);
//        commissionInfoB.setType(1);
//        commissionInfoB.setCommissionValue(bCommission.getCommissionValue());
//        commissionInfoB.setMethod(bCommission.getMethod());
//        commissionInfoMapper.insert(commissionInfoB);
//
//        // 填入C端佣金信息
//        AssertUtils.isNull(cCommission, "佣金信息不能为空");
//        AssertUtils.isTrue(cCommission.getCommissionValue().compareTo(BigDecimal.ZERO) < 0, "佣金值不能小于0");
//
//        CommissionInfo commissionInfoC = new CommissionInfo();
//        commissionInfoC.setProductNo(productNo);
//        commissionInfoC.setProductVid(productVid);
//        commissionInfoC.setType(2);
//        commissionInfoC.setCommissionValue(cCommission.getCommissionValue());
//        commissionInfoC.setMethod(cCommission.getMethod());
//        commissionInfoMapper.insert(commissionInfoC);

        return productVid;

    }


    /**
     * 获取最小排序
     */
    private Integer getMinSort() {
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByAsc(Product::getSort)
                .last("limit 1")
        );
        if (product == null) {
            return 1;
        }
        return product.getSort();
    }

    /**
     * 提取富文本里的图片URL
     */
    private List<String> extractImages(String htmlContent) {
        List<String> imageUrls = new ArrayList<>();

        // 定义正则表达式，匹配 img 标签中的 src 属性
        String regex = "<img[^>]*src=\"(https?://[^\"]+dajiaochuguo\\.com[^\"]*)\"[^>]*>";

        // 使用 Pattern 和 Matcher 匹配
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        // 查找并提取匹配的图片链接
        while (matcher.find()) {
            imageUrls.add(matcher.group(1));
        }

        return imageUrls;
    }

    /**
     * 提取图片URL里的文件编号
     */
    private String extractImagesFileNo(String htmlContent) {

        // 定义正则表达式，匹配 img 标签中的 src 属性
        String regex = "/watermarkImages/files/(\\d+)/";

        // 使用 Pattern 和 Matcher 匹配
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        // 查找并提取匹配的图片链接
        if (matcher.find()) {
            return matcher.group(1);
        }

        return "";
    }

    private String shortCodeToProductNo(String shortCode) {
        if (StringUtils.isBlank(shortCode)) {
            return "";
        }

        if (shortCode.length() > 8) {
            return shortCode;
        }

        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getShortCode, shortCode)
                .orderByAsc(Product::getSort)
                .last("limit 1")
        );
        if (product == null) {
            return shortCode;
        }
        return product.getProductNo().toString();
    }

    /**
     * 填入材料列表
     */
    private void fillMaterialList(List<MaterialVo> materialList,
                                  Long productNo, Integer productVid, Integer orgId,
                                  Integer materialType, Long materialGroupNo,
                                  boolean isUpdate
    ) {

        // 商品编号和版本不能为空
        AssertUtils.isTrue(productNo == null || productVid == null, "商品编号和版本不能为空");

        // 填入材料
        if (materialList != null && materialList.size() > 0) {
            // 记录排序
            int sort = 0;

            for (MaterialVo materialVo : materialList) {
                Material material = new Material();
                material.setProductNo(productNo);
                material.setProductVid(productVid);
                material.setOrgId(orgId);
                material.setMaterialType(materialType);
                material.setMaterialGroupNo(materialGroupNo);
                material.setSort(sort++);
                material.setRemarks(materialVo.getRemarks());

                if (materialVo.getSource() != null && materialVo.getSource().equals(1)) {
                    // 材料库的材料

                    // 编号必传
                    AssertUtils.isTrue(StringUtils.isBlank(materialVo.getMaterialNo()), "材料属于材料库时，材料编号不能为空");

                    // 找到该材料
                    Material libraryMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                            .eq(Material::getMaterialNo, materialVo.getMaterialNo())
                            .eq(Material::getMaterialLibraryFlag, 1)
                            .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
                            .orderByDesc(Material::getCreatedAt)
                            .last("limit 1")
                    );

                    AssertUtils.isNull(libraryMaterial, "未找到对应的材料库材料");

                    material.setSource(1);
                    material.setSourceId(libraryMaterial.getId());
                    material.setMaterialNo(libraryMaterial.getMaterialNo());
                    material.setTitle(libraryMaterial.getTitle());

                    materialMapper.insert(material);
                } else {
                    // 手动填写
                    material.setMaterialNo(IdWorker.getRandomLongId());
                    material.setTitle(materialVo.getTitle());

                    material.setSource(0);

                    // 编辑时，材料编号从旧的里面获得
                    if (isUpdate && (!StringUtils.isBlank(materialVo.getMaterialNo()))) {
                        Material oldMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                                        .eq(Material::getMaterialNo, materialVo.getMaterialNo())
//                                .eq(Material::getProductNo, productNo)
                                        .last("limit 1")
                        );
                        AssertUtils.isNull(oldMaterial, "未找到对应的材料信息");

                        material.setMaterialNo(Long.valueOf(materialVo.getMaterialNo()));
                    }

                    materialMapper.insert(material);

                    // 创建材料模板
                    List<MaterialImageVo> materialImageList = materialVo.getMaterialImageList();
                    if (materialImageList != null && materialImageList.size() > 0) {
                        // 记录排序
                        int imageSort = 0;

                        for (MaterialImageVo materialImageVo : materialImageList) {
                            // 创建材料图片关联关系
                            MaterialImageRelation materialImageRelation = new MaterialImageRelation();
                            materialImageRelation.setMaterialId(material.getId());
                            materialImageRelation.setFileNo(Long.valueOf(materialImageVo.getFileNo()));
                            materialImageRelation.setFileName(materialImageVo.getFileName());
                            materialImageRelation.setSort(imageSort++);

                            materialImageRelationMapper.insert(materialImageRelation);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取材料列表
     */
    private List<MaterialDto> getMaterialList(
            Long productNo, Integer productVid,
            Integer materialType, Long materialGroupNo, Integer watermarkOrgId
    ) {

        // 商品编号和版本不能为空
        AssertUtils.isTrue(productNo == null || productVid == null, "商品编号和版本不能为空");

        Long watermarkImageNo = orgExternalService.getWatermarkNo(watermarkOrgId);

        // 获取商品素材
        List<Material> materials = new ArrayList<>();
        if (materialGroupNo == null) {
            materials = materialMapper.selectList(Wrappers.<Material>lambdaQuery()
                    .eq(Material::getProductNo, productNo)
                    .eq(Material::getProductVid, productVid)
                    .eq(Material::getMaterialType, materialType)
                    .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
        } else {
            materials = materialMapper.selectList(Wrappers.<Material>lambdaQuery()
                    .eq(Material::getProductNo, productNo)
                    .eq(Material::getProductVid, productVid)
                    .eq(Material::getMaterialType, materialType)
                    .eq(Material::getMaterialGroupNo, materialGroupNo)
                    .eq(Material::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
        }

        List<MaterialDto> materialDtoList = new ArrayList<>();

        // 获取商品素材关联图
        materials.forEach(material -> {

            MaterialDto materialDto = new MaterialDto();
            materialDto.setTitle(material.getTitle());
            materialDto.setMaterialNo(material.getMaterialNo().toString());
            materialDto.setMaterialType(material.getMaterialType());
            materialDto.setRemarks(material.getRemarks());
            materialDto.setSource(material.getSource());

            // 用于查找模板的材料id
            Integer materialId = material.getId();

            if (material.getSource() != null && material.getSource().equals(1)) {
                // 来源是材料库时

                // 找到该材料
                Material libraryMaterial = materialMapper.selectOne(Wrappers.<Material>lambdaQuery()
                        .eq(Material::getId, material.getSourceId())
                        .eq(Material::getMaterialLibraryFlag, 1)
                );

                AssertUtils.isNull(libraryMaterial, "未找到对应的材料库材料");

                materialDto.setTitle(libraryMaterial.getTitle());
                materialId = libraryMaterial.getId();
            } else {
                // 手动填写
                materialId = material.getId();
            }

            List<MaterialImageDto> materialImageDtoList = new ArrayList<>();

            List<MaterialImageRelation> materialImageRelationList = materialImageRelationMapper.selectList(Wrappers.<MaterialImageRelation>lambdaQuery()
                    .eq(MaterialImageRelation::getMaterialId, materialId)
                    .eq(MaterialImageRelation::getIsDelete, TrueFalseEnum.FALSE.getCode())
                    .orderByAsc(MaterialImageRelation::getSort)
            );

            materialImageRelationList.forEach(materialImageRelation -> {
                MaterialImageDto materialImageDto = new MaterialImageDto();
                materialImageDto.setFileName(materialImageRelation.getFileName());
                materialImageDto.setFileNo(materialImageRelation.getFileNo().toString());

                // 获取文件数据
                MaterialImage materialImage = materialImageMapper.selectOne(Wrappers.<MaterialImage>lambdaQuery()
                        .eq(MaterialImage::getFileNo, materialImageRelation.getFileNo())
                        .eq(MaterialImage::getIsDelete, TrueFalseEnum.FALSE.getCode())
                );
                if (materialImage != null) {
                    String watermarkedFilePath = watermarkImageExternalService.getWatermarkedFilePath(watermarkImageNo, materialImage.getFileNo(), materialImage.getTemplateType());

                    materialImageDto.setFilePath(watermarkedFilePath);
                    materialImageDto.setFileUrl(FileBaseUtil.getFileUrl(watermarkedFilePath));
                    materialImageDto.setTemplateType(materialImage.getTemplateType());
                }

                materialImageDtoList.add(materialImageDto);
            });

            materialDto.setMaterialImageList(materialImageDtoList);
            materialDtoList.add(materialDto);
        });

        return materialDtoList;
    }


    private List<MaterialVo> materialDtoToMaterialVo(List<MaterialDto> MaterialDtoList) {

        // 传入不能为空
        AssertUtils.isNull(MaterialDtoList, "传入的材料列表不能为空");

        List<MaterialVo> materialVoList = new ArrayList<>();
        for (MaterialDto materialDto : MaterialDtoList) {
            MaterialVo materialVo = new MaterialVo();
            materialVo.setMaterialNo(materialDto.getMaterialNo());
            materialVo.setTitle(materialDto.getTitle());
            materialVo.setRemarks(materialDto.getRemarks());
            materialVo.setSource(materialDto.getSource());

            List<MaterialImageVo> materialImageList = new ArrayList<>();

            // 非库材料时才查找模板图
            if (materialDto.getSource() == null || materialDto.getSource().equals(0)) {
                for (MaterialImageDto materialImageDto : materialDto.getMaterialImageList()) {
                    MaterialImageVo materialImageVo = new MaterialImageVo();
                    materialImageVo.setFileName(materialImageDto.getFileName());
                    materialImageVo.setFileNo(materialImageDto.getFileNo());
                    materialImageList.add(materialImageVo);
                }
            }

            materialVo.setMaterialImageList(materialImageList);

            materialVoList.add(materialVo);
        }

        return materialVoList;
    }

    /**
     * 将ProductInfoDto转换为ProductVo
     *
     * @param productInfoDto 商品信息DTO
     * @return ProductVo 商品参数对象
     */
    public ProductVo convertProductInfoDtoToProductVo(ProductInfoDto productInfoDto) {
        if (productInfoDto == null) {
            return null;
        }

        ProductVo vo = new ProductVo();

        // 基本信息
        vo.setProductNo(productInfoDto.getProductNo());
        vo.setSourceNo(productInfoDto.getSourceNo());
        vo.setName(productInfoDto.getName());
        vo.setDescription(productInfoDto.getDescription());
        vo.setNotes(productInfoDto.getNotes());
        vo.setFeeDescription(productInfoDto.getFeeDescription());
        vo.setDiscountDescription(productInfoDto.getDiscountDescription());
        vo.setTermsOfService(productInfoDto.getTermsOfService());
        vo.setRelatedBusinessNo(productInfoDto.getRelatedBusinessNo());
        vo.setRelatedChildBusinessNo(productInfoDto.getRelatedChildBusinessNo());

        // 价格信息
        vo.setPrice(productInfoDto.getPrice());
        vo.setPlatformDeliveryPrice(productInfoDto.getPlatformDeliveryPrice());


        // 分类和状态信息
        vo.setCategory(productInfoDto.getCategory());
        vo.setEducationalStage(productInfoDto.getEducationalStage());
        vo.setDistrictId(productInfoDto.getDistrictId());
        vo.setSecondaryCategory(productInfoDto.getSecondaryCategory());
        vo.setAvailabilityStatus(productInfoDto.getAvailabilityStatus());

        // 关键词和文案人员
        vo.setKeywordIds(productInfoDto.getKeywordIds());
        vo.setCopywriterList(productInfoDto.getCopywriterList());

        // 封面和校徽
        vo.setCover(productInfoDto.getCover());
        vo.setSchoolLogo(productInfoDto.getSchoolLogo());

        // 库存和开关信息 - 不设置促销相关的库存和开关
        vo.setStock(productInfoDto.getStock());
        vo.setStockOpenFlag(productInfoDto.getStockOpenFlag());
        // 促销相关的开关和库存由B端机构自己配置
        // vo.setPromoOpenFlag(null);
        // vo.setMultipleDiscountFlag(null);
        // vo.setCouponGiftFlag(null);
        // vo.setPromoStock(null);

        // 材料列表转换
        if (productInfoDto.getRequiredMaterialList() != null) {
            vo.setRequiredMaterialList(materialDtoToMaterialVo(productInfoDto.getRequiredMaterialList()));
        }

        if (productInfoDto.getOptionalMaterialList() != null) {
            vo.setOptionalMaterialList(materialDtoToMaterialVo(productInfoDto.getOptionalMaterialList()));
        }

        // 分组材料转换
        if (productInfoDto.getMaterialGroupList() != null) {
            List<MaterialGroupVo> materialGroupList = new ArrayList<>();
            for (MaterialGroupDto materialGroupDto : productInfoDto.getMaterialGroupList()) {
                MaterialGroupVo materialGroupVo = new MaterialGroupVo();
                materialGroupVo.setGroupName(materialGroupDto.getGroupName());
                materialGroupVo.setMaterialGroupNo(materialGroupDto.getMaterialGroupNo());
                materialGroupVo.setMaterialList(materialDtoToMaterialVo(materialGroupDto.getMaterialList()));
                materialGroupList.add(materialGroupVo);
            }
            vo.setMaterialGroupList(materialGroupList);
        }

        // 商品素材转换
        if (productInfoDto.getProductBannerList() != null) {
            List<ProductBannerVo> productBannerVoList = new ArrayList<>();
            for (ProductBannerDto productBannerDto : productInfoDto.getProductBannerList()) {
                ProductBannerVo productBannerVo = new ProductBannerVo();
                BeanUtils.copyProperties(productBannerDto, productBannerVo);
                productBannerVoList.add(productBannerVo);
            }
            vo.setProductBannerList(productBannerVoList);
        }

        // PDF列表转换
        if (productInfoDto.getPdfList() != null) {
            List<ProductPdfVo> productPdfVoList = new ArrayList<>();
            for (ProductPdfDto productPdfDto : productInfoDto.getPdfList()) {
                ProductPdfVo productPdfVo = new ProductPdfVo();
                BeanUtils.copyProperties(productPdfDto, productPdfVo);
                productPdfVoList.add(productPdfVo);
            }
            vo.setPdfList(productPdfVoList);
        }

        // 不设置优惠券列表，由B端机构自己配置
        // vo.setCouponList(new ArrayList<>());

        // 设置默认值（根据ProductVo的@NotNull注解要求）
        if (vo.getRequiredMaterialList() == null) {
            vo.setRequiredMaterialList(new ArrayList<>());
        }
        if (vo.getOptionalMaterialList() == null) {
            vo.setOptionalMaterialList(new ArrayList<>());
        }
        if (vo.getMaterialGroupList() == null) {
            vo.setMaterialGroupList(new ArrayList<>());
        }
        if (vo.getCopywriterList() == null) {
            vo.setCopywriterList(new ArrayList<>());
        }
        if (vo.getProductBannerList() == null) {
            vo.setProductBannerList(new ArrayList<>());
        }
        if (vo.getPdfList() == null) {
            vo.setPdfList(new ArrayList<>());
        }
        if (vo.getCouponList() == null) {
            vo.setCouponList(new ArrayList<>());
        }

        return vo;
    }


    /**
     * 同步修改商品信息
     *
     * @param vo              商品信息
     * @param userLoginInfoEo 用户登录信息
     * @return
     */
    public String synchronizaUpdateProduct(ProductVo vo, UserLoginInfoEo userLoginInfoEo) {

        AssertUtils.isTrue(StringUtils.isBlank(vo.getProductNo()), "商品编号不能为空");

        // 找出商品信息
        Product product = productMapper.selectOne(Wrappers.<Product>lambdaQuery()
                .eq(Product::getProductNo, vo.getProductNo())
                .eq(Product::getOrgId, userLoginInfoEo.getOrgId())
                .eq(Product::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isNull(product, "商品不存在");

        Integer oldProductVid = product.getProductVid();

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(2)) && (vo.getDistrictId() != null && vo.getDistrictId() > 0)) {
            DistrictInfo districtInfo = districtInfoMapper.selectOne(Wrappers.<DistrictInfo>lambdaQuery()
                    .eq(DistrictInfo::getId, vo.getDistrictId())
                    .eq(DistrictInfo::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(districtInfo, "地区不存在或者已被删除");
        }

        if ((vo.getCategory().equals(1) || vo.getCategory().equals(3)) && (vo.getSecondaryCategory() != null && vo.getSecondaryCategory() > 0)) {
            ProductSecondaryCategory productSecondaryCategory = productSecondaryCategoryMapper.selectOne(Wrappers.<ProductSecondaryCategory>lambdaQuery()
                    .eq(ProductSecondaryCategory::getId, vo.getSecondaryCategory())
                    .eq(ProductSecondaryCategory::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );
            AssertUtils.isNull(productSecondaryCategory, "二级分类不存在或者已被删除");
        }

        // 处理上下架
        if (vo.getAvailabilityStatus() != null) {
            Integer oldAvailabilityStatus = product.getAvailabilityStatus();
            if (!oldAvailabilityStatus.equals(vo.getAvailabilityStatus())) {
                product.setAvailabilityStatus(vo.getAvailabilityStatus());

                // 添加日志
                ProductLogEo productLogEo = new ProductLogEo();
                productLogEo.setActionCode(105);
                productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
                productLogEo.setOldValue(oldAvailabilityStatus.toString());
                productLogEo.setNewValue(product.getAvailabilityStatus().toString());
                productLogExternalService.addProductLog(product.getProductNo(), productLogEo);
            }
        }


        Integer productVid = fillProductData(vo, product, userLoginInfoEo, true);
        product.setProductVid(productVid);
        productMapper.updateById(product);

        // 添加日志
        ProductLogEo productLogEo = new ProductLogEo();
        productLogEo.setActionCode(102);
        productLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        productLogEo.setOldValue(oldProductVid.toString());
        productLogEo.setNewValue(productVid.toString());
        productLogExternalService.addProductLog(product.getProductNo(), productLogEo);

        return product.getProductNo().toString();

    }
}
