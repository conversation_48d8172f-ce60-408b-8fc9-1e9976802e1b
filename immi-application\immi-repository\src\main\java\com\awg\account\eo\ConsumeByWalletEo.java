package com.awg.account.eo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * <b>ConsumeByWalletEo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16
 */

@Data
@ApiModel(value = "使用钱包消费参数")
public class ConsumeByWalletEo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "钱包抵扣金额", required = true)
    @NotNull(message = "钱包抵抗金额不能为空")
    private BigDecimal walletDeduction;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "钱包编号", required = true)
    @NotBlank(message = "钱包编号不能为空")
    private String walletNo;

    @ApiModelProperty(value = "会员id", required = true)
    @NotNull(message = "会员id不能为空")
    private Integer memberId;

    @ApiModelProperty(value = "订单编号", required = true)
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;
}
