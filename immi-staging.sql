/*
 Navicat Premium Data Transfer

 Source Server         : 公司测试环境
 Source Server Type    : MySQL
 Source Server Version : 50740
 Source Host           : **************:3306
 Source Schema         : immi-staging

 Target Server Type    : MySQL
 Target Server Version : 50740
 File Encoding         : 65001

 Date: 14/07/2025 17:47:40
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account_wallet
-- ----------------------------
DROP TABLE IF EXISTS `account_wallet`;
CREATE TABLE `account_wallet`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '持有人id（会员id）',
  `balance` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `available_balance` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '可用余额',
  `pending_balance` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '待到账金额',
  `wallet_no` bigint(20) UNSIGNED NOT NULL COMMENT '钱包编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `wallet_no`(`wallet_no`) USING BTREE,
  UNIQUE INDEX `member_id_is_delete`(`member_id`, `is_delete`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '钱包表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for account_wallet_transaction
-- ----------------------------
DROP TABLE IF EXISTS `account_wallet_transaction`;
CREATE TABLE `account_wallet_transaction`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wallet_no` bigint(20) UNSIGNED NOT NULL COMMENT '钱包编号',
  `transaction_no` bigint(20) NOT NULL COMMENT '钱包交易编号(流水编号)',
  `order_no` bigint(20) NULL DEFAULT NULL COMMENT '关联订单编号',
  `action_code` int(11) NOT NULL COMMENT '操作代码（501=后台手动增减钱包，201=购买商品使用钱包，401=订单返佣，402=订单返现(自己)，405=订单返佣作废，406=订单返现(自己)作废，601=订单退还，301=订单返佣金额到账，302=订单返现金额到账，701注册赠送）',
  `action_category` int(11) UNSIGNED NOT NULL COMMENT '操作类目（1=充值，2=消费，3=金额到账，4=返佣/返现，5=手动操作，6=退还，7=系统操作）',
  `balance_change` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '余额变化值',
  `available_balance_change` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '可用余额变化值',
  `pending_balance_change` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '待到账金额变化值',
  `remarks` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `balance_snapshot` decimal(12, 2) NOT NULL COMMENT '余额快照',
  `available_balance_snapshot` decimal(12, 2) NOT NULL COMMENT '可用余额快照',
  `pending_balance_snapshot` decimal(12, 2) NOT NULL COMMENT '待到账金额快照',
  `operator_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `operator_type` int(11) NOT NULL DEFAULT 0 COMMENT '操作人员类型（0系统，1后台管理员，2app会员）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `wallet_transaction_no`(`transaction_no`) USING BTREE,
  INDEX `wallet_no`(`wallet_no`) USING BTREE,
  INDEX `order_no`(`order_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '钱包交易流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bp_business
-- ----------------------------
DROP TABLE IF EXISTS `bp_business`;
CREATE TABLE `bp_business`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `parent_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父编号',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '层级',
  `owner_type` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属类型（0: 中介，1平台，2-B端）',
  `from_platform_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否来自平台',
  `type_id` int(11) NOT NULL COMMENT '业务类型id',
  `business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `parent_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属业务编号【已废弃】',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用（0:否，1:是）',
  `display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '展示标志[0不展示，1展示]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  `deleted_id` int(11) NOT NULL DEFAULT 0 COMMENT '删除id，未删除为0，用于软删除的唯一索引',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `org_id_business_no_deleted_id`(`org_id`, `business_no`, `deleted_id`) USING BTREE,
  INDEX `type_id`(`type_id`) USING BTREE,
  INDEX `owner_type`(`owner_type`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `order`(`order`) USING BTREE,
  INDEX `parent_business_no`(`parent_business_no`) USING BTREE,
  INDEX `business_no`(`business_no`) USING BTREE,
  INDEX `level`(`level`) USING BTREE,
  INDEX `parent_no`(`parent_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1574 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bp_business_process
-- ----------------------------
DROP TABLE IF EXISTS `bp_business_process`;
CREATE TABLE `bp_business_process`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL DEFAULT 0 COMMENT '机构id',
  `process_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '流程编号',
  `name` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `process_no`(`process_no`) USING BTREE,
  INDEX `business_no`(`business_no`) USING BTREE,
  INDEX `order`(`order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务流程表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bp_business_type
-- ----------------------------
DROP TABLE IF EXISTS `bp_business_type`;
CREATE TABLE `bp_business_type`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_assessment_condition
-- ----------------------------
DROP TABLE IF EXISTS `client_assessment_condition`;
CREATE TABLE `client_assessment_condition`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL COMMENT '类型（0=邀请会员，1=消费金额，2=下级消费金额）',
  `assessment_record_id` int(11) NOT NULL COMMENT '考核记录表id',
  `related_content` bigint(20) NOT NULL COMMENT '关联内容，是邀请会员id或者订单编号',
  `completion_amount` decimal(15, 2) NOT NULL COMMENT '完成金额，type为1时有效',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `assessment_record_id`(`assessment_record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员等级考核条件达成的详细信息，记录邀请的每一个人和订单金额' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_business_identity
-- ----------------------------
DROP TABLE IF EXISTS `client_business_identity`;
CREATE TABLE `client_business_identity`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `business_name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业名称',
  `contact_name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `registration_time` int(11) NOT NULL COMMENT '注册时间',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用（0:否，1:是）',
  `c_upgrade_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '由C端升级而成',
  `level` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '等级',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `member_id_is_delete`(`member_id`, `is_delete`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `level`(`level`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'B端身份表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_business_invite
-- ----------------------------
DROP TABLE IF EXISTS `client_business_invite`;
CREATE TABLE `client_business_invite`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '被邀请人的id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `inviter_id` bigint(20) NOT NULL COMMENT '邀请人id，user_info_id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'B端邀请关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_business_level_config
-- ----------------------------
DROP TABLE IF EXISTS `client_business_level_config`;
CREATE TABLE `client_business_level_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) UNSIGNED NOT NULL COMMENT '等级',
  `qualified_invitees` int(11) NOT NULL COMMENT '达标邀请人数',
  `qualified_amount` decimal(15, 2) NOT NULL COMMENT '达标消费金额-固定加币',
  `commission_percentage` decimal(12, 2) UNSIGNED NOT NULL COMMENT '返佣百分比',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'B端等级配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_candidate_member
-- ----------------------------
DROP TABLE IF EXISTS `client_candidate_member`;
CREATE TABLE `client_candidate_member`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `invite_code` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请码',
  `openid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预备成员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_consumer_identity
-- ----------------------------
DROP TABLE IF EXISTS `client_consumer_identity`;
CREATE TABLE `client_consumer_identity`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `registration_time` int(11) NOT NULL COMMENT '注册时间',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用（0:否，1:是）',
  `level` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '等级',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `member_id_is_delete`(`member_id`, `is_delete`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `level`(`level`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'C端身份表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_consumer_invite
-- ----------------------------
DROP TABLE IF EXISTS `client_consumer_invite`;
CREATE TABLE `client_consumer_invite`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `inviter_id` bigint(20) NOT NULL COMMENT '邀请人id',
  `inviter_type` tinyint(4) NOT NULL COMMENT '邀请人类型（3:员工，5:member会员）',
  `member_id` int(11) NOT NULL COMMENT '被邀请人的id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'C端邀请码关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_consumer_level_config
-- ----------------------------
DROP TABLE IF EXISTS `client_consumer_level_config`;
CREATE TABLE `client_consumer_level_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) UNSIGNED NOT NULL COMMENT '等级',
  `qualified_invitees` int(11) NOT NULL COMMENT '达标邀请人数',
  `commission_percentage` decimal(12, 2) UNSIGNED NOT NULL COMMENT '返佣百分比',
  `self_commission_percentage` decimal(12, 2) UNSIGNED NOT NULL COMMENT '自身返佣百分比',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'C端等级配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_invite_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `client_invite_snapshot`;
CREATE TABLE `client_invite_snapshot`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关系内容，每个上线用逗号分隔，格式：类型(1B端, 2C端, 3员工)-等级-id, 类型-等级-id...',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邀请关系快照表，记录邀请时，整个关系链上每个人身份类型和等级' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_level_assessment_record
-- ----------------------------
DROP TABLE IF EXISTS `client_level_assessment_record`;
CREATE TABLE `client_level_assessment_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `start_time` int(11) NOT NULL COMMENT '开始日期',
  `end_time` int(11) NOT NULL COMMENT '结束日期',
  `level` int(10) UNSIGNED NOT NULL COMMENT '等级',
  `target_level` int(10) UNSIGNED NOT NULL COMMENT '目标等级',
  `status` int(10) NOT NULL DEFAULT 0 COMMENT '状态（0=未达到，1=已达到）',
  `achievement_time` int(11) NULL DEFAULT NULL COMMENT '达成时间',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `is_delete`(`is_delete`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'B端等级考核记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_member
-- ----------------------------
DROP TABLE IF EXISTS `client_member`;
CREATE TABLE `client_member`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `country_code` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电话区号',
  `phone` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电话',
  `member_no` bigint(20) NOT NULL COMMENT '会员编号',
  `registration_time` int(11) NOT NULL COMMENT '注册时间',
  `frozen_flag` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否冻结（0:否，1:是）',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用（0:否，1:是）',
  `cancelled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否注销（0:否，1:是）',
  `invite_button_clicked` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '邀请按钮是否已点击',
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `nickname` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `member_no`(`member_no`) USING BTREE,
  UNIQUE INDEX `org_id_country_code_phone_is_delete`(`org_id`, `country_code`, `phone`, `is_delete`) USING BTREE,
  INDEX `frozen_flag`(`frozen_flag`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE,
  INDEX `cancelled_flag`(`cancelled_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '成员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_member_invite_code
-- ----------------------------
DROP TABLE IF EXISTS `client_member_invite_code`;
CREATE TABLE `client_member_invite_code`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inviter_id` bigint(20) NOT NULL COMMENT '邀请人id',
  `inviter_type` tinyint(4) NOT NULL COMMENT '邀请人类型（1:B端，2:C端，3:员工）【目前B端和C端效果是一样的，会自动判断用哪个身份】',
  `type` tinyint(4) NOT NULL COMMENT '邀请类型（邀请到哪个端）（1: B端，2:C端）',
  `invite_code` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请码',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `invite_code`(`invite_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '成员邀请码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_member_invite_code_record
-- ----------------------------
DROP TABLE IF EXISTS `client_member_invite_code_record`;
CREATE TABLE `client_member_invite_code_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '成员id',
  `invite_code` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请码',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邀请码使用记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_member_log
-- ----------------------------
DROP TABLE IF EXISTS `client_member_log`;
CREATE TABLE `client_member_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `action_code` int(11) NOT NULL COMMENT '操作行为代码（301开通B端身份，302开通C端身份，303移除B端身份，304移除C端身份，305恢复B端身份，306恢复C端身份，601注册，602小程序登录绑定，603登录，605更新小程序绑定，606退出登录，607 更改会员信息，701更改B端的邀请关系，702更改C端的邀请关系，307=B端等级改变，308=C端等级改变）',
  `remarks` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `operator_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `operator_type` int(11) NOT NULL DEFAULT 0 COMMENT '操作人员类型（0系统，1后台管理员，2app会员）',
  `old_value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '旧值',
  `new_value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '新值',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `member_no`(`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 182 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '成员日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for client_weapp_member_association
-- ----------------------------
DROP TABLE IF EXISTS `client_weapp_member_association`;
CREATE TABLE `client_weapp_member_association`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `authorization_time` int(11) NOT NULL COMMENT '授权时间',
  `openid` char(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `unionid` char(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'unionid',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `openid`(`openid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序人员关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_app_module_config
-- ----------------------------
DROP TABLE IF EXISTS `comm_app_module_config`;
CREATE TABLE `comm_app_module_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `page_id` int(11) NOT NULL DEFAULT 0 COMMENT '页id（0=签证，1=本地服务，2=学校，3=培训）',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `open` tinyint(4) NOT NULL DEFAULT 1 COMMENT '开关[0关，1开]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 245 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序模块配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_commission_info
-- ----------------------------
DROP TABLE IF EXISTS `comm_commission_info`;
CREATE TABLE `comm_commission_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL COMMENT '类型 [1=B端，2=C端]',
  `method` int(11) NOT NULL COMMENT '方式 [1=按百分比，2=固定金额]',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `commission_value` decimal(12, 2) UNSIGNED NOT NULL COMMENT '佣金的值',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `type_product_no_product_vid`(`type`, `product_no`, `product_vid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 314 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '佣金信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_copywriter_assignment_record
-- ----------------------------
DROP TABLE IF EXISTS `comm_copywriter_assignment_record`;
CREATE TABLE `comm_copywriter_assignment_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_info_id` int(11) NOT NULL COMMENT '人员id',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `reuse_last_flag` int(11) NOT NULL DEFAULT 0 COMMENT '是否复用上一次分配的人员，同一商品同一会员的时候会分配同一个人员',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 690 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文案分配记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_copywriter_user
-- ----------------------------
DROP TABLE IF EXISTS `comm_copywriter_user`;
CREATE TABLE `comm_copywriter_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_info_id` int(11) NOT NULL COMMENT '人员id',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3153 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品文案人员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_district_info
-- ----------------------------
DROP TABLE IF EXISTS `comm_district_info`;
CREATE TABLE `comm_district_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(270) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `pid` int(11) NOT NULL DEFAULT 0,
  `region_code` char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '国家/地区代码',
  `order` int(11) NOT NULL DEFAULT 0,
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `from_platform_id` int(11) NOT NULL DEFAULT 0 COMMENT '来自平台的id，0表示不来自平台',
  `display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '展示标志[0不展示，1展示]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1843 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品地区信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_material
-- ----------------------------
DROP TABLE IF EXISTS `comm_material`;
CREATE TABLE `comm_material`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NULL DEFAULT NULL COMMENT '商品编号',
  `product_vid` int(11) NULL DEFAULT NULL COMMENT '商品版本id',
  `material_no` bigint(20) NOT NULL COMMENT '材料编号',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `material_group_no` bigint(20) NULL DEFAULT NULL COMMENT '材料分组编号（材料类型为1时有效）',
  `material_type` int(11) NOT NULL DEFAULT 0 COMMENT '材料类型（0=必须材料，1=N选M材料，2=非必须材料）',
  `source` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源（0=用户填写，1=素材库素材）',
  `source_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源id，来源为材料库的材料时，该id是材料的id',
  `library_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '材料库里专用的库id，目前只在材料库列表显示，没有其他作用',
  `material_library_flag` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '素材库素材标志',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，操作材料库材料时有效',
  `title` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `remarks` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `material_no`(`material_no`) USING BTREE,
  INDEX `product_no`(`product_no`) USING BTREE,
  INDEX `product_vid`(`product_vid`) USING BTREE,
  INDEX `material_library_flag`(`material_library_flag`) USING BTREE,
  INDEX `is_delete`(`is_delete`) USING BTREE,
  INDEX `source_id`(`source_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5235 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品材料表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_material_group
-- ----------------------------
DROP TABLE IF EXISTS `comm_material_group`;
CREATE TABLE `comm_material_group`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `material_group_no` bigint(20) NOT NULL COMMENT '材料分组编号',
  `group_name` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_no`(`product_no`) USING BTREE,
  INDEX `product_vid`(`product_vid`) USING BTREE,
  INDEX `material_group_no`(`material_group_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 717 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品材料分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_material_image
-- ----------------------------
DROP TABLE IF EXISTS `comm_material_image`;
CREATE TABLE `comm_material_image`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径(打水印之后)',
  `template_type` int(11) NOT NULL DEFAULT 0 COMMENT '材料模板类型（0=图片，1=pdf）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `file_no`(`file_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1025 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '材料模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_material_image_relation
-- ----------------------------
DROP TABLE IF EXISTS `comm_material_image_relation`;
CREATE TABLE `comm_material_image_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `material_id` int(11) NOT NULL COMMENT '材料id，对应material表的id',
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `file_name` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件名称',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `file_no`(`file_no`) USING BTREE,
  INDEX `material_id`(`material_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6411 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '材料和材料模板关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product
-- ----------------------------
DROP TABLE IF EXISTS `comm_product`;
CREATE TABLE `comm_product`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `short_code` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短码',
  `availability_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '可用状态(0=未上架，1=已上架)',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `category` int(11) NOT NULL DEFAULT 1 COMMENT '分类（1=签证类，2=申校类，3=本地服务）',
  `resettable_stock` int(11) NOT NULL DEFAULT 0 COMMENT '可重置库存',
  `initial_stock` int(11) NOT NULL DEFAULT 0 COMMENT '初始库存-用于重置库存',
  `promo_stock` int(11) NULL DEFAULT NULL COMMENT '促销库存',
  `stock_open_flag` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存开启标志',
  `promo_open_flag` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '促销开启标志',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `product_no`(`product_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 249 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_banner
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_banner`;
CREATE TABLE `comm_product_banner`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '类型（0图片，1视频）',
  `description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `upload_record_no` bigint(20) NULL DEFAULT NULL COMMENT '上传记录编号',
  `vid` char(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频vid',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1622 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品banner表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_data
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_data`;
CREATE TABLE `comm_product_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `source_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '来源编号',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `category` int(11) NOT NULL DEFAULT 1 COMMENT '分类（1=签证类，2=申校类）',
  `sales_consultation_type` int(11) NOT NULL DEFAULT 0 COMMENT '销售咨询类型（0=购买，1=咨询，2=无）',
  `secondary_category` int(11) NOT NULL DEFAULT 0 COMMENT '二级分类id',
  `educational_stage` int(11) NULL DEFAULT NULL COMMENT '教育阶段（1=中小学，2=college，3=本科，4=研究生）',
  `district_id` int(11) NULL DEFAULT NULL COMMENT '地区id',
  `name` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `school_logo` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '学校logo',
  `cover` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `cover_width` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品封面图宽度',
  `cover_height` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品封面图高度',
  `purchase_button_text` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '立即购买' COMMENT '购买按钮文案',
  `promotion_button_text` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '立即抢购' COMMENT '促销按钮文案',
  `consultation_button_text` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '立即咨询' COMMENT '咨询按钮文案',
  `consultant_wechat` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '咨询顾问微信',
  `consultant_qrcode` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '咨询顾问二维码',
  `keyword_ids` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词id列表',
  `price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '价格，0表示免费',
  `platform_delivery_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '平台交付价格',
  `promo_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '促销价格，0表示免费，null表示不支持',
  `second_discount_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '第二件优惠价',
  `third_discount_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '第三件优惠价',
  `related_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联业务编号',
  `related_child_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联的子级业务编号',
  `multiple_discount_flag` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '多个购买优惠开关',
  `coupon_gift_flag` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '赠送优惠券开关',
  `source_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源类型，0=自建，1=平台',
  `delivery_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '交付类型，0=自己交付，1=平台交付，2=导入编辑',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `fee_description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '费用描述',
  `discount_description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠说明',
  `notes` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '注意事项',
  `terms_of_service` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务条款',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `product_no_product_vid`(`product_no`, `product_vid`) USING BTREE,
  INDEX `product_no`(`product_no`) USING BTREE,
  INDEX `product_vid`(`product_vid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2371 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_editor_relation
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_editor_relation`;
CREATE TABLE `comm_product_editor_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_info_id` int(11) NOT NULL COMMENT '人员id',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品-编辑人员-关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_gift_coupon_relation
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_gift_coupon_relation`;
CREATE TABLE `comm_product_gift_coupon_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `coupon_template_no` bigint(20) NOT NULL COMMENT '优惠券模板编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 917 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品赠送优惠券-关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_keyword
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_keyword`;
CREATE TABLE `comm_product_keyword`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `from_platform_id` int(11) NOT NULL DEFAULT 0 COMMENT '来自平台的id，0表示不来自平台',
  `display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '展示标志[0不展示，1展示]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 144 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品关键词表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_log
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_log`;
CREATE TABLE `comm_product_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `action_code` int(11) NOT NULL COMMENT '操作行为代码（101添加商品，102编辑商品，103删除商品，105上下架，106更改商品字段）',
  `remarks` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `old_value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '旧值',
  `new_value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '新值',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_no`(`product_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4444 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_pdf
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_pdf`;
CREATE TABLE `comm_product_pdf`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 845 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品pdf表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comm_product_secondary_category
-- ----------------------------
DROP TABLE IF EXISTS `comm_product_secondary_category`;
CREATE TABLE `comm_product_secondary_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` int(11) NOT NULL COMMENT '分类（1=签证类，2=申校类，3=本地服务）',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `from_platform_id` int(11) NOT NULL DEFAULT 0 COMMENT '来自平台的id，0表示不来自平台',
  `display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '展示标志[0不展示，1展示]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 539 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品二级分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_allocation_record
-- ----------------------------
DROP TABLE IF EXISTS `crm_allocation_record`;
CREATE TABLE `crm_allocation_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '线索id',
  `rule_no` int(11) NOT NULL DEFAULT 0 COMMENT '分配规则编号',
  `channel_id` int(11) NOT NULL COMMENT '渠道id，user_info_id',
  `area_id` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地区id，通过多个id形式组合出来的id',
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `sales_id` int(11) NOT NULL DEFAULT 0 COMMENT '销售id，user_info_id，没有匹配到则为0',
  `remarks` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0开始，1成功，2失败）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `rule_no`(`rule_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 108 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分配记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_allocation_rule
-- ----------------------------
DROP TABLE IF EXISTS `crm_allocation_rule`;
CREATE TABLE `crm_allocation_rule`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_no` int(11) NOT NULL COMMENT '分配规则编号',
  `channel_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道列表，user_info_id列表，用逗号分隔',
  `area_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区列表，地区id列表，用逗号分隔',
  `questionnaire_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号列表，用逗号分隔',
  `sales_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '销售列表，user_info_id列表，用逗号分隔',
  `previous_sales_id` int(11) NOT NULL DEFAULT 0 COMMENT '上次分配的销售id，用于轮询分配',
  `open_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '规则开关',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `rule_no`(`rule_no`) USING BTREE,
  INDEX `previous_sales_id`(`previous_sales_id`) USING BTREE,
  INDEX `open_flag`(`open_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 982 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分配规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_attachment
-- ----------------------------
DROP TABLE IF EXISTS `crm_attachment`;
CREATE TABLE `crm_attachment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '类型（0=普通附件，1=合同附件）',
  `path` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径，相对路径',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件名称',
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `client_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户业务编号',
  `user_info_id` int(11) NULL DEFAULT NULL COMMENT '上传人的user_info_id，是用户在单个组织名下的id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `client_business_no`(`client_business_no`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 527 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_client_business
-- ----------------------------
DROP TABLE IF EXISTS `crm_client_business`;
CREATE TABLE `crm_client_business`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `sales_person_id` int(11) NULL DEFAULT NULL COMMENT '销售人员（user_info_id）',
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `client_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户业务编号',
  `type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '类型（0=无合同业务，1=有合同业务）',
  `amount_value` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '金额值',
  `amount_unit` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '金额单位',
  `region` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CA' COMMENT '区域（国家/地区）',
  `initiator_id` int(11) NOT NULL DEFAULT 0 COMMENT '发起人',
  `deal_maker_id` int(11) NOT NULL DEFAULT 0 COMMENT '成交人id[未使用]',
  `signing_time` datetime(0) NULL DEFAULT NULL COMMENT '签署时间',
  `contract_attachment_id` int(11) NOT NULL DEFAULT 0 COMMENT '合同附件id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 746 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户业务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_client_payment_status
-- ----------------------------
DROP TABLE IF EXISTS `crm_client_payment_status`;
CREATE TABLE `crm_client_payment_status`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `amount_value` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '金额值',
  `amount_unit` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '金额单位',
  `payment_date` date NULL DEFAULT NULL COMMENT '发生日期，采用日期格式',
  `payment_time` int(11) NULL DEFAULT NULL COMMENT '发生时间，时间戳形式',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态值（0=待支付，1=定金支付，2=期款支付，3=已完款，4=已退费）',
  `payment_org_id` int(11) NOT NULL DEFAULT 0 COMMENT '收款机构id',
  `tax` int(11) NOT NULL DEFAULT 1 COMMENT '是否含税（0=不含税，1=含5%GST）',
  `client_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户业务编号',
  `payment_method` int(11) NOT NULL DEFAULT 0 COMMENT '支付方式（0=未知/没填写，1=checkpay，2=微信，3=支付宝，4=银行转账，5=EMT，6=现金）',
  `image_path` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片路径',
  `remark` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `payment_account_no` bigint(20) NULL DEFAULT NULL COMMENT '收款账户编号',
  `related_process_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '流程编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `collection_org_id`(`payment_org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 681 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户支付记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_client_process_status
-- ----------------------------
DROP TABLE IF EXISTS `crm_client_process_status`;
CREATE TABLE `crm_client_process_status`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `process_date` date NULL DEFAULT NULL COMMENT '流程的发生日期，采用字符串日期格式',
  `process_time` int(11) NOT NULL COMMENT '流程的发生时间，时间戳形式',
  `process_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '流程编号',
  `client_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户业务编号',
  `business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `image_path` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片路径',
  `process_text` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '步骤-文本',
  `planned_receipt` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '计划收款金额值',
  `planned_receipt_unit` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划收款金额单位',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `process_no`(`process_no`) USING BTREE,
  INDEX `business_no`(`business_no`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `client_business_no`(`client_business_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 832 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户进程状态' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_field_role_relation
-- ----------------------------
DROP TABLE IF EXISTS `crm_field_role_relation`;
CREATE TABLE `crm_field_role_relation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `field_code` int(11) NOT NULL COMMENT '类型(0=渠道 1=主管  2=文案  3=顾问  4=持牌 )',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `role_id`(`role_id`) USING BTREE,
  INDEX `field_code`(`field_code`) USING BTREE,
  INDEX `is_delete`(`is_delete`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字段-角色-关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_follow_up_notice_config
-- ----------------------------
DROP TABLE IF EXISTS `crm_follow_up_notice_config`;
CREATE TABLE `crm_follow_up_notice_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` int(11) NOT NULL COMMENT '潜客状态（0=有效潜客，1=已付费，2=进程中，3=已完结，4=已放弃，5=已退费，6=待审核，7=已审核，8=已递交）',
  `org_id` int(11) NOT NULL COMMENT '当前跟进机构id',
  `checked` int(11) NOT NULL DEFAULT 0,
  `default_days` int(11) NULL DEFAULT NULL,
  `s_days` int(11) NULL DEFAULT NULL,
  `a_days` int(11) NULL DEFAULT NULL,
  `b_days` int(11) NULL DEFAULT NULL,
  `c_days` int(11) NULL DEFAULT NULL,
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `status_org_id`(`status`, `org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '跟进通知配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads`;
CREATE TABLE `crm_leads`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '潜客状态（0=有效潜客，1=已付费，2=进程中，3=已完结，4=已放弃，5=已退费，6=待审核，7=已审核，8=已递交）',
  `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `planned_follow_up_date` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划跟进日期，采用字符串日期格式',
  `planned_follow_up_time` int(11) NULL DEFAULT NULL COMMENT '计划跟进时间，时间戳形式',
  `log_last_follow_up_time` int(11) NOT NULL DEFAULT 0 COMMENT '日志中的最后跟进时间',
  `follow_up_content` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跟进内容',
  `org_id` int(11) NOT NULL COMMENT '当前跟进机构id',
  `org_leads_id` int(11) NULL DEFAULT NULL COMMENT '机构下的leadsId',
  `user_info_id` int(11) NULL DEFAULT NULL COMMENT '邀请的user_info_id，是用户在单个组织名下的id',
  `created_by_user` int(11) NOT NULL DEFAULT 0 COMMENT '建表人的user_info_id',
  `related_member_id` int(11) NULL DEFAULT NULL COMMENT '关联电商会员id',
  `related_order_no` bigint(20) NULL DEFAULT NULL COMMENT '关联电商订单编号',
  `source_id` int(11) NOT NULL DEFAULT 0 COMMENT '来源id',
  `description` varchar(13000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述文本',
  `area_info` varchar(156) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所在地区信息，冗余字段',
  `wechat` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `phone` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `business_nos` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联业务',
  `leads_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '潜客编号',
  `star_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '标星标志（0未标星，1已标星）',
  `intention_level` enum('-','C','B','A','S') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '-' COMMENT '意向等级',
  `auto_assign_execution` tinyint(4) NOT NULL DEFAULT 0 COMMENT '自动分配执行（0未执行，1已执行）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `org_id_org_leads_id`(`org_id`, `org_leads_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `lead_no`(`leads_no`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE,
  INDEX `starFlag`(`star_flag`) USING BTREE,
  INDEX `auto_assign_execution`(`auto_assign_execution`) USING BTREE,
  INDEX `source_id`(`source_id`) USING BTREE,
  INDEX `created_at`(`created_at`) USING BTREE,
  INDEX `is_delete`(`is_delete`) USING BTREE,
  INDEX `org_leads_id`(`org_leads_id`) USING BTREE,
  INDEX `created_by_user`(`created_by_user`) USING BTREE,
  INDEX `log_last_follow_up_time`(`log_last_follow_up_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 911 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_area
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_area`;
CREATE TABLE `crm_leads_area`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `region` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国家/地区信息',
  `district` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大陆城市地区信息',
  `area_id` char(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地区id，采用-拼接形式组成的',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 179 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客地址区域信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_custom_sort
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_custom_sort`;
CREATE TABLE `crm_leads_custom_sort`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `user_info_id` int(11) NOT NULL COMMENT 'user_info_id',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '潜客状态（0=有效潜客，1=已付费，2=进程中，3=已完结，4=已放弃，5=已退费，6=待审核，7=已审核，8=已递交）',
  `org_id` int(11) NOT NULL COMMENT '当前跟进机构id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12908 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客自定义排序表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_government
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_government`;
CREATE TABLE `crm_leads_government`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `fee_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '费用名称',
  `actual_amount` decimal(11, 2) NOT NULL COMMENT '实付金额',
  `currency_unit` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '金额单位',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  `updated_at` int(11) NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '政府费表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crm_leads_government_screenshot
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_government_screenshot`;
CREATE TABLE `crm_leads_government_screenshot`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `crm_leads_government_id` int(11) NOT NULL COMMENT '关联的政府费记录ID',
  `screenshot_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付截图路径',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  `updated_at` int(11) NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '政府费-支付截图表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crm_leads_log
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_log`;
CREATE TABLE `crm_leads_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `action_code` int(11) NOT NULL COMMENT '操作行为代码（101=潜客首次提交问卷，102创建潜客，103添加留言，104删除附件，105添加附件，106新增客户业务，107添加客户业务付款状态，108编辑客户业务付款状态，111添加客户流程状态，112编辑客户流程状态，115删除潜客信息，118编辑客户业务，119删除客户业务，121删除客户支付状态，122删除客户进程状态，123更改计划跟进日期，124更改跟进内容，125更改潜客标题，126更改潜客描述，127更改潜客状态 128分配人员 129移除分配的人员 130修改客户意向等级 131切换星级标志 132删除留言 133更改所属地区id 134=更改潜客关联业务 135更改渠道 136更改潜客标签 137更改潜客来源渠道 139通过电商订单添加潜客 151=编辑潜客进程 152=编辑业务进程步骤 153=删除业务进程步骤 193=编辑微信号，194=编辑手机号  195=增加政府费  196修改政府费  197删除政府费）',
  `related_table_id` int(11) NOT NULL COMMENT '关联表id，用于找到相关的数据',
  `remarks` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `action_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作内容',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧值（有需要才填入，统一使用字符串）',
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新值（有需要才填入，统一使用字符串）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `action_code`(`action_code`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `related_table_id`(`related_table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9199 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_message
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_message`;
CREATE TABLE `crm_leads_message`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '留言类型（0=文本，1=文件，2=富文本）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '留言内容',
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 673 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客留言表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_source
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_source`;
CREATE TABLE `crm_leads_source`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `from_platform_id` int(11) NOT NULL DEFAULT 0 COMMENT '来自平台的id，0表示不来自平台',
  `display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '展示标志[0不展示，1展示]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 249 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客来源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_status_config
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_status_config`;
CREATE TABLE `crm_leads_status_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '潜客状态（0=有效潜客，1=已付费，2=进程中，3=已完结，4=已放弃，5=已退费，6=待审核，7=已审核，8=已递交）',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `open` tinyint(4) NOT NULL DEFAULT 1 COMMENT '开关[0关，1开]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 361 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客状态配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_tag
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_tag`;
CREATE TABLE `crm_leads_tag`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_leads_tag_map
-- ----------------------------
DROP TABLE IF EXISTS `crm_leads_tag_map`;
CREATE TABLE `crm_leads_tag_map`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tag_id` int(11) NOT NULL COMMENT '标签id',
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tag_id`(`tag_id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 386 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客标签关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for crm_payment_account
-- ----------------------------
DROP TABLE IF EXISTS `crm_payment_account`;
CREATE TABLE `crm_payment_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `payment_account_no` bigint(20) NOT NULL COMMENT '收款账户编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 501 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '收款账户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crm_personnel_assignment
-- ----------------------------
DROP TABLE IF EXISTS `crm_personnel_assignment`;
CREATE TABLE `crm_personnel_assignment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '类型(1=主管  2=文案  3=顾问  4=持牌 )',
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `user_info_id` int(11) NOT NULL COMMENT 'user_info_id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `is_delete`(`is_delete`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 935 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '潜客-人员分配表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for file_source_file
-- ----------------------------
DROP TABLE IF EXISTS `file_source_file`;
CREATE TABLE `file_source_file`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '类型（0预留，1水印源文件，3商品视频）',
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `org_id` int(11) NULL DEFAULT NULL COMMENT '机构id',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `file_no`(`file_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1528 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件-源文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for file_watermark_image
-- ----------------------------
DROP TABLE IF EXISTS `file_watermark_image`;
CREATE TABLE `file_watermark_image`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `origin_path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原始图片路径',
  `degree` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '315' COMMENT '角度',
  `watermark_no` bigint(20) NOT NULL COMMENT '水印编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `watermark_no`(`watermark_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '水印图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for file_watermarked_file
-- ----------------------------
DROP TABLE IF EXISTS `file_watermarked_file`;
CREATE TABLE `file_watermarked_file`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `watermark_no` bigint(20) NOT NULL COMMENT '水印编号',
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `watermark_no_file_no`(`watermark_no`, `file_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1616 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '水印后的文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ipaddr_district
-- ----------------------------
DROP TABLE IF EXISTS `ipaddr_district`;
CREATE TABLE `ipaddr_district`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(270) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `parent_id` smallint(5) NULL DEFAULT NULL,
  `suffix` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `code` char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `area_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `order` tinyint(2) NULL DEFAULT NULL,
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7047 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'ip地区信息（中国）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ipaddr_location_record
-- ----------------------------
DROP TABLE IF EXISTS `ipaddr_location_record`;
CREATE TABLE `ipaddr_location_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0未解析，1解析中，2已解析，3解析失败）',
  `matched_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否有匹配项',
  `return_data` varchar(16000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '解析返回结果',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 208 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'IP归属地解析记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ipaddr_region
-- ----------------------------
DROP TABLE IF EXISTS `ipaddr_region`;
CREATE TABLE `ipaddr_region`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(80) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `code` char(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '代码',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 252 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'ip信息，国家和地区数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_answer
-- ----------------------------
DROP TABLE IF EXISTS `kbs_answer`;
CREATE TABLE `kbs_answer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_no` bigint(20) NOT NULL COMMENT '问题编号',
  `answer_no` bigint(20) NOT NULL COMMENT '回答编号',
  `answer_detail_no` bigint(20) NOT NULL COMMENT '回答内容编号，最新的内容',
  `answer_time` int(11) NOT NULL COMMENT '回答时间',
  `user_info_id` int(11) NOT NULL COMMENT '回答人id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 167 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，回答表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_answer_attachment
-- ----------------------------
DROP TABLE IF EXISTS `kbs_answer_attachment`;
CREATE TABLE `kbs_answer_attachment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上传文件uid，前端需要，保存和回显即可',
  `path` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径，相对路径',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件名称',
  `answer_detail_no` bigint(20) NOT NULL COMMENT '回答内容编号',
  `answer_no` bigint(20) NOT NULL COMMENT '回答编号',
  `user_info_id` int(11) NULL DEFAULT NULL COMMENT '上传人的user_info_id，是用户在单个组织名下的id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `answer_detail_no`(`answer_detail_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 546 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，回答附件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_answer_detail
-- ----------------------------
DROP TABLE IF EXISTS `kbs_answer_detail`;
CREATE TABLE `kbs_answer_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `answer_no` bigint(20) NOT NULL COMMENT '回答编号',
  `answer_detail_no` bigint(20) NOT NULL COMMENT '回答内容编号',
  `user_info_id` int(11) NOT NULL COMMENT '回答人id',
  `update_answer_time` int(11) NOT NULL COMMENT '更新回答时间',
  `ip` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容-富文本',
  `content_text` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容-纯文本',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE,
  INDEX `answer_no`(`answer_no`) USING BTREE,
  INDEX `answer_content_no`(`answer_detail_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 446 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，回答的内容表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_answer_image
-- ----------------------------
DROP TABLE IF EXISTS `kbs_answer_image`;
CREATE TABLE `kbs_answer_image`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `answer_detail_no` bigint(20) NOT NULL COMMENT '回答内容编号',
  `answer_no` bigint(20) NOT NULL COMMENT '回答编号',
  `path` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径，相对路径',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，回答的图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_answer_upvote
-- ----------------------------
DROP TABLE IF EXISTS `kbs_answer_upvote`;
CREATE TABLE `kbs_answer_upvote`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `upvoted_time` int(11) NOT NULL COMMENT '点赞时间',
  `answer_no` bigint(20) NOT NULL COMMENT '回答编号',
  `answer_detail_no` bigint(20) NOT NULL COMMENT '回答内容编号',
  `user_info_id` int(11) NOT NULL COMMENT '回答人id',
  `ip` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `answer_detail_no`(`answer_detail_no`) USING BTREE,
  INDEX `answer_no`(`answer_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 67 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，回答点赞表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_category
-- ----------------------------
DROP TABLE IF EXISTS `kbs_category`;
CREATE TABLE `kbs_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '标签层级',
  `parent_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 99 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库问答分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_question
-- ----------------------------
DROP TABLE IF EXISTS `kbs_question`;
CREATE TABLE `kbs_question`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_no` bigint(20) NOT NULL COMMENT '问题编号',
  `title` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `user_info_id` int(11) NOT NULL COMMENT '提问人user_info_id',
  `ip` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `question_time` int(11) NOT NULL COMMENT '提问时间',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_no`(`question_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 244 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，问题表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_question_category
-- ----------------------------
DROP TABLE IF EXISTS `kbs_question_category`;
CREATE TABLE `kbs_question_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_no` bigint(20) NOT NULL COMMENT '问题编号',
  `category_id` int(11) NOT NULL COMMENT '分类id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 322 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库，问题-分类-中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_question_tag
-- ----------------------------
DROP TABLE IF EXISTS `kbs_question_tag`;
CREATE TABLE `kbs_question_tag`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_tag_no` bigint(20) NOT NULL COMMENT '标签编号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库-问题标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for kbs_question_tag_map
-- ----------------------------
DROP TABLE IF EXISTS `kbs_question_tag_map`;
CREATE TABLE `kbs_question_tag_map`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_no` bigint(20) NOT NULL COMMENT '问题编号',
  `question_tag_no` bigint(20) NOT NULL COMMENT '标签编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 122 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库-问题标签一对多关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for op_banner
-- ----------------------------
DROP TABLE IF EXISTS `op_banner`;
CREATE TABLE `op_banner`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `display_target` tinyint(4) NOT NULL DEFAULT 0 COMMENT '展示位置：1=首页签证类，2=首页申校类，3=本地服务',
  `banner_no` bigint(20) NOT NULL COMMENT 'banner编号',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `title` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片路径',
  `display_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '展示标志[0不展示，1展示]',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `link_type` int(11) NOT NULL COMMENT '链接类型（1=商品，2=小程序）',
  `link` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `banner_no`(`banner_no`) USING BTREE,
  INDEX `display_target`(`display_target`) USING BTREE,
  INDEX `display_flag`(`display_flag`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运营-banner表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_commission
-- ----------------------------
DROP TABLE IF EXISTS `ord_commission`;
CREATE TABLE `ord_commission`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `commission_method` int(11) NOT NULL COMMENT '佣金方式 [1=按百分比，2=固定金额]',
  `commission_value` decimal(12, 2) UNSIGNED NOT NULL COMMENT '佣金的值',
  `actual_commission_amount` decimal(12, 2) UNSIGNED NOT NULL COMMENT '实际的返佣金额',
  `recipient_id` int(11) NOT NULL COMMENT '接受人id（佣金所属）',
  `recipient_type` int(11) NOT NULL COMMENT '接受人类型（0=自身，1=B端，2=C端）',
  `level` int(11) NOT NULL COMMENT '层级（第几级返佣）',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0=未结算，1=已结算，3=已作废）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_no`(`order_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单佣金表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_coupon_applicable_product
-- ----------------------------
DROP TABLE IF EXISTS `ord_coupon_applicable_product`;
CREATE TABLE `ord_coupon_applicable_product`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coupon_no` bigint(20) NOT NULL COMMENT '优惠券模板编号或者会员优惠券编号',
  `coupon_template_vid` int(11) NOT NULL COMMENT '优惠券模板版本id',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '类型（0=优惠券模板，1=会员优惠券）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2548 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券适用商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_coupon_template
-- ----------------------------
DROP TABLE IF EXISTS `ord_coupon_template`;
CREATE TABLE `ord_coupon_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coupon_template_no` bigint(20) NOT NULL COMMENT '优惠券模板编号',
  `coupon_template_vid` int(11) NOT NULL COMMENT '模板版本id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `discount_amount` decimal(12, 2) NOT NULL COMMENT '优惠金额',
  `expiry_type` int(11) NOT NULL DEFAULT 0 COMMENT '过期类型（0=多少天内过期，1=固定截至时间过期）',
  `validity_days` int(11) NOT NULL DEFAULT 0 COMMENT '有效天数，过期类型为0时有效',
  `expiry_timestamp` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '过期时间戳，过期类型为1时有效',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_id` int(11) NOT NULL DEFAULT 0 COMMENT '删除id，未删除为0，用于软删除的唯一索引',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `coupon_template_no_coupon_template_vid`(`coupon_template_no`, `coupon_template_vid`) USING BTREE,
  UNIQUE INDEX `coupon_template_no_deleted_id`(`coupon_template_no`, `deleted_id`) USING BTREE,
  INDEX `expiry_type`(`expiry_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 942 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_member_coupon
-- ----------------------------
DROP TABLE IF EXISTS `ord_member_coupon`;
CREATE TABLE `ord_member_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coupon_no` bigint(20) NOT NULL COMMENT '优惠券编号',
  `member_id` int(11) NOT NULL COMMENT '会员id',
  `status` int(11) NOT NULL COMMENT '状态（0=未使用，1=已使用，2=已过期）',
  `coupon_template_no` bigint(20) NOT NULL COMMENT '优惠券模板编号',
  `coupon_template_vid` int(11) NOT NULL COMMENT '模板版本id',
  `discount_amount` decimal(12, 2) NOT NULL COMMENT '优惠金额',
  `expiry_timestamp` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '过期时间戳',
  `received_timestamp` int(11) NOT NULL COMMENT '优惠券到账时间戳',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `coupon_no`(`coupon_no`) USING BTREE,
  INDEX `coupon_template_no`(`coupon_template_no`) USING BTREE,
  INDEX `coupon_template_vid`(`coupon_template_vid`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员优惠券' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_order
-- ----------------------------
DROP TABLE IF EXISTS `ord_order`;
CREATE TABLE `ord_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `status` int(11) UNSIGNED NOT NULL COMMENT '状态（0=待付款、1=已完成、2=已结算，5已取消，6=已过期，7=已作废，9订单异常）',
  `member_id` int(11) NOT NULL COMMENT '购买人-会员id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `order_amount` decimal(15, 2) NOT NULL COMMENT '订单金额-固定加币',
  `tax` decimal(15, 2) NOT NULL COMMENT '税，固定加币',
  `amount_due` decimal(15, 2) NOT NULL COMMENT '应付金额-固定加币',
  `amount_paid` decimal(15, 2) NOT NULL COMMENT '实付金额-根据单位字段',
  `wallet_deduction` decimal(15, 2) NOT NULL COMMENT '钱包抵扣-固定加币',
  `coupon_deduction` decimal(15, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券抵扣-固定加币',
  `exchange_rate` decimal(15, 7) UNSIGNED NULL DEFAULT NULL COMMENT '汇率（应付货币兑实付货币）',
  `pay_method` int(10) UNSIGNED NOT NULL COMMENT '支付方式（1=微信支付，2=钱包支付，3=线下支付）',
  `paid_unit` char(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实付的金额单位',
  `pay_time` int(11) NULL DEFAULT NULL COMMENT '支付时间',
  `settle_time` int(11) NULL DEFAULT NULL COMMENT '结算时间',
  `settle_operator_id` int(11) NULL DEFAULT NULL COMMENT '结算操作人id',
  `calculation_version` int(11) UNSIGNED NOT NULL DEFAULT 2 COMMENT '计算顺序/方式的版本，1是税算在钱包抵扣之前，2是最新版本',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  `void_time` int(11) NULL DEFAULT NULL COMMENT '作废时间',
  `void_operator_id` int(11) NULL DEFAULT NULL COMMENT '作废操作人id',
  `void_previous_status` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '订单作废前的状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_order_product
-- ----------------------------
DROP TABLE IF EXISTS `ord_order_product`;
CREATE TABLE `ord_order_product`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `use_promo_price` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否使用了促销价格',
  `use_second_discount_price` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否使用了第二个优惠价',
  `use_third_discount_price` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否使用了第三个优惠价',
  `use_resettable_stock` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否使用了可重置库存',
  `purchase_quantity` int(11) UNSIGNED NOT NULL DEFAULT 1 COMMENT '购买数量',
  `price` decimal(12, 2) UNSIGNED NOT NULL COMMENT '价格，0表示免费',
  `source_product_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '来源商品编号',
  `source_product_vid` int(11) NOT NULL DEFAULT 0 COMMENT '来源商品版本id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_no`(`order_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_order_send_coupon
-- ----------------------------
DROP TABLE IF EXISTS `ord_order_send_coupon`;
CREATE TABLE `ord_order_send_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_coupon_no` bigint(20) NOT NULL COMMENT '会员优惠券编号',
  `product_no` bigint(20) NULL DEFAULT NULL COMMENT '商品编号',
  `order_no` bigint(20) NULL DEFAULT NULL COMMENT '订单编号',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '类型（0=订单赠送，1=管理员定向发送）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单发送优惠券关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ord_order_use_coupon
-- ----------------------------
DROP TABLE IF EXISTS `ord_order_use_coupon`;
CREATE TABLE `ord_order_use_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_coupon_no` bigint(20) NOT NULL COMMENT '会员优惠券编号',
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `deduction_amount` decimal(15, 2) NOT NULL COMMENT '抵扣金额',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单使用优惠券关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for plv_upload_record
-- ----------------------------
DROP TABLE IF EXISTS `plv_upload_record`;
CREATE TABLE `plv_upload_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `status` int(11) NOT NULL COMMENT '视频状态（0已提交，1审核中，2审核通过，3审核不通过，4上传失败）',
  `width` int(11) NOT NULL DEFAULT 0,
  `height` int(11) NOT NULL DEFAULT 0,
  `duration` int(11) NOT NULL DEFAULT 0,
  `upload_record_no` bigint(20) NOT NULL COMMENT '上传记录编号',
  `vid` char(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频vid',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频封面',
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `operator_id` int(11) NOT NULL COMMENT '操作人id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 218 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保利威的上传视频记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for points_account
-- ----------------------------
DROP TABLE IF EXISTS `points_account`;
CREATE TABLE `points_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `points_account_no` bigint(20) NOT NULL COMMENT '积分账户编号',
  `balance` decimal(20, 2) NOT NULL COMMENT '余额',
  `user_info_id` int(11) NOT NULL COMMENT '回答人id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分账户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for points_record
-- ----------------------------
DROP TABLE IF EXISTS `points_record`;
CREATE TABLE `points_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `points_account_no` bigint(20) NOT NULL COMMENT '积分账户编号',
  `operation_type` int(11) NOT NULL COMMENT '操作类型（1提问，2回答，3获赞）',
  `change_value` decimal(20, 2) NOT NULL COMMENT '改变的值',
  `value_snapshot` decimal(20, 2) NOT NULL COMMENT '值快照',
  `time` int(11) NOT NULL COMMENT '发生时间',
  `related_table_id` int(11) NULL DEFAULT NULL COMMENT '关联表id，用于找到相关的数据',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `points_account_no`(`points_account_no`) USING BTREE,
  INDEX `operation_type`(`operation_type`) USING BTREE,
  INDEX `related_table_id`(`related_table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 359 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分明细记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_choice_option
-- ----------------------------
DROP TABLE IF EXISTS `qs_choice_option`;
CREATE TABLE `qs_choice_option`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `choice_question_id` int(11) NOT NULL,
  `order` int(11) NOT NULL COMMENT '排序',
  `content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `choice_question_id`(`choice_question_id`) USING BTREE,
  INDEX `order`(`order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '选择题的选项表【还没开始使用】' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_choice_question
-- ----------------------------
DROP TABLE IF EXISTS `qs_choice_question`;
CREATE TABLE `qs_choice_question`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL COMMENT '类型(1：单选，2：多选)',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '选择题数据表【还没开始使用】' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_fill_detail
-- ----------------------------
DROP TABLE IF EXISTS `qs_fill_detail`;
CREATE TABLE `qs_fill_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fill_record_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提交记录编号',
  `question_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目编号',
  `question_id` int(11) NOT NULL COMMENT '题目id，冗余字段',
  `remarks` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `identifiable_answer` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可被标识的作答内容',
  `unidentifiable_answer` varchar(5120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '不可被标识的作答内容',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_no`(`question_no`) USING BTREE,
  INDEX `submission_record_no`(`fill_record_no`) USING BTREE,
  INDEX `question_id`(`question_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11668 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提交的详细数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_fill_record
-- ----------------------------
DROP TABLE IF EXISTS `qs_fill_record`;
CREATE TABLE `qs_fill_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fill_record_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提交记录编号',
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `instance_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷实例编号',
  `invite_code` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请码',
  `leads_id` int(11) NOT NULL COMMENT '潜客id',
  `ip` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `user_info_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员id',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fill_record_no`(`fill_record_no`) USING BTREE,
  INDEX `instance_no`(`instance_no`) USING BTREE,
  INDEX `invite_code`(`invite_code`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE,
  INDEX `leads_id`(`leads_id`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE,
  INDEX `is_delete`(`is_delete`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 601 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提交记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_invite_code
-- ----------------------------
DROP TABLE IF EXISTS `qs_invite_code`;
CREATE TABLE `qs_invite_code`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `short_code` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请码（用于短链接）',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `user_info_id` int(11) NOT NULL COMMENT '用户信息表的id，是用户在单个组织名下的id',
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '类型（1管理员邀请码，2渠道邀请码）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `short_code`(`short_code`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `user_id`(`user_info_id`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 999 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邀请码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_logic_settings
-- ----------------------------
DROP TABLE IF EXISTS `qs_logic_settings`;
CREATE TABLE `qs_logic_settings`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目编号',
  `type` tinyint(4) NOT NULL COMMENT '类型（0：保留值，1：题目关联）',
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `instance_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷实例编号',
  `expression` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表达式，JSON格式，说明具体的逻辑运算规则',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_no`(`question_no`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `instance_no`(`instance_no`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 138 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '逻辑设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_page
-- ----------------------------
DROP TABLE IF EXISTS `qs_page`;
CREATE TABLE `qs_page`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '页编号',
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `instance_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷实例编号',
  `order` int(11) NOT NULL COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `page_no`(`page_no`) USING BTREE,
  INDEX `instance_no`(`instance_no`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1543 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷分页表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_page_question
-- ----------------------------
DROP TABLE IF EXISTS `qs_page_question`;
CREATE TABLE `qs_page_question`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目编号',
  `page_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '页编号',
  `order` int(11) NOT NULL COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_no`(`question_no`) USING BTREE,
  INDEX `page_no`(`page_no`) USING BTREE,
  INDEX `order`(`order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2442 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分页-题目-关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_question
-- ----------------------------
DROP TABLE IF EXISTS `qs_question`;
CREATE TABLE `qs_question`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_type` int(11) NOT NULL COMMENT '题目类型',
  `relater_table_id` int(11) NULL DEFAULT NULL COMMENT '关联表id，根据题目类型，在对应的表下找相关的数据（暂时未用）',
  `title` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目内容',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目附加描述',
  `content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目详情，json数据，前端控制',
  `field_code` tinyint(6) NOT NULL DEFAULT 0 COMMENT '字段代码（0=普通，1=主申请人姓名，2=定存金额，3=微信号，4=手机号）',
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `instance_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷实例编号',
  `question_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `relater_table_id`(`relater_table_id`) USING BTREE,
  INDEX `question_no`(`question_no`) USING BTREE,
  INDEX `question_type_id`(`question_type`) USING BTREE,
  INDEX `field_code`(`field_code`) USING BTREE,
  INDEX `instance_no`(`instance_no`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2502 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_question_type
-- ----------------------------
DROP TABLE IF EXISTS `qs_question_type`;
CREATE TABLE `qs_question_type`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `label` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_questionnaire
-- ----------------------------
DROP TABLE IF EXISTS `qs_questionnaire`;
CREATE TABLE `qs_questionnaire`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `title` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷标题',
  `description` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '问卷描述',
  `custom_directory` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '自定义目录',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `questionnaire_u_no`(`questionnaire_no`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qs_questionnaire_instance
-- ----------------------------
DROP TABLE IF EXISTS `qs_questionnaire_instance`;
CREATE TABLE `qs_questionnaire_instance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `instance_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷实例编号',
  `source_questionnaire_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源问卷编号',
  `source_instance_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源问卷实例编号',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `questionnaire_no_instance_no`(`questionnaire_no`, `instance_no`) USING BTREE,
  INDEX `questionnaire_no`(`questionnaire_no`) USING BTREE,
  INDEX `instance_no`(`instance_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 298 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷实例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_menu
-- ----------------------------
DROP TABLE IF EXISTS `system_menu`;
CREATE TABLE `system_menu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口/页面/其他',
  `module_id` int(11) NOT NULL DEFAULT 0 COMMENT '功能模块id',
  `order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `platform_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台菜单标志',
  `intermediary_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '中介公司菜单标志',
  `resource_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '资源公司菜单标志',
  `channel_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道公司菜单标志',
  `business_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'B端公司菜单标志',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '禁用标志（0:不禁用，1:禁用）',
  `type` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型（0=接口，1=页面，2=模块/按钮，3=特殊权限）',
  `role_type` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '属于什么角色类型的菜单（0:管理员，1渠道人员）',
  `created_at` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `order`(`order`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE,
  INDEX `platform_flag`(`platform_flag`) USING BTREE,
  INDEX `intermediary_flag`(`intermediary_flag`) USING BTREE,
  INDEX `resource_flag`(`resource_flag`) USING BTREE,
  INDEX `channel_flag`(`channel_flag`) USING BTREE,
  INDEX `role_type`(`role_type`) USING BTREE,
  INDEX `module_id`(`module_id`) USING BTREE,
  INDEX `business_flag`(`business_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 389 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_module
-- ----------------------------
DROP TABLE IF EXISTS `system_module`;
CREATE TABLE `system_module`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `created_at` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '特性功能模块表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org
-- ----------------------------
DROP TABLE IF EXISTS `system_org`;
CREATE TABLE `system_org`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型（0=中介，1=渠道，2=资源，3=平台，5=B端公司）',
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '租户名称',
  `mch_id` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户号',
  `avatar` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用（0:否，1:是）',
  `user_quota` int(11) NOT NULL DEFAULT -1 COMMENT '后台用户上限/用户容量，-1表示不限',
  `watermark_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '水印编号',
  `expire_time` int(11) NULL DEFAULT NULL COMMENT '到期时间，null表示永久',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织机构表，一般是一个企业或者公司' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org_info
-- ----------------------------
DROP TABLE IF EXISTS `system_org_info`;
CREATE TABLE `system_org_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `home_page_id` int(11) NOT NULL DEFAULT 0 COMMENT '首页id（0=签证，1=本地服务，2=学校，3=培训）',
  `manager_name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '负责人名称',
  `manager_country_code` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话区号',
  `manager_phone` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '负责人电话',
  `manager_wechat` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '负责人微信',
  `manager_email` char(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '负责人邮箱',
  `official_website_domain` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '官网域名',
  `questionnaire_domain` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问卷域名',
  `logo` char(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`org_id`) USING BTREE,
  INDEX `home_page_id`(`home_page_id`) USING BTREE,
  INDEX `logo`(`logo`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org_management_log
-- ----------------------------
DROP TABLE IF EXISTS `system_org_management_log`;
CREATE TABLE `system_org_management_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `action_code` int(11) NOT NULL COMMENT '操作行为代码（）',
  `related_table_id` int(11) NOT NULL COMMENT '关联表id，用于找到相关的数据',
  `remarks` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `action_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作内容',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `old_value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧值（有需要才填入，统一使用字符串）',
  `new_value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新值（有需要才填入，统一使用字符串）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构管理日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org_module
-- ----------------------------
DROP TABLE IF EXISTS `system_org_module`;
CREATE TABLE `system_org_module`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `module_id` int(11) NOT NULL COMMENT '功能模块id',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 [0: 未开通，1:已开通]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `feature_module_id`(`module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构功能开通表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org_seat_order
-- ----------------------------
DROP TABLE IF EXISTS `system_org_seat_order`;
CREATE TABLE `system_org_seat_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `purchase_quantity` int(11) UNSIGNED NOT NULL DEFAULT 1 COMMENT '购买数量',
  `price` decimal(12, 2) UNSIGNED NOT NULL COMMENT '价格，0表示免费',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构席位订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org_service_plans
-- ----------------------------
DROP TABLE IF EXISTS `system_org_service_plans`;
CREATE TABLE `system_org_service_plans`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `service_plans_id` int(11) NOT NULL COMMENT '服务套餐id',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构和服务套餐的中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_org_skin
-- ----------------------------
DROP TABLE IF EXISTS `system_org_skin`;
CREATE TABLE `system_org_skin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `bg_color` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景色',
  `bg_text_color` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景色字体颜色',
  `fore_color` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '前景色',
  `fore_text_color` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '前景色字体颜色',
  `highlight_color` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '促销字体颜色',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '皮肤表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_ranking_participant
-- ----------------------------
DROP TABLE IF EXISTS `system_ranking_participant`;
CREATE TABLE `system_ranking_participant`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `traffic_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '流量榜参与标志',
  `high_quality_traffic_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '优质流量榜参与标志',
  `conversion_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '转化榜参与标志',
  `sales_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '销售榜参与标志',
  `user_info_id` int(11) NOT NULL COMMENT '人员信息id（user_info表）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '排行榜参与者' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_role
-- ----------------------------
DROP TABLE IF EXISTS `system_role`;
CREATE TABLE `system_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `edit_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否允许编辑（0:否，1:是）',
  `is_platform_role` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否平台角色（0:否，1:是）',
  `product_editor_role_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否商品编辑角色（0:否，1:是）',
  `is_admin` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否超管（0:否，1:是）',
  `role_type` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色类型（0:管理员，1渠道人员）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_platform_role`(`is_platform_role`) USING BTREE,
  INDEX `is_admin`(`is_admin`) USING BTREE,
  INDEX `tenant_id`(`org_id`) USING BTREE,
  INDEX `role_type`(`role_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 130 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `system_role_menu`;
CREATE TABLE `system_role_menu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27844 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色-菜单-中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_security_log
-- ----------------------------
DROP TABLE IF EXISTS `system_security_log`;
CREATE TABLE `system_security_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `operation_code` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型，0=保留，1001=注册[自主注册]，1002=登录，1003=创建组织[公司]，1005=编辑公司信息',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `user_info_id` int(11) NOT NULL COMMENT '用户信息表的id，是用户在单个组织名下的id',
  `org_id` int(11) NOT NULL COMMENT '机构id（不是属于某个机构的时候使用0）',
  `ip` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `old_value` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '旧值（有需要才填入，统一使用字符串）',
  `new_value` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '新值（有需要才填入，统一使用字符串）',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`org_id`) USING BTREE,
  INDEX `business_user_id`(`user_id`) USING BTREE,
  INDEX `operationCode`(`operation_code`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3325 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全日志表，用来记录登录、注册等等的敏感操作' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_service_plans
-- ----------------------------
DROP TABLE IF EXISTS `system_service_plans`;
CREATE TABLE `system_service_plans`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
  `user_quota` int(11) NOT NULL DEFAULT 1 COMMENT '后台用户上限/用户容量，-1表示不限',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户的服务套餐' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user
-- ----------------------------
DROP TABLE IF EXISTS `system_user`;
CREATE TABLE `system_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户编号',
  `password_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码哈希',
  `password_salt` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码盐值',
  `nickname` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '花名，全平台唯一',
  `email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮箱，全局唯一',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用（0:否，1:是）',
  `email_verification_status` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '邮箱验证状态（0: 待验证，1已发邮件，2验证通过）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE,
  UNIQUE INDEX `user_no`(`user_no`) USING BTREE,
  UNIQUE INDEX `nickname`(`nickname`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 104 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user_info
-- ----------------------------
DROP TABLE IF EXISTS `system_user_info`;
CREATE TABLE `system_user_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '企业用户id',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `product_editor_user_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否商品编辑人员（0:否，1:是）',
  `name` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `avatar` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `photo` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '照片',
  `nickname` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '花名，跟着user表里的花名，冗余字段',
  `country_code` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话区号',
  `phone` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电话',
  `introduction` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `qrcode` varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '咨询二维码',
  `channel_id` int(11) NOT NULL DEFAULT 0 COMMENT '显示4位，前补0，渠道人员才用到',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`org_id`) USING BTREE,
  INDEX `business_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user_msg_viewed
-- ----------------------------
DROP TABLE IF EXISTS `system_user_msg_viewed`;
CREATE TABLE `system_user_msg_viewed`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_info_id` int(11) NOT NULL COMMENT 'user_info_id，是用户在单个组织下的用户id',
  `leads_cursor` int(11) NOT NULL DEFAULT 0 COMMENT '潜客读取游标，表示用户已读到哪里，目前使用时间戳',
  `kbs_cursor` int(11) NOT NULL DEFAULT 0 COMMENT '知识库读取游标，表示用户已读到哪里，目前使用时间戳',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_info_id`(`user_info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户消息查看表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user_notice
-- ----------------------------
DROP TABLE IF EXISTS `system_user_notice`;
CREATE TABLE `system_user_notice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_info_id` int(11) NOT NULL COMMENT '管理员id',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `type` int(11) NOT NULL COMMENT '类型（1=新leads通知）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 173 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user_org
-- ----------------------------
DROP TABLE IF EXISTS `system_user_org`;
CREATE TABLE `system_user_org`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '企业用户id',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `disabled_flag` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '禁用标志（0:不禁用，1:禁用）',
  `employment_status` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '雇用状态（0：在职，1离职）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `disabled_flag`(`disabled_flag`) USING BTREE,
  INDEX `tenant_id`(`org_id`) USING BTREE,
  INDEX `business_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构和用户中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user_preferences
-- ----------------------------
DROP TABLE IF EXISTS `system_user_preferences`;
CREATE TABLE `system_user_preferences`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '企业用户id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `timezone` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时区',
  `leads_table_config` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '潜客表格配置',
  `leads_columns_freeze_config` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '潜客表格列冻结配置',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `business_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 104 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业用户偏好设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_user_role
-- ----------------------------
DROP TABLE IF EXISTS `system_user_role`;
CREATE TABLE `system_user_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '企业用户id',
  `role_id` int(11) NOT NULL,
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 264 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户-角色-中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_adviser
-- ----------------------------
DROP TABLE IF EXISTS `website_adviser`;
CREATE TABLE `website_adviser`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adviser_no` bigint(20) NOT NULL COMMENT '顾问编号',
  `office_no` bigint(20) NULL DEFAULT NULL COMMENT '办公室编号',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `home_display_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '首页展示标志[0不展示，1展示]',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `photo_path` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '照片',
  `name` char(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `job_title` char(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职称',
  `identity_ids` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份，多选，使用逗号分隔（1=管理团队，2=持牌移民顾问，3=资深顾问）',
  `license_file_no` bigint(20) NULL DEFAULT NULL COMMENT '牌照照片文件编号',
  `qrcode_path` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '咨询二维码',
  `introduction` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `adviser_no`(`adviser_no`) USING BTREE,
  INDEX `office_no`(`office_no`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `home_display_flag`(`home_display_flag`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，顾问' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_adviser_license
-- ----------------------------
DROP TABLE IF EXISTS `website_adviser_license`;
CREATE TABLE `website_adviser_license`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `watermark_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '水印编号',
  `path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `file_no`(`file_no`) USING BTREE,
  INDEX `watermark_no`(`watermark_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网-顾问-牌照表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_approval_letter
-- ----------------------------
DROP TABLE IF EXISTS `website_approval_letter`;
CREATE TABLE `website_approval_letter`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `file_no` bigint(20) NOT NULL COMMENT '文件编号',
  `watermark_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '水印编号',
  `path` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `case_no`(`file_no`) USING BTREE,
  INDEX `watermark_no`(`watermark_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1306 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网-获批信表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for website_banner
-- ----------------------------
DROP TABLE IF EXISTS `website_banner`;
CREATE TABLE `website_banner`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `display_target` tinyint(4) NOT NULL DEFAULT 0 COMMENT '展示位置：1首页，2团队页，3案例页，4项目页',
  `banner_no` bigint(20) NOT NULL COMMENT 'banner编号',
  `title` char(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `title_color` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '#FFFFFF' COMMENT '标题配色',
  `image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片路径',
  `display_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '展示标志[0不展示，1展示]',
  `introduction` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `link` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `display_target`(`display_target`) USING BTREE,
  INDEX `display_flag`(`display_flag`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `banner_no`(`banner_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，banner表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_base_config
-- ----------------------------
DROP TABLE IF EXISTS `website_base_config`;
CREATE TABLE `website_base_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '官网标题',
  `logo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `keywords` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `text_color` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文本配色',
  `copyright` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版权所属',
  `footer_color` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '底部配色',
  `footer_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '底部配图',
  `network_record_number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '网安备案号',
  `icp_record_number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ICP备案号',
  `watermark_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '水印编号',
  `qrcode_image` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码图片路径',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `watermark_no`(`watermark_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网 基础配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_case
-- ----------------------------
DROP TABLE IF EXISTS `website_case`;
CREATE TABLE `website_case`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `case_no` bigint(20) NOT NULL COMMENT '案例编号',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `child_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子级业务编号',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `name` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '案例名称',
  `customer_name` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户名称',
  `description` varchar(7000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `home_display_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '首页展示标志[0不展示，1展示]',
  `top_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '置顶标志[0否，1是]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `business_no`(`business_no`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `home_display_flag`(`home_display_flag`) USING BTREE,
  INDEX `top_flag`(`top_flag`) USING BTREE,
  INDEX `case_no`(`case_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网-案例表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_case_approval_letter
-- ----------------------------
DROP TABLE IF EXISTS `website_case_approval_letter`;
CREATE TABLE `website_case_approval_letter`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `case_no` bigint(20) NOT NULL COMMENT '案例编号',
  `approval_letter_file_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '获批信文件编号',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `case_no`(`case_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 300 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网案例，获批信图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_case_sharing
-- ----------------------------
DROP TABLE IF EXISTS `website_case_sharing`;
CREATE TABLE `website_case_sharing`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `origin_org_id` int(11) NOT NULL COMMENT '源机构id',
  `sharing_org_id` int(11) NOT NULL COMMENT '共享的机构id（共享给哪个机构）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网-案例共享表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for website_case_step
-- ----------------------------
DROP TABLE IF EXISTS `website_case_step`;
CREATE TABLE `website_case_step`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `case_no` bigint(20) NOT NULL COMMENT '案例编号',
  `step_name` char(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '步骤名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `date` date NULL DEFAULT NULL COMMENT '日期',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `case_no`(`case_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 394 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网案例步骤表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_directory
-- ----------------------------
DROP TABLE IF EXISTS `website_directory`;
CREATE TABLE `website_directory`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '标签层级',
  `directory_no` bigint(20) NOT NULL COMMENT '目录编号',
  `parent_no` bigint(20) NOT NULL DEFAULT 0,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `link` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `head_display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '头部展示标志[0不展示，1展示]',
  `footer_display_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '页脚展示标志[0不展示，1展示]',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `directory_no`(`directory_no`) USING BTREE,
  INDEX `pid`(`parent_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 160 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，目录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_news
-- ----------------------------
DROP TABLE IF EXISTS `website_news`;
CREATE TABLE `website_news`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `news_no` bigint(20) NOT NULL COMMENT '新闻编号',
  `title` char(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `cover_image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面图链接',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容-富文本',
  `introduction` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `custom_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义路径',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `home_display_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '首页展示标志[0不展示，1展示]',
  `hot_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '热门标志[0否，1是]',
  `top_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '置顶标志[0否，1是]',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `hot_flag`(`hot_flag`) USING BTREE,
  INDEX `top_flag`(`top_flag`) USING BTREE,
  INDEX `news_no`(`news_no`) USING BTREE,
  INDEX `home_display_flag`(`home_display_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，新闻表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for website_news_tag
-- ----------------------------
DROP TABLE IF EXISTS `website_news_tag`;
CREATE TABLE `website_news_tag`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `news_tag_no` bigint(20) NOT NULL COMMENT '标签编号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `news_tag_no`(`news_tag_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，新闻标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_news_tag_map
-- ----------------------------
DROP TABLE IF EXISTS `website_news_tag_map`;
CREATE TABLE `website_news_tag_map`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `news_tag_no` bigint(20) NOT NULL COMMENT '标签编号',
  `news_no` bigint(20) NOT NULL COMMENT '新闻编号',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '新闻-新闻标签关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_office
-- ----------------------------
DROP TABLE IF EXISTS `website_office`;
CREATE TABLE `website_office`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `office_no` bigint(20) NOT NULL COMMENT '办公室编号',
  `name` char(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `office_no`(`office_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，办公室' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for website_project
-- ----------------------------
DROP TABLE IF EXISTS `website_project`;
CREATE TABLE `website_project`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_no` bigint(20) NOT NULL COMMENT '项目编号',
  `home_display_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '首页展示标志[0不展示，1展示]',
  `preview_image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '预览图路径',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容-富文本',
  `introduction` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `custom_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义路径',
  `business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编号',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `home_display_flag`(`home_display_flag`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `business_no`(`business_no`) USING BTREE,
  INDEX `project_no`(`project_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网-项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for website_region_language
-- ----------------------------
DROP TABLE IF EXISTS `website_region_language`;
CREATE TABLE `website_region_language`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `language` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `region` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域（国家/地区）',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '官网，国家/地区以及语言表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_applet_audit_record
-- ----------------------------
DROP TABLE IF EXISTS `wechat_applet_audit_record`;
CREATE TABLE `wechat_applet_audit_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `authorizer_appid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '授权小程序appid',
  `audit_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核id（微信端生成）',
  `issue_version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发行版本',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0：待审核 1：已提交 2：审核通过 3：审核拒绝 4：审核延后）',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝/延后原因',
  `created_at` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0：否 1：是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序审核记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_applet_authorizer
-- ----------------------------
DROP TABLE IF EXISTS `wechat_applet_authorizer`;
CREATE TABLE `wechat_applet_authorizer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) NOT NULL COMMENT '机构id',
  `authorizer_appid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '授权小程序appid',
  `appsecret` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授权小程序名称',
  `head_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序头像',
  `qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授权小程序二维码url',
  `service_type` tinyint(1) NULL DEFAULT NULL COMMENT '小程序类型（0：普通公众号 1：试用小程序 4：小游戏 10：小商店）',
  `verify_type` tinyint(1) NULL DEFAULT NULL COMMENT '小程序认证类型（0：未认证 1：微信认证）',
  `icp_status` int(11) NULL DEFAULT NULL COMMENT '备案的状态',
  `icp_status_text` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备案的状态文本',
  `audit_data` varchar(9000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备案驳回原因',
  `created_at` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除（0：否 1：是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_msg_record
-- ----------------------------
DROP TABLE IF EXISTS `wechat_msg_record`;
CREATE TABLE `wechat_msg_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信通知记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_order
-- ----------------------------
DROP TABLE IF EXISTS `wechat_order`;
CREATE TABLE `wechat_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wx_order_no` char(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信订单编号',
  `order_no` bigint(20) NOT NULL COMMENT '订单编号',
  `openid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `status` int(11) NOT NULL COMMENT '状态(0=未支付，1=已支付，2=支付失败，3=已过期)',
  `amount` bigint(20) NOT NULL COMMENT '金额-单位人民币-分',
  `callback_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调数据',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `wx_order_no`(`wx_order_no`) USING BTREE,
  INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_receive_msg_record
-- ----------------------------
DROP TABLE IF EXISTS `wechat_receive_msg_record`;
CREATE TABLE `wechat_receive_msg_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息内容',
  `openid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '接受到的来自微信服务器的消息记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wechat_send_msg_record
-- ----------------------------
DROP TABLE IF EXISTS `wechat_send_msg_record`;
CREATE TABLE `wechat_send_msg_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `msg_type` int(11) NOT NULL COMMENT '消息类型，1新工单通知，2新工单通知[已废弃]',
  `related_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联表id',
  `parameter` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求参数',
  `content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息内容',
  `status` tinyint(4) NOT NULL COMMENT '状态，-1待处理，-2已处理，-3无须通知，0未发生，1已发送，2发送成功，3发送失败',
  `return_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方-返回状态码',
  `return_msg` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方-返回msg',
  `return_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方-返回数据',
  `biz_error_msg` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义业务异常',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `msg_type`(`msg_type`) USING BTREE,
  INDEX `related_id`(`related_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 159 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送给用户的消息记录【微信消息】' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wechat_user_association
-- ----------------------------
DROP TABLE IF EXISTS `wechat_user_association`;
CREATE TABLE `wechat_user_association`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '系统用户id',
  `openid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'openid',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信用户和系统用户关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
