package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * <b> OrgTypeEnum </b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-05
 */

@Getter
@AllArgsConstructor
public enum OrgTypeEnum {

    //类型（0=中介，1=渠道，2=资源，3=平台）
    INTERMEDIARY(0, "中介"),
    CHANNEL(1, "渠道"),
    RESOURCE(2, "资源"),
    PLATFORM(3, "平台"),
    BUSINESS(5, "B端")
    ;

    private final Integer code;
    private final String label;

    public static OrgTypeEnum parse(Integer code) {
        return Arrays.stream( OrgTypeEnum.values() )
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    public static OrgTypeEnum parse(String label) {
        return Arrays.stream(OrgTypeEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }
}
