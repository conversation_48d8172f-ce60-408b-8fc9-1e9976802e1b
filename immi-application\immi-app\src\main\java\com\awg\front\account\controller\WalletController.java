package com.awg.front.account.controller;

import com.awg.account.dto.WalletInfoDto;
import com.awg.account.dto.WalletTransactionItemDto;
import com.awg.account.service.IWalletService;
import com.awg.account.vo.MemberWalletInfoVo;
import com.awg.account.vo.QueryWalletTransactionVo;
import com.awg.client.vo.GetMemberInviteCodeVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 80 )
@Api( tags = {"钱包相关接口"} )
@RestController
@RequestMapping( "/account/wallet" )
public class WalletController extends BaseController {

    @Resource
    private IWalletService walletService;

    @ApiOperation( "获取钱包信息" )
    @PostMapping( "/getMemberWalletInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WalletInfoDto.class )
    })
    public DataResult getMemberWalletInfo(@RequestBody MemberWalletInfoVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(walletService.getMemberWalletInfo(vo, getMemberLoginInfoEo()));
    }

    @ApiOperation( "获取钱包流水" )
    @PostMapping( "/transactionList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WalletTransactionItemDto.class )
    })
    public DataResult transactionList(@RequestBody QueryWalletTransactionVo vo) {

        // 获取会员id
        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        // 获取钱包编号
        MemberWalletInfoVo memberWalletInfoVo = new MemberWalletInfoVo();
        memberWalletInfoVo.setType(2);
        WalletInfoDto walletInfoDto = walletService.getMemberWalletInfo(memberWalletInfoVo, getMemberLoginInfoEo());
        vo.setWalletNo(walletInfoDto.getWalletNo());

        ValidationUtils.validate(vo);
        return renderSuccess(walletService.consumerWalletTransactionList(vo));
    }
}
