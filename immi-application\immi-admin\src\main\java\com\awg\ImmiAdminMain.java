package com.awg;

import com.awg.common.constant.ConfigConstant;
import com.awg.common.redis.RedisUtils;
import com.awg.common.spring.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;

/**
 * <p>
 * 启动器
 * </p>
 *
 * @author: yang qiang
 * @date: 2020/10/28 16:42
 **/

@EnableTransactionManagement
@Slf4j
@SpringBootApplication
@MapperScan("com.awg.*.*.mapper")
public class ImmiAdminMain {
    public static void main(String[] args) throws Exception {
        ConfigurableApplicationContext application = SpringApplication.run(ImmiAdminMain.class, args);
        Environment env = application.getEnvironment();

        // 配置初始化
        RedisUtils redisUtils = SpringContextUtils.getBean(RedisUtils.class);
        if (!redisUtils.exists(ConfigConstant.CONFIG_SWITCH_MAINTAIN)) {
            redisUtils.set(ConfigConstant.CONFIG_SWITCH_MAINTAIN, "0");
            ConfigConstant.DEFAULT_IP_WHITE_LIST.forEach(obj -> redisUtils.lPush(ConfigConstant.CONFIG_IP_WHITE_LIST, obj));
        }

        log.info("\n----------------------------------------------------------\n\t" +
                        "Application '{}' is running! Access URLs:\n\t" +
                        "Doc: \thttp://{}:{}/immi-admin/doc.html\n" +
                        "----------------------------------------------------------",
                env.getProperty("spring.application.name"),
                InetAddress.getLocalHost().getHostAddress(),
                env.getProperty("server.port"),
                InetAddress.getLocalHost().getHostAddress(),
                env.getProperty("server.port"));
    }
}
