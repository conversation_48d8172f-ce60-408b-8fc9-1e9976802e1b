package com.awg.admin.comm.controller;

import com.awg.comm.entity.ProductKeyword;
import com.awg.comm.entity.ProductSecondaryCategory;
import com.awg.comm.service.IProductKeywordService;
import com.awg.comm.service.IProductSecondaryCategoryService;
import com.awg.comm.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 224 )
@Api( tags = {"商品关键词相关接口"} )
@RestController
@RequestMapping( "/comm/keyword" )
public class ProductKeywordController extends BaseController {

    @Resource
    private IProductKeywordService productKeywordService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取关键词列表【全部】" )
    @PostMapping( "/listAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductKeyword.class )
    })
    public DataResult listAll(@RequestBody QueryDistrictVo vo) {
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        return renderSuccess(productKeywordService.getProductKeywordAll(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "添加" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addKeyword(@RequestBody ProductKeywordVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productKeywordService.addProductKeyword(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "更新" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateKeyword(@RequestBody ProductKeywordVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productKeywordService.updateProductKeyword(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除" )
    @PostMapping( "/delete/{keywordId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult deleteKeyword(@PathVariable( value = "keywordId" ) Integer keywordId) {
        AssertUtils.isTrue(keywordId==null || keywordId<=0, "请先传入id");
        productKeywordService.deleteProductKeyword(keywordId, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 55)
    @ApiOperation( "排序" )
    @PostMapping( "/sort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortKeyword(@RequestBody DistrictSortVo vo) {
        vo.setPid(0);
        ValidationUtils.validate(vo);
        productKeywordService.sortProductKeyword(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 65)
    @ApiOperation( "显示/隐藏" )
    @PostMapping( "/displayChange" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult displayChange(@RequestBody DisplayChangeVo vo) {
        ValidationUtils.validate(vo);
        productKeywordService.displayChange(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

}
