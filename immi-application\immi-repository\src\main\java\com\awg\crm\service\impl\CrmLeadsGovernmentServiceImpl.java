package com.awg.crm.service.impl;

import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.utils.FileBaseUtil;
import com.awg.crm.dto.CrmLeadsGovernmentDto;
import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.entity.CrmLeadsGovernmentScreenshot;
import com.awg.crm.eo.LeadsLogEo;
import com.awg.crm.externalService.ILeadsLogExternalService;
import com.awg.crm.mapper.CrmLeadsGovernmentMapper;
import com.awg.crm.service.CrmLeadsGovernmentScreenshotService;
import com.awg.crm.service.CrmLeadsGovernmentService;
import com.awg.crm.vo.CrmLeadsGovernmentScreenshotVo;
import com.awg.crm.vo.CrmLeadsGovernmentVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ProjectName
 * @ClassName CrmLeadsGovernmentServiceImpl
 * @Author: ZhouJun
 * @Date: 2025-07-09
 * @Description:
 **/
//todo 可对uri路径和日志对象进行优化，减少代码冗余
@Service
public class CrmLeadsGovernmentServiceImpl extends ServiceImpl<CrmLeadsGovernmentMapper, CrmLeadsGovernment> implements CrmLeadsGovernmentService {

    @Resource
    CrmLeadsGovernmentScreenshotService crmLeadsGovernmentScreenshotService;


    @Resource
    CrmLeadsGovernmentMapper crmLeadsGovernmentMapper;


    @Resource
    private ILeadsLogExternalService leadsLogExternalService;


    @Override
    public List<CrmLeadsGovernmentDto> listByLeadsId(Long leadsId) {
        List<CrmLeadsGovernmentDto> crmLeadsGovernments = crmLeadsGovernmentMapper.listByLeadsId(leadsId.intValue());
        // 检查 crmLeadsGovernments 是否为 null 或空列表
        if (CollectionUtils.isNotEmpty(crmLeadsGovernments)) {
            // 遍历每个 CrmLeadsGovernmenDto 对象，处理其 screenshots 字段
            crmLeadsGovernments.forEach(governmentDto -> {
                if (CollectionUtils.isNotEmpty(governmentDto.getScreenshots())) {
                    governmentDto.setScreenshots(
                            governmentDto.getScreenshots().stream()
                                    .map(screenshot -> {
                                        screenshot.setScreenshotPath(FileBaseUtil.getFileUrl(screenshot.getScreenshotPath()));
                                        return screenshot;
                                    })
                                    .collect(Collectors.toList())
                    );
                }
            });
        }

        return crmLeadsGovernments;
    }

    @Override
    public CrmLeadsGovernmentDto getById(Long id) {
        CrmLeadsGovernmentDto crmLeadsGovernmentDto = crmLeadsGovernmentMapper.getById(id.intValue());
        if (crmLeadsGovernmentDto != null && CollectionUtils.isNotEmpty(crmLeadsGovernmentDto.getScreenshots())) {
            //拼接路径前缀
            crmLeadsGovernmentDto.setScreenshots(
                    crmLeadsGovernmentDto.getScreenshots().stream()
                            .map(s -> {
                                s.setScreenshotPath(FileBaseUtil.getFileUrl(s.getScreenshotPath()));
                                return s;
                            })
                            .collect(Collectors.toList())
            );
        }
        return crmLeadsGovernmentDto;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCrmLeadsGovernment(CrmLeadsGovernmentVo crmLeadsGovernmentVo, UserLoginInfoEo userLoginInfoEo) {
        CrmLeadsGovernment crmLeadsGovernment = new CrmLeadsGovernment();
        BeanUtils.copyProperties(crmLeadsGovernmentVo, crmLeadsGovernment);
        //新增政府费
        baseMapper.insert(crmLeadsGovernment);

        if (CollectionUtils.isNotEmpty(crmLeadsGovernmentVo.getScreenshots())) {
            //拼接路径前缀
            crmLeadsGovernmentVo.setScreenshots(
                    crmLeadsGovernmentVo.getScreenshots().stream()
                            .map(s -> {
                                s.setScreenshotPath(FileBaseUtil.getRelativeUrl(s.getScreenshotPath()));
                                return s;
                            })
                            .collect(Collectors.toList())
            );
            // 5. 批量新增截图
            List<CrmLeadsGovernmentScreenshot> screenshots = crmLeadsGovernmentVo.getScreenshots().stream()
                    .map(screenshotPath -> {
                        CrmLeadsGovernmentScreenshot screenshot = new CrmLeadsGovernmentScreenshot();
                        screenshot.setCrmLeadsGovernmentId(crmLeadsGovernment.getId());
                        screenshot.setScreenshotPath(screenshotPath.getScreenshotPath());
//                        screenshot.setIsDelete(0);
                        return screenshot;
                    })
                    .collect(Collectors.toList());

            // 调用 Service 的 saveBatch 方法批量保存截图
            crmLeadsGovernmentScreenshotService.saveBatch(screenshots);
        }

        //新增日志
        LeadsLogEo leadsLogEo = new LeadsLogEo();
        leadsLogEo.setLeadsId(crmLeadsGovernmentVo.getLeadsId());
        leadsLogEo.setRelatedTableId(crmLeadsGovernment.getId());
        leadsLogEo.setActionCode(195);
        leadsLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        leadsLogEo.setRemarks("新增了");
        StringBuilder actionContent = new StringBuilder();
        actionContent.append(crmLeadsGovernment.getFeeName());
        actionContent.append(" ");
        actionContent.append(crmLeadsGovernment.getActualAmount());
        actionContent.append(" ");
        actionContent.append(crmLeadsGovernment.getCurrencyUnit());
        actionContent.append(" ");
        String result = actionContent.toString();
        leadsLogEo.setActionContent(result);
        leadsLogExternalService.addLeadsLog(leadsLogEo);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCrmLeadsGovernmentByld(Long id, UserLoginInfoEo userLoginInfoEo) {
        CrmLeadsGovernment crmLeadsGovernment = baseMapper.selectById(id);
        if (crmLeadsGovernment == null){
            return;
        }
        //删除和政府费关联的政府费截图
        LambdaQueryWrapper<CrmLeadsGovernmentScreenshot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmLeadsGovernmentScreenshot::getCrmLeadsGovernmentId, id);
        crmLeadsGovernmentScreenshotService.remove(wrapper);

        //删除政府费
        baseMapper.deleteById(id);


        //删除日志
        LeadsLogEo leadsLogEo = new LeadsLogEo();
        leadsLogEo.setLeadsId(crmLeadsGovernment.getLeadsId());
        leadsLogEo.setRelatedTableId(id.intValue());
        leadsLogEo.setActionCode(197);
        leadsLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        leadsLogEo.setRemarks("删除了");
        StringBuilder actionContent = new StringBuilder();
        actionContent.append(crmLeadsGovernment.getFeeName());
        actionContent.append(" ");
        actionContent.append(crmLeadsGovernment.getActualAmount());
        actionContent.append(" ");
        actionContent.append(crmLeadsGovernment.getCurrencyUnit());
        actionContent.append(" ");
        String result = actionContent.toString();
        leadsLogEo.setActionContent(result);
        leadsLogExternalService.addLeadsLog(leadsLogEo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCrmLeadsGovernment(CrmLeadsGovernmentVo crmLeadsGovernmentVo, UserLoginInfoEo userLoginInfoEo) {
        CrmLeadsGovernment crmLeadsGovernment = new CrmLeadsGovernment();
        BeanUtils.copyProperties(crmLeadsGovernmentVo, crmLeadsGovernment);
        //修改政府费
        baseMapper.updateById(crmLeadsGovernment);

        //拼接路径前缀
        if (CollectionUtils.isNotEmpty(crmLeadsGovernmentVo.getScreenshots())) {
            crmLeadsGovernmentVo.setScreenshots(
                    crmLeadsGovernmentVo.getScreenshots().stream()
                            .map(s -> {
                                s.setScreenshotPath(FileBaseUtil.getRelativeUrl(s.getScreenshotPath()));
                                return s;
                            })
                            .collect(Collectors.toList())
            );
        }

        // 2. 处理支付截图（code=1 新增，code=2 删除）
        List<CrmLeadsGovernmentScreenshotVo> screenshots = crmLeadsGovernmentVo.getScreenshots();
        if (CollectionUtils.isNotEmpty(screenshots)) {
            // 2.1 分类处理
            List<CrmLeadsGovernmentScreenshotVo> toInsertVos = screenshots.stream()
                    .filter(s -> s.getCode() == 1)
                    .collect(Collectors.toList());

            List<CrmLeadsGovernmentScreenshotVo> toDelete = screenshots.stream()
                    .filter(s -> s.getCode() == 2)
                    .collect(Collectors.toList());

            // 2.2 执行删除
            if (CollectionUtils.isNotEmpty(toDelete)) {
                List<Integer> idsToDelete = toDelete.stream()
                        .map(CrmLeadsGovernmentScreenshotVo::getId)
                        .collect(Collectors.toList());
                LambdaQueryWrapper<CrmLeadsGovernmentScreenshot> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(CrmLeadsGovernmentScreenshot::getId, idsToDelete);
                crmLeadsGovernmentScreenshotService.remove(wrapper);
            }

            // 2.3 执行新增
            if (CollectionUtils.isNotEmpty(toInsertVos)) {
                // 将 CrmLeadsGovernmentScreenshotVo 转换为 CrmLeadsGovernmentScreenshot
                List<CrmLeadsGovernmentScreenshot> toInsert = convertToEntities(toInsertVos);
                toInsert.forEach(s -> {
                    s.setCrmLeadsGovernmentId(crmLeadsGovernment.getId());
                    s.setCode(null); // 清除 code，避免下次重复使用
                });
                crmLeadsGovernmentScreenshotService.saveBatch(toInsert);
            }
        }


        //修改日志
        CrmLeadsGovernment government = baseMapper.selectById(crmLeadsGovernmentVo.getId());
        LeadsLogEo leadsLogEo = new LeadsLogEo();
        leadsLogEo.setLeadsId(government.getLeadsId());
        leadsLogEo.setRelatedTableId(crmLeadsGovernmentVo.getId());
        leadsLogEo.setActionCode(196);
        leadsLogEo.setOperatorId(userLoginInfoEo.getUserInfoId());
        leadsLogEo.setRemarks("修改了");
        StringBuilder actionContent = new StringBuilder();
        actionContent.append(crmLeadsGovernment.getFeeName());
        actionContent.append(" ");
        actionContent.append(crmLeadsGovernment.getActualAmount());
        actionContent.append(" ");
        actionContent.append(crmLeadsGovernment.getCurrencyUnit());
        actionContent.append(" ");
        String result = actionContent.toString();
        leadsLogEo.setActionContent(result);
        leadsLogExternalService.addLeadsLog(leadsLogEo);
    }


    /**
     * 将 CrmLeadsGovernmentScreenshotVo 转换为 CrmLeadsGovernmentScreenshot
     */
    private CrmLeadsGovernmentScreenshot convertToEntity(CrmLeadsGovernmentScreenshotVo vo) {
        CrmLeadsGovernmentScreenshot entity = new CrmLeadsGovernmentScreenshot();
        BeanUtils.copyProperties(vo, entity);
        return entity;
    }

    private List<CrmLeadsGovernmentScreenshot> convertToEntities(List<CrmLeadsGovernmentScreenshotVo> vos) {
        return vos.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }


}
