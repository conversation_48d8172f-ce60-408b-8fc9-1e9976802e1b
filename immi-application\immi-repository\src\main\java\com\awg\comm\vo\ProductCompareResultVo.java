package com.awg.comm.vo;

import com.awg.comm.dto.MaterialDto;
import com.awg.comm.dto.MaterialGroupDto;
import com.awg.comm.dto.ProductBannerDto;
import com.awg.comm.dto.ProductPdfDto;
import com.awg.comm.entity.ProductKeyword;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("商品对比结果")
public class ProductCompareResultVo {

    @ApiModelProperty("商品编号")
    private String productNo;

    @ApiModelProperty("是否有差异")
    private Boolean hasDifferences;

    @ApiModelProperty("分类（1=签证类，2=申校类）")
    private PlatformNewProductInfoVo<Integer> category;

    @ApiModelProperty("二级分类id")
    private PlatformNewProductInfoVo<Integer> secondaryCategory;

    @ApiModelProperty("二级分类名称")
    private PlatformNewProductInfoVo<String> secondaryCategoryName;

    @ApiModelProperty("教育阶段（1=中小学，2=college，3=本科，4=研究生）")
    private PlatformNewProductInfoVo<Integer> educationalStage;

    @ApiModelProperty("地区id")
    private PlatformNewProductInfoVo<Integer> districtId;

    @ApiModelProperty("地区名称")
    private PlatformNewProductInfoVo<String> districtName;

    @ApiModelProperty("学校logo")
    private PlatformNewProductInfoVo<String> schoolLogo;

    @ApiModelProperty("商品封面")
    private PlatformNewProductInfoVo<String> cover;

    @ApiModelProperty("商品名称")
    private PlatformNewProductInfoVo<String> name;

    @ApiModelProperty("商品描述")
    private PlatformNewProductInfoVo<String> description;

    @ApiModelProperty("平台交付价格，0表示免费")
    private PlatformNewProductInfoVo<BigDecimal> platformDeliveryPrice;

    @ApiModelProperty("交付类型，0=自己交付，1=平台交付")
    private PlatformNewProductInfoVo<Integer> deliveryType;

    @ApiModelProperty("关键词id列表")
    private PlatformNewProductInfoVo<String> keywordIds;

    @ApiModelProperty("关键词列表")
    private PlatformNewProductInfoVo<List<ProductKeyword>> keywordList;

    @ApiModelProperty("关联项目(业务)")
    private PlatformNewProductInfoVo<String> relatedBusinessNo;

    @ApiModelProperty("关联项目(业务)名称")
    private PlatformNewProductInfoVo<String> relatedBusinessName;

    @ApiModelProperty("关联子级项目(业务)编号")
    private PlatformNewProductInfoVo<String> relatedChildBusinessNo;

    @ApiModelProperty("关联子级项目(业务)名称")
    private PlatformNewProductInfoVo<String> relatedChildBusinessName;

    @ApiModelProperty("注意事项")
    private PlatformNewProductInfoVo<String> notes;

    @ApiModelProperty("商品简介")
    private PlatformNewProductInfoVo<String> feeDescription;

    @ApiModelProperty("服务条款")
    private PlatformNewProductInfoVo<String> termsOfService;

    @ApiModelProperty("价格，0表示免费")
    private PlatformNewProductInfoVo<BigDecimal> price;

    @ApiModelProperty("必须材料列表")
    private PlatformNewProductInfoVo<List<MaterialDto>> requiredMaterialList;

    @ApiModelProperty("可选材料列表")
    private PlatformNewProductInfoVo<List<MaterialDto>> optionalMaterialList;

    @ApiModelProperty("分组材料列表（N选M材料）")
    private PlatformNewProductInfoVo<List<MaterialGroupDto>> materialGroupList;

    @ApiModelProperty("商品素材列表")
    private PlatformNewProductInfoVo<List<ProductBannerDto>> productBannerList;

    @ApiModelProperty("商品pdf")
    private PlatformNewProductInfoVo<List<ProductPdfDto>> pdfList;
}
