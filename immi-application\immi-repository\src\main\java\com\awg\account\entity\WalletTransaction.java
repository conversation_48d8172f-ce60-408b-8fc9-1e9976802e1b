package com.awg.account.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 钱包交易流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@EqualsAndHashCode( callSuper = true )
@TableName(value = "account_wallet_transaction")
public class WalletTransaction extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 钱包编号
     */
    private Long walletNo;

    /**
     * 钱包交易编号(流水编号)
     */
    private Long transactionNo;

    /**
     * 操作代码（）
     */
    private Integer actionCode;

    /**
     * 操作类目（1=充值，2=消费，3=金额到账，4=返佣，5=手动操作）
     */
    private Integer actionCategory;

    /**
     * 余额改变
     */
    private BigDecimal balanceChange;

    /**
     * 可用余额改变
     */
    private BigDecimal availableBalanceChange;

    /**
     * 待到账金额改变
     */
    private BigDecimal pendingBalanceChange;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 余额快照
     */
    private BigDecimal balanceSnapshot;

    /**
     * 可用余额快照
     */
    private BigDecimal availableBalanceSnapshot;

    /**
     * 待到账金额快照
     */
    private BigDecimal pendingBalanceSnapshot;

    /**
     * 操作人id，系统操作的话为0
     */
    private Long operatorId;

    /**
     * 操作人员类型
     */
    private Integer operatorType;

    /**
     * 关联订单编号
     */
    private Long orderNo;

}
