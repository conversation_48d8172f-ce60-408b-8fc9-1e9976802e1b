package com.awg.common.validator.type;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/***
 * @description: 枚举类型效验器
 * @author: 夜晓
 * @date: 2021/3/29 10:03
 **/

@Documented
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Repeatable(ValidationEnum.List.class)
@Constraint(validatedBy = {EnumValidator.class})
@SuppressWarnings(value = "unused")
public @interface ValidationEnum {

    //提示信息
    String message() default "非法类型";

    //不同情况下效验逻辑
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    //枚举类型
    Class<?> clazz();

    //需要效验的属性 (写方法名, 如果枚举只有一个属性, 可使用默认值)
    String method() default "getCode";

    @Documented
    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
    @Retention(RUNTIME)
    @interface List {
        ValidationEnum[] value();
    }
}
