package com.awg.common.base.result;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <b>DictionaryResult</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/3 15:14
 */
@Data
public class DictionaryResult implements Serializable , Cloneable{

    private static final long serialVersionUID = 1L;

    private Integer key;

    private String value;


    @Override
    public DictionaryResult clone() throws CloneNotSupportedException {
        return  (DictionaryResult) super.clone();
    }

    public DictionaryResult() {
    }

    public DictionaryResult(Integer key, String value) {
        this.key = key;
        this.value = value;
    }
}