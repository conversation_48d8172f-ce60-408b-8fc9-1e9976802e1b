package com.awg.account.mapper;

import com.awg.account.dto.WalletTransactionItemDto;
import com.awg.account.entity.WalletTransaction;
import com.awg.account.vo.QueryWalletTransactionVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 钱包交易流水表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface WalletTransactionCrossMapper extends BaseMapper<WalletTransaction> {

    /**
     * <p>
     * 获取交易流水列表
     * </p>
     *
     * @author: lun
     * @date: 2023-12-12
     */
    Page<WalletTransactionItemDto> walletTransactionList(
            Page<WalletTransactionItemDto> page,
            @Param( "query" ) QueryWalletTransactionVo query
    );
}
