# 商品同步系统详细设计方案

## 1. 需求分析

### 1.1 业务背景
- **平台机构**（org_id=1）：拥有最高权限，可以创建和管理商品
- **B端机构**（org_id=2,3,4...）：需要从平台同步商品的机构
- **其他机构**：普通机构账号

### 1.2 核心需求
1. 平台商品可以同步到开启同步功能的B端机构
2. 平台修改商品后，可以更新到已同步的B端机构
3. 检测版本冲突：如果B端手动修改了从平台同步的商品，需要手动处理
4. 权限隔离：不同机构只能看到自己的商品

### 1.3 数据表结构
```sql
-- 商品主表
comm_product (
    id,
    product_no,           -- 商品编号（唯一标识）
    product_vid,          -- 商品版本号
    org_id,              -- 机构ID（1=平台，2=B端...）
    is_sync,             -- 同步状态(1:可同步,2:已同步,3:可更新)
    sync_source          -- 数据来源（0=B端手动创建，1=平台同步过来）
)

-- 商品数据表
comm_product_data (
    id,
    product_no,          -- 商品编号
    product_vid,         -- 商品版本号
    org_id,              -- 机构ID
    name,                -- 商品名称
    price,               -- 价格
    description,         -- 描述
    ... 其他详细字段
)

-- 商品同步配置表
comm_product_synchronization (
    id,
    org_id,              -- 机构ID
    synchronization_type, -- 同步类型
    consultation_button_text, -- 咨询按钮文案
    consultant_wechat,   -- 咨询顾问微信
    consultant_qrcode    -- 咨询顾问二维码
)
```

### 1.4 关键字段说明

#### sync_source字段（重要）
- **0**：B端机构手动创建/导入的商品
- **1**：从平台同步过来的商品
- **特点**：此字段在商品创建时设置，之后不会改变

## 2. 核心业务逻辑设计

### 2.1 同步状态管理

#### 状态定义
- **可同步(1)**：商品刚创建，可以同步到B端机构
- **已同步(2)**：商品已经同步到B端机构
- **可更新(3)**：平台商品有修改，可以更新到B端机构

#### 状态流转
```
创建商品 → 可同步(1)
    ↓ 点击"同步到B端"
已同步(2)
    ↓ 平台修改商品
可更新(3)
    ↓ 点击"更新到B端"
已同步(2) 或 可更新(3)（如果有冲突）
```

### 2.2 版本控制机制

#### 商品创建流程
1. 在`comm_product`表插入一条记录，`product_vid = 1`
2. 在`comm_product_data`表插入一条记录，`product_vid = 1`
3. 两条记录通过`product_no + product_vid`关联

#### 商品修改流程
1. 更新`comm_product`表的`product_vid = product_vid + 1`
2. 在`comm_product_data`表新增一条记录，使用新的`product_vid`
3. 查询时通过最大的`product_vid`获取最新数据

#### 示例
```
商品编号1001的版本演进：

创建时：
comm_product: product_no=1001, product_vid=1, org_id=1
comm_product_data: product_no=1001, product_vid=1, name="美国签证"

第一次修改：
comm_product: product_no=1001, product_vid=2, org_id=1
comm_product_data: 
  - product_no=1001, product_vid=1, name="美国签证"
  - product_no=1001, product_vid=2, name="美国留学签证" (新增)

第二次修改：
comm_product: product_no=1001, product_vid=3, org_id=1
comm_product_data:
  - product_no=1001, product_vid=1, name="美国签证"
  - product_no=1001, product_vid=2, name="美国留学签证"
  - product_no=1001, product_vid=3, name="美国留学签证服务" (新增)
```

### 2.3 同步机制设计

#### 首次同步流程
1. **检查权限**：确认当前用户是平台用户（org_id=1）
2. **获取目标机构**：查询`comm_product_synchronization`表，获取所有开启同步功能的B端机构
3. **复制商品数据**：
   - 复制`comm_product`记录到各B端机构，设置`sync_source=1`
   - 复制`comm_product_data`记录到各B端机构
4. **更新状态**：将平台商品的`is_sync`更新为2（已同步）

#### 更新同步流程
1. **查找已同步机构**：查询所有已同步该商品的B端机构
2. **版本冲突检测**：逐个检查每个机构是否手动修改过商品
3. **执行更新**：
   - 无冲突：直接更新商品数据
   - 有冲突：跳过该机构，记录冲突信息
4. **更新状态**：根据结果更新平台商品状态

### 2.4 冲突检测算法

#### 检测原理
由于`sync_source`字段不会改变，我们需要通过其他方式检测B端是否手动修改了从平台同步的商品。

#### 检测维度
1. **数据来源过滤**：只检测`sync_source=1`的商品（平台同步的）
2. **版本号比较**：B端版本号是否大于平台最后同步时的版本号
3. **修改时间检查**：B端最后修改时间是否晚于最后同步时间
4. **数据内容比较**：关键字段是否与平台数据不一致

#### 检测逻辑（方法1：版本号比较）
```
function isProductModified(platformProductNo, orgId) {
    // 1. 查询B端商品
    orgProduct = 查询B端商品(platformProductNo, orgId)

    if (orgProduct == null) {
        return false // 商品不存在，未修改
    }

    // 2. 只检测平台同步过来的商品
    if (orgProduct.sync_source != 1) {
        return false // 不是平台同步的商品，不参与冲突检测
    }

    // 3. 查询平台商品当前版本
    platformProduct = 查询平台商品(platformProductNo)

    // 4. 查询最后一次同步时的平台版本号
    lastSyncVersion = 查询最后同步版本(platformProductNo, orgId)

    // 5. 如果B端版本号大于最后同步时的版本，说明B端有修改
    if (orgProduct.product_vid > lastSyncVersion) {
        return true // B端版本更新，说明被手动修改过
    }

    return false
}
```

#### 检测逻辑（方法2：时间戳比较）
```
function isProductModified(platformProductNo, orgId) {
    orgProduct = 查询B端商品(platformProductNo, orgId)

    if (orgProduct == null || orgProduct.sync_source != 1) {
        return false // 商品不存在或不是平台同步的
    }

    // 查询最后一次同步时间
    lastSyncTime = 查询最后同步时间(platformProductNo, orgId)

    // 如果B端修改时间晚于最后同步时间，说明被手动修改
    return orgProduct.updated_at > lastSyncTime
}
```

## 3. 技术架构设计

### 3.1 分层架构

```
Controller层（API接口）
    ↓
Service层（业务逻辑）
    ↓
Repository层（数据访问）
    ↓
Database层（数据存储）
```

### 3.2 核心服务设计

#### ProductSyncService（商品同步服务）
```java
interface ProductSyncService {
    // 同步商品到所有开启同步的B端机构
    SyncResult syncProductToAllOrgs(Long productNo);
    
    // 更新商品到所有已同步的B端机构
    SyncResult updateProductToAllOrgs(Long productNo);
    
    // 检查商品同步状态
    Integer checkSyncStatus(Long productNo);
    
    // 检查是否有版本冲突
    boolean hasVersionConflict(Long productNo, Integer orgId);
}
```

#### SyncConfigService（同步配置服务）
```java
interface SyncConfigService {
    // 获取所有开启同步的机构ID
    List<Integer> getEnabledSyncOrgIds();
    
    // 检查机构是否开启同步
    boolean isSyncEnabled(Integer orgId);
    
    // 获取机构同步配置
    SyncConfig getSyncConfig(Integer orgId);
}
```

### 3.3 数据传输对象设计

#### SyncResult（同步结果）
```java
class SyncResult {
    boolean success;           // 是否成功
    String message;           // 结果消息
    int successCount;         // 成功数量
    int failCount;           // 失败数量
    int skipCount;           // 跳过数量
    List<Integer> successOrgIds;  // 成功的机构ID
    List<Integer> failOrgIds;     // 失败的机构ID
    List<ConflictInfo> conflicts; // 冲突信息
}
```

#### ConflictInfo（冲突信息）
```java
class ConflictInfo {
    Integer orgId;            // 机构ID
    String orgName;          // 机构名称
    String reason;           // 冲突原因
    Integer platformVersion; // 平台版本号
    Integer orgVersion;      // 机构版本号
}
```

## 4. API接口设计

### 4.1 同步商品到B端机构
```
POST /comm/product/syncToOrgs/{productNo}

功能：将平台商品同步到所有开启同步功能的B端机构

参数：
- productNo: 商品编号

响应：
{
  "success": true,
  "message": "同步完成，成功：2个，失败：0个",
  "data": {
    "successCount": 2,
    "failCount": 0,
    "skipCount": 0,
    "successOrgIds": [2, 3],
    "failOrgIds": [],
    "conflicts": []
  }
}
```

### 4.2 更新商品到B端机构
```
POST /comm/product/updateToOrgs/{productNo}

功能：将平台商品更新同步到已同步的B端机构

参数：
- productNo: 商品编号

响应：
{
  "success": true,
  "message": "更新完成，成功：1个，跳过：1个",
  "data": {
    "successCount": 1,
    "failCount": 0,
    "skipCount": 1,
    "successOrgIds": [2],
    "skipOrgIds": [3],
    "conflicts": [
      {
        "orgId": 3,
        "orgName": "机构3",
        "reason": "B端机构已手动修改商品",
        "platformVersion": 3,
        "orgVersion": 2
      }
    ]
  }
}
```

### 4.3 检查同步状态
```
GET /comm/product/syncStatus/{productNo}

功能：获取商品当前的同步状态

响应：
{
  "success": true,
  "data": 2  // 1:可同步, 2:已同步, 3:可更新
}
```

### 4.4 获取开启同步的机构
```
GET /comm/product/enabledSyncOrgs

功能：获取所有开启同步功能的机构列表

响应：
{
  "success": true,
  "data": [2, 3, 5, 8]
}
```

## 5. 前端交互设计

### 5.1 商品列表页面设计

#### 页面布局
```
商品列表
┌─────────────────────────────────────────────────────────┐
│ 商品名称    │ 商品编号 │ 版本号 │ 价格   │ 同步状态 │ 操作    │
├─────────────────────────────────────────────────────────┤
│ 美国留学签证 │ 1001    │ v3    │ ¥5000 │ 可同步   │[同步到B端]│
│ 英国大学申请 │ 1002    │ v2    │ ¥8000 │ 已同步   │[已同步]   │
│ 加拿大移民   │ 1003    │ v4    │ ¥12000│ 可更新   │[更新到B端]│
└─────────────────────────────────────────────────────────┘
```

#### 状态显示
- **可同步**：绿色标签，显示"同步到B端"蓝色按钮
- **已同步**：蓝色标签，显示"已同步"灰色禁用按钮
- **可更新**：橙色标签，显示"更新到B端"橙色按钮

### 5.2 操作流程设计

#### 同步操作流程
1. 用户点击"同步到B端"按钮
2. 前端发送API请求
3. 显示加载状态
4. 接收响应结果
5. 显示操作结果弹窗
6. 更新按钮状态

#### 结果展示
```
同步结果弹窗
┌─────────────────────────────────┐
│ 同步完成！                       │
│                                │
│ 商品：美国留学签证               │
│ 成功同步：2个机构               │
│ 失败同步：0个机构               │
│                                │
│ 成功机构：机构2, 机构3          │
│                                │
│           [确定]                │
└─────────────────────────────────┘
```

#### 冲突处理展示
```
更新结果弹窗
┌─────────────────────────────────┐
│ 更新完成！                       │
│                                │
│ 商品：加拿大移民咨询             │
│ 成功更新：1个机构               │
│ 跳过更新：1个机构               │
│                                │
│ ⚠️ 需要手动处理：               │
│ 机构3 - B端已手动修改商品        │
│                                │
│           [确定]                │
└─────────────────────────────────┘
```

### 5.3 JavaScript实现逻辑

#### 按钮状态管理
```javascript
function updateSyncButton(productNo, syncStatus) {
    const button = document.querySelector(`[data-product="${productNo}"]`);

    switch(syncStatus) {
        case 1: // 可同步
            button.textContent = '同步到B端';
            button.className = 'btn btn-primary';
            button.onclick = () => syncProduct(productNo);
            button.disabled = false;
            break;

        case 2: // 已同步
            button.textContent = '已同步';
            button.className = 'btn btn-secondary';
            button.disabled = true;
            break;

        case 3: // 可更新
            button.textContent = '更新到B端';
            button.className = 'btn btn-warning';
            button.onclick = () => updateProduct(productNo);
            button.disabled = false;
            break;
    }
}
```

#### API调用示例
```javascript
async function syncProduct(productNo) {
    try {
        showLoading('正在同步商品...');

        const response = await fetch(`/comm/product/syncToOrgs/${productNo}`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + getToken()
            }
        });

        const result = await response.json();

        if (result.success) {
            showSuccessDialog(result.data);
            updateSyncButton(productNo, 2); // 更新为已同步
        } else {
            showErrorDialog(result.message);
        }
    } catch (error) {
        showErrorDialog('同步失败：' + error.message);
    } finally {
        hideLoading();
    }
}
```

## 6. 业务场景处理

### 6.1 场景1：平台新建商品
**流程**：
1. 平台用户创建商品
2. 系统自动设置`is_sync = 1`（可同步）
3. 前端显示"同步到B端"按钮
4. 用户点击按钮触发同步

**数据变化**：
```
创建前：无数据

创建后：
comm_product: product_no=1001, product_vid=1, org_id=1, is_sync=1
comm_product_data: product_no=1001, product_vid=1, org_id=1, name="新商品"
```

### 6.2 场景2：首次同步到B端
**流程**：
1. 用户点击"同步到B端"
2. 系统查询开启同步的B端机构（假设机构2、3）
3. 复制商品数据到各机构
4. 更新平台商品状态为"已同步"

**数据变化**：
```
同步前：
comm_product: product_no=1001, product_vid=1, org_id=1, is_sync=1

同步后：
comm_product:
  - product_no=1001, product_vid=1, org_id=1, is_sync=2
  - product_no=1001, product_vid=1, org_id=2, is_sync=2, sync_source=1
  - product_no=1001, product_vid=1, org_id=3, is_sync=2, sync_source=1

comm_product_data:
  - product_no=1001, product_vid=1, org_id=1, name="新商品"
  - product_no=1001, product_vid=1, org_id=2, name="新商品"
  - product_no=1001, product_vid=1, org_id=3, name="新商品"
```

### 6.3 场景3：平台修改商品
**流程**：
1. 平台用户修改商品
2. 系统版本号+1，新增data记录
3. 更新状态为"可更新"
4. 前端显示"更新到B端"按钮

**数据变化**：
```
修改前：
comm_product: product_no=1001, product_vid=1, org_id=1, is_sync=2

修改后：
comm_product: product_no=1001, product_vid=2, org_id=1, is_sync=3

comm_product_data:
  - product_no=1001, product_vid=1, org_id=1, name="新商品"
  - product_no=1001, product_vid=2, org_id=1, name="修改后的商品" (新增)
```

### 6.4 场景4：B端手动修改从平台同步的商品
**流程**：
1. B端用户修改从平台同步过来的商品
2. 系统版本号+1，新增data记录，但`sync_source`保持为1
3. 平台更新时通过版本号或时间戳检测到冲突
4. 跳过该机构，提示手动处理

**数据变化**：
```
B端修改前：
comm_product: product_no=1001, product_vid=1, org_id=2, sync_source=1

B端修改后：
comm_product: product_no=1001, product_vid=2, org_id=2, sync_source=1 (保持不变)

comm_product_data:
  - product_no=1001, product_vid=1, org_id=2, name="新商品"
  - product_no=1001, product_vid=2, org_id=2, name="B端修改的商品" (新增)
```

### 6.4.1 场景4补充：B端手动创建商品
**流程**：
1. B端用户手动创建商品（不是同步的）
2. 系统设置`sync_source = 0`
3. 这类商品不参与平台同步流程

**数据变化**：
```
B端手动创建：
comm_product: product_no=2001, product_vid=1, org_id=2, sync_source=0

comm_product_data:
  - product_no=2001, product_vid=1, org_id=2, name="B端自创商品"
```

### 6.5 场景5：更新时处理冲突
**流程**：
1. 平台用户点击"更新到B端"
2. 系统检测机构2有冲突（B端修改过），机构3无冲突
3. 更新机构3，跳过机构2
4. 返回冲突信息

**处理逻辑**：
```
检查机构2：
- sync_source=1 (平台同步的商品)
- 版本号检测：B端版本 > 最后同步版本 → 有冲突，跳过

检查机构3：
- sync_source=1 (平台同步的商品)
- 版本号检测：B端版本 = 最后同步版本 → 无冲突，更新

结果：
- 成功：1个（机构3）
- 跳过：1个（机构2）
- 冲突信息：机构2需要手动处理
```

### 6.6 场景6：同步范围说明
**重要说明**：
- **只同步`sync_source=1`的商品**：即从平台同步过来的商品
- **不同步`sync_source=0`的商品**：即B端手动创建的商品
- **冲突检测只针对平台同步的商品**：B端自创商品不参与平台同步流程

## 7. 异常处理和边界情况

### 7.1 权限控制
- **检查点**：每个同步API调用前
- **验证逻辑**：`userInfo.orgId == 1`
- **失败处理**：返回403错误，提示"只有平台用户可以执行同步操作"

### 7.2 数据一致性
- **事务控制**：所有同步操作使用数据库事务
- **回滚机制**：任何步骤失败都回滚整个操作
- **重试机制**：网络异常时支持重试

### 7.3 并发控制
- **问题**：多用户同时操作同一商品
- **解决**：使用乐观锁或悲观锁
- **实现**：版本号检查或数据库锁

### 7.4 性能优化
- **批量操作**：使用批量插入减少数据库交互
- **异步处理**：大量机构同步时使用异步任务
- **缓存策略**：缓存机构配置信息

## 8. 测试策略

### 8.1 单元测试
- 同步服务各方法的单元测试
- 冲突检测算法测试
- 状态流转逻辑测试

### 8.2 集成测试
- API接口完整流程测试
- 数据库事务测试
- 权限控制测试

### 8.3 场景测试
- 各种业务场景的端到端测试
- 异常情况处理测试
- 并发操作测试

## 9. 部署和监控

### 9.1 部署要求
- 确保数据库表结构正确
- 配置相关权限和角色
- 部署前端页面和API接口

### 9.2 监控指标
- 同步成功率
- 冲突发生频率
- API响应时间
- 错误日志统计

### 9.3 运维工具
- 同步状态查询工具
- 数据修复工具
- 日志分析工具

## 10. 关键技术点说明

### 10.1 sync_source字段的正确理解
- **设置时机**：商品创建时设置，之后永不改变
- **0值含义**：B端机构手动创建/导入的商品
- **1值含义**：从平台同步过来的商品
- **冲突检测**：只针对`sync_source=1`的商品进行版本冲突检测

### 10.2 冲突检测策略
1. **过滤条件**：只检测`sync_source=1`的商品
2. **检测方法**：版本号比较、时间戳比较、数据内容比较
3. **处理方式**：有冲突的跳过更新，无冲突的直接更新

### 10.3 同步范围控制
- **同步目标**：只同步平台商品到B端机构
- **更新目标**：只更新B端的`sync_source=1`商品
- **排除范围**：B端`sync_source=0`的商品不参与平台同步流程

## 11. 总结

这个商品同步系统设计方案完整地解决了您需求文档中提到的所有问题：

### 核心特性
1. **版本控制**：通过版本号实现商品数据的版本管理
2. **智能同步**：自动识别开启同步功能的B端机构
3. **精准冲突检测**：基于版本号和时间戳检测B端手动修改
4. **状态管理**：清晰的同步状态流转机制
5. **权限隔离**：基于机构ID的数据和操作权限控制
6. **数据来源区分**：明确区分平台同步商品和B端自创商品

### 技术优势
- 完整的事务控制确保数据一致性
- 详细的错误处理和日志记录
- 灵活的前端交互设计
- 可扩展的架构设计
- 准确的冲突检测算法

### 业务价值
- 提高商品管理效率
- 减少手动操作错误
- 支持复杂的多机构业务场景
- 提供良好的用户体验
- 保护B端机构的自主商品数据

### 关键修正点
- **修正了sync_source字段的理解**：从"会改变的状态标记"改为"固定的来源标识"
- **优化了冲突检测算法**：基于版本号和时间戳而非sync_source变化
- **明确了同步范围**：只处理平台同步的商品，不影响B端自创商品

这个方案可以作为开发团队的详细指导文档，涵盖了从需求分析到技术实现的各个方面，特别是正确处理了sync_source字段的业务逻辑。
