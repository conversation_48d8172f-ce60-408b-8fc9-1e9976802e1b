package com.awg.account.externalService.impl;

import com.awg.account.entity.Wallet;
import com.awg.account.entity.WalletTransaction;
import com.awg.account.enums.WalletActionCodeEnum;
import com.awg.account.eo.ConsumeByWalletEo;
import com.awg.account.externalService.IWalletExternalService;
import com.awg.account.mapper.WalletMapper;
import com.awg.account.mapper.WalletTransactionMapper;
import com.awg.account.service.IWalletBaseService;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @since 2024-02-06
 */
@Service
public class WalletExternalServiceImpl extends ServiceImpl<WalletMapper, Wallet> implements IWalletExternalService {

    @Resource
    private IWalletBaseService walletBaseService;

    @Resource
    private Redisson redisson;

    @Resource
    private WalletTransactionMapper walletTransactionMapper;

    /**
     * <p>
     * 注册赠送
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW )
    public void registerGive(String walletNo){
        // 开启锁
        RLock lock = redisson.getLock("immiLock:account:wallet:registerGive");
        lock.lock();
        try {
            // 是否已经赠送过
            List<WalletTransaction> walletTransactionList = walletTransactionMapper.selectList(Wrappers.<WalletTransaction>lambdaQuery()
                    .eq(WalletTransaction::getWalletNo, walletNo)
                    .eq(WalletTransaction::getActionCode, WalletActionCodeEnum.REGISTER_GIFT.getCode())
                    .eq(WalletTransaction::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );

            if(walletTransactionList.size()<=0) {
                walletBaseService.registerGive(walletNo);
            }

        } catch (BusinessException e) {
            throw new BusinessException(e.getMessageCode(), e.getDetailMessage());
        } catch (Exception e) {
            // 抛出异常
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), e.getMessage());
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * <p>
     * 获取会员钱包编号
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public Long getMemberWalletNo(Integer memberId) {
        return walletBaseService.getMemberWallet(memberId).getWalletNo();
    }

    /**
     * <p>
     * 消费
     * </p>
     *
     * @param vo
     * @param userLoginInfoEo
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void consumeByWallet(ConsumeByWalletEo vo, UserLoginInfoEo userLoginInfoEo) {
        walletBaseService.consumeByWallet(vo, userLoginInfoEo);
    }

    /**
     * <p>
     * 订单佣金
     * </p>
     *
     * @param commission
     * @param memberId
     * @param orderNo
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void orderCommission(BigDecimal commission, Integer memberId, String orderNo, boolean isSelf ) {
        walletBaseService.orderCommission(commission, memberId, orderNo, isSelf);
    }

    /**
     * <p>
     * 订单退款
     * </p>
     *
     * @param loginInfoEo
     * @param refundAmount
     * @param memberId
     * @param orderNo
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void orderRefund(MemberLoginInfoEo loginInfoEo, BigDecimal refundAmount, Integer memberId, String orderNo, UserLoginInfoEo userLoginInfoEo ) {
        walletBaseService.orderRefund(loginInfoEo, refundAmount, memberId, orderNo, userLoginInfoEo);
    }

    /**
     * <p>
     * 订单佣金到账
     * </p>
     *
     * @param userLoginInfoEo
     * @param orderNo
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public List<Integer> orderCommissionArrival(UserLoginInfoEo userLoginInfoEo, String orderNo ){
        return walletBaseService.orderCommissionArrival(userLoginInfoEo, orderNo);
    }

    /**
     * <p>
     * 订单佣金作废
     * </p>
     *
     * @param userLoginInfoEo
     * @param orderNo
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public List<Integer> orderCommissionVoid(UserLoginInfoEo userLoginInfoEo, String orderNo, String remarks ){
        return walletBaseService.orderCommissionVoid(userLoginInfoEo, orderNo, remarks);
    }

}
