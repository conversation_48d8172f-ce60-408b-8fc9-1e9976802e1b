package com.awg.admin.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.enums.LeadsStatusEnum;
import com.awg.website.dto.CaseDto;
import com.awg.website.dto.CaseSyncDto;
import com.awg.website.dto.NewsDto;
import com.awg.website.entity.WebsiteCase;
import com.awg.website.entity.WebsiteCaseApprovalLetter;
import com.awg.website.entity.WebsiteCaseSharing;
import com.awg.website.mapper.WebsiteCaseApprovalLetterMapper;
import com.awg.website.mapper.WebsiteCaseMapper;
import com.awg.website.service.IWebsiteCaseService;
import com.awg.website.vo.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@ApiSupport( order = 53 )
@Api( tags = {"官网管理-案例相关接口"} )
@RestController
@RequestMapping( "/website/case" )
public class WebsiteCaseController extends BaseController {

    @Resource
    private IWebsiteCaseService websiteCaseService;

    @Resource
    private WebsiteCaseMapper websiteCaseMapper;

    @Resource
    private WebsiteCaseApprovalLetterMapper websiteCaseApprovalLetterMapper;

    @ApiOperationSupport(order = 5)
    @ApiOperation( "案例列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QueryCaseVo.class )
    })
    public DataResult list(@RequestBody QueryCaseVo vo) {
        ValidationUtils.validate(vo);
        vo.setWatermarkOrgId(getCurrentOrgId());

        BasePageResult<CaseDto> result = websiteCaseService.queryCaseList(vo, getCurrentOrgId(), 1);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 8)
    @ApiOperation( "案例详情" )
    @PostMapping( "/detail/{caseNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = CaseDto.class )
    })
    public DataResult detail(@PathVariable( value = "caseNo" ) String caseNo) {
        AssertUtils.isTrue( Long.parseLong(caseNo) <= 0, "No不合法");
        return renderSuccess(websiteCaseService.caseDetail(caseNo, getUserLoginInfoEo(), getCurrentOrgId()));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "添加案例" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult addCase(@RequestBody CaseVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        if(StringUtils.isBlank(vo.getRegion())) {
            vo.setRegion("CA");
        }

        ValidationUtils.validate(vo);

        // 循环验证步骤
        for(CaseStepVo stepVo: vo.getCaseStepList()){
            ValidationUtils.validate(stepVo);
        }

        return renderSuccess(websiteCaseService.addCase(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑案例" )
    @PostMapping( "/upd" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updCase(@RequestBody CaseVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        if(StringUtils.isBlank(vo.getRegion())) {
            vo.setRegion("CA");
        }

        ValidationUtils.validate(vo);

        // 循环验证步骤
        for(CaseStepVo stepVo: vo.getCaseStepList()){
            ValidationUtils.validate(stepVo);
        }

        return renderSuccess(websiteCaseService.updCase(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "切换首页显示状态" )
    @PostMapping( "/switchDisplayFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchCaseFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteCaseService.switchDisplayFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "切换置顶状态" )
    @PostMapping( "/switchTopFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult switchTopFlag(@RequestBody SwitchCaseFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteCaseService.switchTopFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 50)
    @ApiOperation( "删除案例" )
    @PostMapping( "/del/{caseNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delCase(@PathVariable( value = "caseNo" ) String caseNo) {
        AssertUtils.isTrue( Long.parseLong(caseNo) <= 0, "No不合法");
        websiteCaseService.delCase(caseNo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 60)
    @ApiOperation( "案例同步列表" )
    @PostMapping( "/syncList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuerySyncCaseList.class )
    })
    public DataResult syncList(@RequestBody QuerySyncCaseList vo) {
        ValidationUtils.validate(vo);
        AssertUtils.isTrue( true, "接口已停用");

        BasePageResult<CaseSyncDto> result = websiteCaseService.getSyncCaseList(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

}
