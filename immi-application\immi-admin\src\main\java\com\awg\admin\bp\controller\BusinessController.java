package com.awg.admin.bp.controller;

import com.awg.bp.dto.BusinessChildDto;
import com.awg.bp.dto.BusinessDto;
import com.awg.bp.dto.BusinessProcessDto;
import com.awg.bp.dto.BusinessTypeDto;
import com.awg.bp.mapper.BusinessProcessMapper;
import com.awg.bp.service.IBusinessService;
import com.awg.bp.service.IBusinessTypeService;
import com.awg.bp.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.qs.dto.SubmitDetailDto;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 35 )
@Api( tags = {"业务流程相关接口"} )
@RestController
@RequestMapping( "/bp/business" )
public class BusinessController extends BaseController {

    @Resource
    private IBusinessTypeService businessTypeService;

    @Resource
    private IBusinessService businessService;

    @Resource
    private BusinessProcessMapper businessProcessMapper;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "查询业务类型列表" )
    @PostMapping( "/getBusinessTypeList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessTypeDto.class )
    })
    public DataResult getBusinessTypeList(@RequestBody QueryBusinessTypeVo vo) {
        ValidationUtils.validate(vo);

        BasePageResult<BusinessTypeDto> result = businessTypeService.getBusinessTypeList(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "获取业务类型列表【全部】" )
    @PostMapping( "/getBusinessTypeListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessTypeDto.class )
    })
    public DataResult getBusinessTypeList() {
        return renderSuccess(businessTypeService.getBusinessTypeListAll());
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "添加业务类型" )
    @PostMapping( "/addBusinessType" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addBusinessType(@RequestBody AddBusinessTypeVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(businessTypeService.addBusinessType(vo));
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "修改业务类型" )
    @PostMapping( "/updateBusinessType" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateBusinessType(@RequestBody UpdateBusinessTypeVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(businessTypeService.updateBusinessType(vo));
    }

    @ApiOperationSupport(order = 50)
    @ApiOperation( "删除业务类型" )
    @PostMapping( "/delBusinessType/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delBusinessType(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "id不合法");
        return renderSuccess(businessTypeService.delBusinessType(id));
    }

    @ApiOperationSupport(order = 60)
    @ApiOperation( "获取业务列表【平台】" )
    @PostMapping( "/getPlatformBusinessList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessDto.class )
    })
    public DataResult getPlatformBusinessList(@RequestBody QueryBusinessVo vo) {

        vo.setOrgId(0);
        vo.setOwnerType(1);

        ValidationUtils.validate(vo);

        BasePageResult<BusinessDto> result = businessService.getPlatformBusinessList(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 70)
    @ApiOperation( "添加业务" )
    @PostMapping( "/addPlatformBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addPlatformBusiness(@RequestBody AddBusinessVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        return renderSuccess(businessService.addPlatformBusiness(vo));
    }

    @ApiOperationSupport(order = 80)
    @ApiOperation( "修改业务" )
    @PostMapping( "/updatePlatformBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updatePlatformBusiness(@RequestBody UpdateBusinessVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        return renderSuccess(businessService.updatePlatformBusiness(vo));
    }

    @ApiOperationSupport(order = 90)
    @ApiOperation( "回收业务" )
    @PostMapping( "/takeBackBusiness/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult takeBackBusiness(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "id不合法");
        if(getCurrentOrgType().equals(5)) {
            return renderSuccess(businessService.takeBackBusiness(id, getCurrentOrgId(), getCurrentOrgType(), 2));
        }
        else {
            return renderSuccess(businessService.takeBackBusiness(id, 0, getCurrentOrgType(), 1));
        }
    }

    @ApiOperationSupport(order = 100)
    @ApiOperation( "恢复业务" )
    @PostMapping( "/recoverBusiness/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult recoverBusiness(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "id不合法");
        if(getCurrentOrgType().equals(5)) {
            return renderSuccess(businessService.recoverBusiness(id, getCurrentOrgId(), getCurrentOrgType(), 2));
        }
        else {
            return renderSuccess(businessService.recoverBusiness(id, 0, getCurrentOrgType(), 1));
        }
    }

    @ApiOperationSupport(order = 102)
    @ApiOperation( "显示/隐藏业务" )
    @PostMapping( "/businessDisplayChange" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult businessDisplayChange(@RequestBody BusinessDisplayChangeVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        return renderSuccess(businessService.businessDisplayChange(vo));
    }

    @ApiOperationSupport(order = 110)
    @ApiOperation( "获取业务列表【含流程】" )
    @PostMapping( "/getBusinessList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessDto.class )
    })
    public DataResult getBusinessList(@RequestBody QueryBusinessVo vo) {

        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);

        List<BusinessDto> result = businessService.getBusinessList(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 120)
    @ApiOperation( "获取业务列表【含流程】【全部】" )
    @PostMapping( "/getBusinessListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessDto.class )
    })
    public DataResult getBusinessListAll(@RequestBody BusinessListAllVo vo) {

        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }
        ValidationUtils.validate(vo);
        List<BusinessDto> result = businessService.getBusinessListAll(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 125)
    @ApiOperation( "获取流程列表" )
    @PostMapping( "/getProcessList/{businessNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessProcessDto.class )
    })
    public DataResult getProcessList(@PathVariable( value = "businessNo" ) String businessNo) {
        AssertUtils.isTrue(StringUtils.isBlank(businessNo), "请先传入编号");
        return renderSuccess(businessProcessMapper.getProcessList(businessNo));
    }

    @ApiOperationSupport(order = 130)
    @ApiOperation( "添加流程" )
    @PostMapping( "/addProcess" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addProcess(@RequestBody AddProcessVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(businessService.addProcess(vo, getCurrentOrgType()));
    }

    @ApiOperationSupport(order = 140)
    @ApiOperation( "修改流程" )
    @PostMapping( "/updateProcess" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateProcess(@RequestBody EditProcessVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(businessService.updateProcess(vo, getCurrentOrgType()));
    }

    @ApiOperationSupport(order = 150)
    @ApiOperation( "删除流程" )
    @PostMapping( "/delProcess/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delProcess(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "id不合法");
        return renderSuccess(businessService.delProcess(id, getCurrentOrgType()));
    }

    @ApiOperationSupport(order = 160)
    @ApiOperation( "流程排序" )
    @PostMapping( "/sortProcess" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult sortProcess(@RequestBody SortProcessVo vo) {
        ValidationUtils.validate(vo);
        businessService.sortProcess(vo, getCurrentOrgType());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 170)
    @ApiOperation( "业务排序" )
    @PostMapping( "/sortBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult sortBusiness(@RequestBody SortBusinessVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        businessService.sortBusiness(vo, getCurrentOrgType());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 210)
    @ApiOperation( "获取业务子级列表" )
    @PostMapping( "/getChildBusinessList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessChildDto.class )
    })
    public DataResult getChildBusinessList(@RequestBody QueryBusinessVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        BasePageResult<BusinessChildDto> result = businessService.getChildBusinessList(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 220)
    @ApiOperation( "添加子级业务" )
    @PostMapping( "/addChildBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult addChildBusiness(@RequestBody ChildBusinessVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        return renderSuccess(businessService.addChildBusiness(vo));
    }

    @ApiOperationSupport(order = 230)
    @ApiOperation( "修改子级业务" )
    @PostMapping( "/updateChildBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateChildBusiness(@RequestBody ChildBusinessVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        return renderSuccess(businessService.updateChildBusiness(vo));
    }

    @ApiOperationSupport(order = 240)
    @ApiOperation( "删除子级业务" )
    @PostMapping( "/deleteChildBusiness/{childBusinessNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult deleteChildBusiness(@PathVariable( value = "childBusinessNo" ) String childBusinessNo) {
        AssertUtils.isTrue(StringUtils.isBlank(childBusinessNo), "请先传入子级编号");
        if(getCurrentOrgType().equals(5)) {
            businessService.deleteChildBusiness(childBusinessNo, getCurrentOrgId(), 2);
        }
        else {
            businessService.deleteChildBusiness(childBusinessNo, 0, 1);
        }
        return renderSuccess();
    }

    @ApiOperationSupport(order = 260)
    @ApiOperation( "获取子级业务列表【全部】" )
    @PostMapping( "/getChildBusinessListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessChildDto.class )
    })
    public DataResult getChildBusinessListAll(@RequestBody QueryBusinessVo vo) {
        vo.setPageNo(1);
        vo.setPageSize(50);
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
            vo.setOwnerType(2);
        }
        else {
            vo.setOrgId(0);
            vo.setOwnerType(1);
        }

        ValidationUtils.validate(vo);
        List<BusinessChildDto> result = businessService.getChildBusinessListAll(vo);
        return renderSuccess(result);
    }



}
