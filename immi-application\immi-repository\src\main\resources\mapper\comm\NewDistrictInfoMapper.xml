<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.awg.comm.mapper.NewDistrictInfoMapper">
    <select id="getDistrictListAll" resultType="com.awg.comm.dto.DistrictDto">
        select
            CASE
                WHEN di.from_platform_id > 0 THEN di.id
                ELSE di.id
            END AS districtId,
            di.pid,
            di.from_platform_id,
            di.display_flag,
            di.`order`,
            di.name
        from comm_district_info di
        <where>
            di.is_delete = 0
            and di.region_code = #{regionCode}
            <if test="query.orgId!=null">
                and di.org_id = #{query.orgId}
            </if>
            <if test="query.displayFlag!=null">
                and di.display_flag = #{query.displayFlag}
            </if>
        </where>
    </select>
</mapper>