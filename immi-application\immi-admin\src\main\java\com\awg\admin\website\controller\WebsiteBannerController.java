package com.awg.admin.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.website.dto.BannerDto;
import com.awg.website.service.IWebsiteBannerService;
import com.awg.website.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@ApiSupport( order = 53 )
@Api( tags = {"官网管理-banner相关接口"} )
@RestController
@RequestMapping( "/website/banner" )
public class WebsiteBannerController extends BaseController {

    @Resource
    private IWebsiteBannerService websiteBannerService;

    @ApiOperationSupport(order = 7)
    @ApiOperation( "获取banner列表" )
    @PostMapping( "/bannerList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BannerDto.class )
    })
    public DataResult getBannerList(@RequestBody QueryBannerVo vo) {
        ValidationUtils.validate(vo);
        vo.setOrgId(getUserLoginInfoEo().getOrgId());
        BasePageResult<BannerDto> result = websiteBannerService.queryBannerList(vo);

        // 循环填入banner完整路径
        for (BannerDto bannerDto : result.getData()) {
            bannerDto.setImageUrl(FileBaseUtil.getFileUrl(bannerDto.getImagePath()));
        }

        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "添加banner" )
    @PostMapping( "/addBanner" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addBanner(@RequestBody AddBannerVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");

        ValidationUtils.validate(vo);
        return renderSuccess(websiteBannerService.addBanner(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑banner" )
    @PostMapping( "/updateBanner" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateBanner(@RequestBody UpdateBannerVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteBannerService.updateBanner(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "切换banner显示状态" )
    @PostMapping( "/switchDisplayFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchDisplayFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteBannerService.switchDisplayFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "排序banner" )
    @PostMapping( "/sortBanner" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult sortBanner(@RequestBody WebsiteSortVo vo) {
        ValidationUtils.validate(vo);
        websiteBannerService.sortBanner(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 50)
    @ApiOperation( "删除banner" )
    @PostMapping( "/delBanner/{bannerNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delBanner(@PathVariable( value = "bannerNo" ) String bannerNo) {
        AssertUtils.isTrue( Long.parseLong(bannerNo) <= 0, "No不合法");
        websiteBannerService.delBanner(bannerNo, getUserLoginInfoEo());
        return renderSuccess();
    }

}
