package com.awg.common.base.controller;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.result.DataResult;
import com.awg.common.constant.UserTokenConstant;
import com.awg.common.exception.AssertUtils;
import com.awg.common.jwt.JWTUtils;
import com.awg.common.jwt.JwtConstant;
import com.awg.common.jwt.JwtProperties;
import com.awg.common.redis.RedisUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 基础controller
 * </p>
 *
 * @author: yang qiang
 * @date: 2019-11-18 14:16
 **/
public class BaseController {

    @Resource
    protected HttpServletRequest request;

    @Resource
    private RedisUtils redisUtils;

    /**
     * @param data: 业务数据
     * @description: 统一给前端人员规范格式（成功情况）
     * @author: yang qiang
     * @date: 2021/1/8 11:15
     * @return: com.awg.common.base.result.DataResult
     **/
    protected DataResult renderSuccess(Object data) {
        return DataResult.success(data);
    }

    /**
     * @param data: 业务数据
     * @description: 统一给前端人员规范格式（成功情况）
     * @author: yang qiang
     * @date: 2021/1/8 11:15
     * @return: com.awg.common.base.result.DataResult
     **/
    protected DataResult renderSuccess(Object data, String msg) {
        DataResult result = DataResult.success(data);
        result.setMsg(msg);
        return result;
    }

    /**
     * @description: 统一给前端人员规范格式（成功情况-不需要返回业务数据情况）
     * @author: yang qiang
     * @date: 2021/1/8 11:15
     * @return: com.awg.common.base.result.DataResult
     **/
    protected DataResult renderSuccess() {
        return DataResult.success();
    }

    protected DataResult renderSuccessToMap(String key, Object value) {
        Map<String, Object> map = new HashMap<>(16);
        map.put(key, value);
        DataResult result = DataResult.success(map);
        return result;
    }
    
    /**
     * @param code: 业务code
     * @param msg:  业务描述
     * @description: 统一给前端人员规范格式（自定义业务）
     * @author: yang qiang
     * @date: 2021/1/8 11:15
     * @return: com.awg.common.base.result.DataResult
     **/
    protected DataResult renderError(Integer code, String msg) {
        DataResult result = DataResult.fail(msg);
        result.setCode(code);
        return result;
    }

    /**
     * <p>
     * 获取token（不效验）
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-26
     * @return: java.lang.String
     */
    protected String getUserToken() {
        return request.getHeader(JwtProperties.TOKEN_KEY);
    }

    /**
     * @description: 获取当前登录人的accessToken
     * @author: yangqiang
     * @date: 2022/5/19 13:40
     * @return: java.lang.String
     **/
    protected String getCurrentAccessToken() {
        return request.getHeader(JwtProperties.TOKEN_KEY);
    }

    /**
     * -----------------------------------以下是后台相关------------------------------------------
     */

    /**
     * <p>
     * 获取登录的hashKey[redis里的key]
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected String getLoginHashKey () {

        String timeKey = getLoginTimeKey();
        String uid = String.valueOf(getUid());

        String hashKey = StringUtils.isEmpty(timeKey) ?
                UserTokenConstant.ADMIN_ONLINE_USER_PREFIX + uid :
                UserTokenConstant.ADMIN_ONLINE_USER_PREFIX + uid + ":" + timeKey ;

        return hashKey;
    }

    /**
     * <p>
     * 获取uid
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getUid() {
        return Integer.parseInt(parseToken(JwtConstant.UID));
    }

    /**
     * <p>
     * 获取当前登录的用户信息表id
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getUserInfoId () {
        return Integer.parseInt(parseToken(JwtConstant.USER_INFO_ID));
    }

    /**
     * <p>
     * 获取当前登录机构
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getCurrentOrgId () {
        return Integer.parseInt(parseToken(JwtConstant.CURRENT_ORG_ID));
    }

    /**
     * <p>
     * 获取登录的时间key
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected String getLoginTimeKey () {
        return String.valueOf(parseToken(JwtConstant.TIME_KEY));
    }

    /**
     * <p>
     * 获取当前登录机构类型
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getCurrentOrgType () {
        return Integer.parseInt(parseToken(JwtConstant.CURRENT_ORG_TYPE));
    }

    /**
     * <p>
     * 获取当前登录角色类型
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getCurrentRoleType () {
        return Integer.parseInt(parseToken(JwtConstant.CURRENT_ROLE_TYPE));
    }

    /**
     * <p>
     * 获取username
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.String
     */
    protected String getUsername() {
        return parseToken(JwtConstant.USERNAME);
    }

    /**
     * <p>
     * 获取用户登录信息
     * </p>
     *
     * @author: lun
     * @date: 2023-08-04
     * @return: UserLoginInfoEo
     */
    protected UserLoginInfoEo getUserLoginInfoEo () {
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setUserId(getUid());
        userLoginInfoEo.setUserInfoId(getUserInfoId());
        userLoginInfoEo.setOrgId(getCurrentOrgId());
        userLoginInfoEo.setOrgType(getCurrentOrgType());
        userLoginInfoEo.setRoleType(getCurrentRoleType());
        userLoginInfoEo.setIp(getIpAddrPro());

        return userLoginInfoEo;
    }

    /**
     * <p>
     * 效验token
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-11-04
     * @return: com.auth0.jwt.interfaces.DecodedJWT
     */
    protected DecodedJWT verifyToken() {
        String token = getUserToken();
        DecodedJWT decodedJwt = JWTUtils.verify(token);
        // 效验是否有效
        String uid = decodedJwt.getClaim(JwtConstant.UID).asString();
        String timeKey = decodedJwt.getClaim(JwtConstant.TIME_KEY).asString();

        String hashKey = StringUtils.isEmpty(timeKey) ?
                UserTokenConstant.ADMIN_ONLINE_USER_PREFIX + uid :
                UserTokenConstant.ADMIN_ONLINE_USER_PREFIX + uid + ":" + timeKey ;

        Map<Object, Object> result = redisUtils.hGetAll(hashKey);
        AssertUtils.isFalse(StringUtils.equals(token, String.valueOf(result.get("accessToken"))), BaseResponseCode.LOGIN_AUTH);

        return decodedJwt;
    }

    /**
     * <p>
     * 解析jwt信息
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.String
     */
    private String parseToken(String key) {
        DecodedJWT decodedJwt = verifyToken();

        return decodedJwt.getClaim(key).asString();
    }

    /**
     * -----------------------------------以下是app端相关------------------------------------------
     */

    /**
     * <p>
     * [APP端]获取登录的时间key
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected String getMemberLoginTimeKey () {
        return String.valueOf(parseMemberToken(JwtConstant.TIME_KEY));
    }

    /**
     * <p>
     * [app端版本]获取登录的hashKey[redis里的key]
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected String getMemberLoginHashKey () {

        String timeKey = getMemberLoginTimeKey();
        String memberId = String.valueOf(getCurrentMemberId());

        String hashKey = UserTokenConstant.APP_ONLINE_USER_PREFIX + memberId + ":" + timeKey;

        return hashKey;
    }

    /**
     * <p>
     * 效验token[app端]
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-11-04
     * @return: com.auth0.jwt.interfaces.DecodedJWT
     */
    protected DecodedJWT verifyMemberToken() {
        String token = getUserToken();

        AssertUtils.isTrue(StringUtils.isBlank(token), BaseResponseCode.LOGIN_AUTH);

        DecodedJWT decodedJwt = JWTUtils.verify(token);
        // 效验是否有效
        String memberId = decodedJwt.getClaim(JwtConstant.MEMBER_ID).asString();
        String timeKey = decodedJwt.getClaim(JwtConstant.TIME_KEY).asString();

        String hashKey = UserTokenConstant.APP_ONLINE_USER_PREFIX + memberId + ":" + timeKey;

        AssertUtils.isFalse(redisUtils.exists(hashKey), BaseResponseCode.LOGIN_AUTH);

        Map<Object, Object> result = redisUtils.hGetAll(hashKey);
        AssertUtils.isFalse(StringUtils.equals(token, String.valueOf(result.get("accessToken"))), BaseResponseCode.LOGIN_AUTH);

        return decodedJwt;
    }

    /**
     * <p>
     * 解析jwt信息[app端]
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.String
     */
    private String parseMemberToken(String key) {
        DecodedJWT decodedJwt = verifyMemberToken();

        return decodedJwt.getClaim(key).asString();
    }

    /**
     * <p>
     * app-获取当前登录人memberId
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getCurrentMemberId() {
        return Integer.parseInt(parseMemberToken(JwtConstant.MEMBER_ID));
    }

    /**
     * <p>
     * app-获取当前登录人memberNo
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Long
     */
    protected Long getCurrentMemberNo() {
        return Long.parseLong(parseMemberToken(JwtConstant.MEMBER_NO));
    }

    /**
     * <p>
     * app-获取当前登录人orgId
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-10-13
     * @return: java.lang.Integer
     */
    protected Integer getCurrentMemberOrgId() {
        return Integer.parseInt(parseMemberToken(JwtConstant.MEMBER_ORG_ID));
    }

    /**
     * <p>
     * [app端]获取会员登录信息
     * </p>
     *
     * @author: lun
     * @date: 2024-02-07
     * @return: MemberLoginInfoEo
     */
    protected MemberLoginInfoEo getMemberLoginInfoEo () {
        MemberLoginInfoEo memberLoginInfoEo = new MemberLoginInfoEo();

        memberLoginInfoEo.setMemberId(getCurrentMemberId());
        memberLoginInfoEo.setMemberNo(getCurrentMemberNo());
        memberLoginInfoEo.setOrgId(getCurrentMemberOrgId());
        memberLoginInfoEo.setIp(getIpAddrPro());

        return memberLoginInfoEo;
    }


    /**
     * -----------------------------------以下是通用------------------------------------------
     */

    /**
     * @description: 获取iP
     * @author: yangqiang
     * @date: 2022/5/19 13:40
     * @return: java.lang.String
     **/
    protected String getIpAddrPro() {

//        String ipAddress = request.getHeader("X-Real-IP");
//        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
//            ipAddress = request.getHeader("x-forwarded-for");
//        }

        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ipAddress = inet.getHostAddress();
            }
        }

        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress != null && ipAddress.length() > 15) {
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }

        return ipAddress;
    }

    /**
     * @description: 获取访问的域名
     * @author: lun
     * @date: 2023/10/10 13:40
     * @return: java.lang.String
     **/
    protected String getDomainHost(String domainHost) {
        String host = domainHost;
        if (host == null || host.length() == 0){
            host = request.getHeader("Host");
        }
        if (host == null || host.length() == 0){
            host = request.getHeader("HOST");
        }
        if (host == null || host.length() == 0){
            host = request.getHeader("host");
        }

        // 正则去掉多余的字符
        Pattern pattern = Pattern.compile("(?<=//|)((\\w)+\\.)+\\w+");
        Matcher matcher = pattern.matcher(host);
        if (matcher.find()) {
            return matcher.group();
        }
        else {
            return host;
        }
    }
}