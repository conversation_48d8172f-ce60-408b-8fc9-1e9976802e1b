package com.awg.comm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 批量同步商品请求 VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@ApiModel(value = "批量同步商品请求")
public class BatchSynchronizeProductVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "初始同步商品编号列表（第一次同步到B端的商品）", required = false)
    @Size(min = 1, message = "初始同步商品列表不能为空")
    private List<@NotEmpty(message = "商品编号不能为空") String> initialSyncProductNos;

    @ApiModelProperty(value = "更新同步商品编号列表（已同步过，需要更新到B端的商品）", required = false)
    @Size(min = 1, message = "更新同步商品列表不能为空")
    private List<@NotEmpty(message = "商品编号不能为空") String> updateSyncProductNos;



    /**
     * 获取所有商品编号总数
     */
    public int getTotalCount() {
        int count = 0;
        if (initialSyncProductNos != null) {
            count += initialSyncProductNos.size();
        }
        if (updateSyncProductNos != null) {
            count += updateSyncProductNos.size();
        }
        return count;
    }
}
