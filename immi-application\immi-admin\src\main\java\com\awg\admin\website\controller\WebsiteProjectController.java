package com.awg.admin.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.website.dto.BannerDto;
import com.awg.website.dto.ProjectDto;
import com.awg.website.service.IWebsiteProjectService;
import com.awg.website.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@ApiSupport( order = 50 )
@Api( tags = {"官网管理-项目相关接口"} )
@RestController
@RequestMapping( "/website/project" )
public class WebsiteProjectController extends BaseController {

    @Resource
    private IWebsiteProjectService websiteProjectService;

    @ApiOperationSupport(order = 6)
    @ApiOperation( "获取项目列表" )
    @PostMapping( "/projectList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProjectDto.class )
    })
    public DataResult getProjectList(@RequestBody QueryProjectVo vo) {
        ValidationUtils.validate(vo);
        vo.setOrgId(getUserLoginInfoEo().getOrgId());
        BasePageResult<ProjectDto> result = websiteProjectService.queryProjectList(vo,getUserLoginInfoEo());
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 9)
    @ApiOperation( "获取项目详情" )
    @PostMapping( "/projectDetail/{projectNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProjectDto.class )
    })
    public DataResult getProjectDetail(@PathVariable( value = "projectNo" ) String projectNo) {
        AssertUtils.isTrue( Long.parseLong(projectNo) <= 0, "No不合法");
        return renderSuccess(websiteProjectService.projectDetail(projectNo,getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 15)
    @ApiOperation( "添加项目" )
    @PostMapping( "/addProject" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addProject(@RequestBody AddProjectVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");

        ValidationUtils.validate(vo);
        return renderSuccess(websiteProjectService.addProject(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑项目" )
    @PostMapping( "/updateProject" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateProject(@RequestBody UpdateProjectVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteProjectService.updateProject(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "切换首页显示状态" )
    @PostMapping( "/switchDisplayFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchProjectDisplayFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteProjectService.switchDisplayFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "排序项目" )
    @PostMapping( "/sortProject" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult sortProject(@RequestBody WebsiteSortVo vo) {
        ValidationUtils.validate(vo);
        websiteProjectService.sortProject(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 50)
    @ApiOperation( "删除项目" )
    @PostMapping( "/delProject/{projectNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delProject(@PathVariable( value = "projectNo" ) String projectNo) {
        AssertUtils.isTrue( Long.parseLong(projectNo) <= 0, "No不合法");
        websiteProjectService.delProject(projectNo, getUserLoginInfoEo());
        return renderSuccess();
    }
}
