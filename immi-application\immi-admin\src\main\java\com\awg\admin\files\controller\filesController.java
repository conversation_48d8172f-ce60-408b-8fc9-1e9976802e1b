package com.awg.admin.files.controller;

import com.awg.comm.externalService.IMaterialExternalService;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileType;
import com.awg.common.utils.PathFormat;
import com.awg.file.entity.WatermarkLog;
import com.awg.file.externalService.IWatermarkImageExternalService;
import com.awg.file.mapper.WatermarkLogMapper;
import com.awg.plv.externalService.IPlvUploadRecordExternalService;
import com.awg.plv.utils.PolyvUtil;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.thirdparty.sdk.cos.TencentCOSUtils;
import com.awg.utils.date.DateUtils;
import com.awg.utils.pdf.PdfUtils;
import com.awg.utils.random.IdWorker;
import com.awg.utils.random.RandomString;
import com.awg.website.externalService.IWebsiteCaseExternalService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.io.InputStream;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 35 )
@Api( tags = {"文件相关接口"} )
@RestController
@RequestMapping( "/files" )
public class filesController extends BaseController {

    @Value( "${cos.key.prefix}" )
    private String cosKeyPrefix;

    @Value( "${cos.key.fileServiceUrlBase}" )
    private String fileUrlBase;

    @Value( "${immiFile.comm.material.watermark.key}" )
    private String materialWatermarkKey;

    @Resource
    private IWatermarkImageExternalService watermarkImageExternalService;

    @Resource
    private IOrgExternalService orgExternalService;

    @Resource
    private IWebsiteCaseExternalService websiteCaseExternalService;

    @Resource
    private IMaterialExternalService materialExternalService;

    @Resource
    private IPlvUploadRecordExternalService polyvUploadRecordService;

    @Resource
    private WatermarkLogMapper watermarkLogMapper;

    @ApiOperationSupport( order = 10 )
    @ApiOperation( value = "附件上传【type是类型，0=普通文件，1=水印图片，2获批信图片，3=材料图片，5=顾问牌照图，6=PDF材料，7仅打电商水印（不做其他处理，用于富文本）】，8商品视频" )
    @PostMapping( "/upload" )
    @DynamicResponseParameters(
            name = "mapUploadToImages",
            properties = {
                    @DynamicParameter( name = "url", value = "文件远程访问路径" ),
                    @DynamicParameter ( name = "cosKey", value = "文件远程相对路径" ),
                    @DynamicParameter ( name = "oldFileName", value = "文件名字[旧]" ),
                    @DynamicParameter ( name = "newFileName", value = "文件名字[新]" ),
                    @DynamicParameter ( name = "maxSize", value = "文件大小[单位：kb]" )
            } )
    public DataResult uploadToBrand(
            @RequestParam( "uploadFile" ) MultipartFile uploadFile,
            @RequestParam( value = "type", defaultValue = "0") Integer type
    ) {

        if(type==null) {
            type = 0;
        }

        // 文件大小
        long fileSize = uploadFile.getSize();

        String[] suffixList = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tif", ".tiff", ".svg"};
        String suffixTest = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
        if (Arrays.asList(suffixList).contains(suffixTest)) {
            // 图片类型
            AssertUtils.isTrue(fileSize>1048576L*10L, "图片大小不能超过10M");
        }
        else {
            // 非图片
            AssertUtils.isTrue(fileSize>1048576L*200L, "文件大小不能超过200M");
        }

        // 必须登录
        Integer loginUid = super.getUid();
        AssertUtils.isTrue(loginUid==null || loginUid<=0, "请登录后再操作");


        // 普通上传
        if (type == 0) {
            String newFileName = "";
            String suffix = "";
            try {
                String oldFileName = uploadFile.getOriginalFilename();
                suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
                oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());

                // 文件名字[新]
                newFileName =  oldFileName;

            } catch (Exception e) {
                throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
            }

            // 随机加密数
            String randomKey = RandomString.getRandomCode(12) + RandomString.getRandomCode(12) + IdWorker.getRandomNo();

            //cos存储同名字
            String bucketName = "file-immi";
            String savePath = cosKeyPrefix + "/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 保存到数据库的路径
            String dataPath = "/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 完整的路径
            String fileUrl = fileUrlBase + dataPath;

            // 上传一份到备份存储桶
            this.uploadToKodo(uploadFile, newFileName, savePath, "immi-private-657593", dataPath, fileUrl);

            Map<String, Object> result = this.uploadToKodo(uploadFile, newFileName, savePath, bucketName, dataPath, fileUrl);

            // 如果是tiff格式的文件，需要转换成png格式
            if (".tiff".equals(suffix)) {
                String rotateSavePath =  "/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                        + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName+".png";

                // 转换图片格式
                TencentCOSUtils.tiff2png(
                        bucketName,
                        cosKeyPrefix + result.get("dataPath").toString(),
                        cosKeyPrefix + rotateSavePath
                );
                result.put("dataPath", rotateSavePath);
                result.put("fileUrl", fileUrlBase + rotateSavePath);
                result.put("newFileName", newFileName+".png");
            }

            return renderSuccess(result);
        }

        // 水印上传
        if (type == 1) {
            String newFileName = "";
            try {
                String oldFileName = uploadFile.getOriginalFilename();
                String suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
                oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());

                // 文件名字[新]
                newFileName =  oldFileName;

            } catch (Exception e) {
                throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
            }

            // 随机加密数
            String randomKey = RandomString.getRandomCode(20) + RandomString.getRandomCode(16) + IdWorker.getRandomNo();

            //cos存储同名字
            String bucketName = "file-immi";
            String savePath = cosKeyPrefix + "/watermark/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 保存到数据库的路径
            String dataPath = "/watermark/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 完整的路径
            String fileUrl = fileUrlBase + dataPath;

            // 上传一份到备份存储桶
            this.uploadToKodo(uploadFile, newFileName, savePath, "immi-private-657593", dataPath, fileUrl);

            Map<String, Object> result = this.uploadToKodo(uploadFile, newFileName, savePath, bucketName, dataPath, fileUrl);

            // 准备旋转后的key
            randomKey = RandomString.getRandomCode(20) + RandomString.getRandomCode(16) + IdWorker.getRandomNo();
            String rotateSavePath =  "/watermark/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + result.get("newFileName").toString();

            // 旋转水印图片
            TencentCOSUtils.watermarkRotate(
                    bucketName,
                    cosKeyPrefix + result.get("dataPath").toString(),
                    cosKeyPrefix + rotateSavePath, "315"
            );

            Long watermarkNo = watermarkImageExternalService.updateWatermarkImage(rotateSavePath, result.get("dataPath").toString(), "315");

            result.put("watermarkNo", watermarkNo.toString());
            result.put("dataPath", rotateSavePath);
            result.put("fileUrl", fileUrlBase + rotateSavePath);

            return renderSuccess(result);
        }

        // 打水印
        if (type == 2 || type == 3 || type == 5 || type == 6 || type==7) {

            // 上传原始文件
            String newFileName = "";
            String suffix = "";
            try {
                String oldFileName = uploadFile.getOriginalFilename();
                suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
                oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());

                // 文件名字[新]
                newFileName =  oldFileName;

            } catch (Exception e) {
                throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
            }

            // 随机加密数
            String randomKey = RandomString.getRandomCode(20) + RandomString.getRandomCode(20) + IdWorker.getRandomNo();

            //cos存储同名字
            String bucketName = "file-immi";
            String savePath = cosKeyPrefix + "/private/source/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 保存到数据库的路径
            String dataPath = "/private/source/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 完整的路径
            String fileUrl = fileUrlBase + dataPath;

            // 上传一份到备份存储桶
            this.uploadToKodo(uploadFile, newFileName, savePath, "immi-private-657593", dataPath, fileUrl);

            // 上传到正式存储桶
            Map<String, Object> result = this.uploadToKodo(uploadFile, newFileName, savePath, bucketName, dataPath, fileUrl);

            // 记录原始文件，生成编号
            Long fileNo =  watermarkImageExternalService.WatermarkSourceImage(result.get("dataPath").toString(),1,null,super.getUserInfoId());

            // 准备新的结果
            Map<String, Object> newResult = new LinkedHashMap<>(18);

            if(type==2 || type==5) {
                // 打水印
                newResult = this.watermark(fileNo, result, bucketName, super.getCurrentOrgId(), type);
            }

            if(type==3 || type==7) {
                Long watermarkImageNo = orgExternalService.getWatermarkNo(getCurrentOrgId());
                String newSavePath = watermarkImageExternalService.watermarkImageBase(watermarkImageNo, fileNo, null, 0);
                if(type==3) {
                    materialExternalService.addMaterialImage(fileNo, newSavePath, 0);
                }

                newResult.put("oldFileName", result.get("oldFileName").toString());
                newResult.put("newFileName", result.get("newFileName").toString());
                newResult.put("maxSize", result.get("maxSize").toString());
                newResult.put("dataPath", newSavePath);
                newResult.put("fileUrl", fileUrlBase + newSavePath);
            }

            // PDF材料打水印
            if(type==6) {

                // 调用PDF打水印
                try {
                    Long watermarkImageNo = orgExternalService.getWatermarkNo(getCurrentOrgId());

                    // 水印后的路径
                    String newSavePath = watermarkImageExternalService.watermarkImageBase(watermarkImageNo, fileNo, null, 1);

                    String pdfSavePath = cosKeyPrefix + newSavePath;
                    String pdfDataPath = newSavePath;
                    String pdfFileUrl = fileUrlBase + newSavePath;

                    materialExternalService.addMaterialImage(fileNo, pdfDataPath, 1);

                    newResult.put("oldFileName", result.get("oldFileName").toString());
                    newResult.put("newFileName", result.get("newFileName").toString());
                    newResult.put("maxSize", result.get("maxSize").toString());
                    newResult.put("dataPath", pdfDataPath);
                    newResult.put("fileUrl", pdfFileUrl);

                } catch (Exception e) {
                    throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "PDF打水印异常，请联系管理员");
                }
            }

            // 填入文件编号
            newResult.put("fileNo", fileNo.toString());

            return renderSuccess(newResult);
        }

        if (type == 8) {
            String newFileName = "";
            try {
                String oldFileName = uploadFile.getOriginalFilename();
                String suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
                oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());

                // 文件名字[新]
                newFileName =  IdWorker.getRandomNo();

            } catch (Exception e) {
                throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
            }

            // 随机加密数
            String randomKey = RandomString.getRandomCode(8) + RandomString.getRandomCode(16) + IdWorker.getRandomNo();

            //cos存储同名字
            String bucketName = "file-immi";
            String savePath = cosKeyPrefix + "/productVideo/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 保存到数据库的路径
            String dataPath = "/productVideo/" + super.getUid() + "/" + super.getCurrentOrgId() + "/files/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

            // 完整的路径
            String fileUrl = fileUrlBase + dataPath;

            Map<String, Object> result = this.uploadToKodo(uploadFile, newFileName, savePath, bucketName, dataPath, fileUrl);

            // 记录原始文件，生成编号
            Long fileNo =  watermarkImageExternalService.WatermarkSourceImage(result.get("dataPath").toString(),2,1,super.getUserInfoId());

            String cataid = PolyvUtil.getProductVideoCataidByOrgId(1);
            Long uploadRecordNo = polyvUploadRecordService.addRecord(1, "", super.getUserInfoId(), fileNo);
            try {
                PolyvUtil.uploadVideoByCos(cataid, result.get("fileUrl").toString(), result.get("newFileName").toString(), uploadRecordNo.toString());
            } catch (IOException e) {
                throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "访问异常");
            }

            result.put("uploadRecordNo", uploadRecordNo.toString());

            return renderSuccess(result);
        }


        return renderSuccess();
    }

    private Map<String, Object> uploadToKodo(MultipartFile uploadFile, String newFileName, String savePath, String bucketName,
                                             String dataPath, String fileUrl) {
        Map<String, Object> result = new LinkedHashMap<>(18);
        // 文件名字
        String oldFileName = null;
        // 文件大小[单位：kb 1KB=1024字节 1MG=1024KB ]
        long maxSize = 0L;

        // 文件后缀
        String suffix = null;
        // 文件上传到cos后台访问域名
        String url = null;
        InputStream inputStream = null;
        try {
            if (uploadFile == null) {
                // 附件为空
                throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "附件不允许为空!");
            }
            maxSize = uploadFile.getSize();

            oldFileName = uploadFile.getOriginalFilename();
            suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
            oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());
            savePath = savePath + suffix;
            savePath = PathFormat.parse(savePath, oldFileName);
            dataPath = dataPath + suffix;
            dataPath = PathFormat.parse(dataPath, oldFileName);
            fileUrl = fileUrl + suffix;
            fileUrl = PathFormat.parse(fileUrl, oldFileName);
            inputStream = uploadFile.getInputStream();
            // 将文件上传到腾讯云服务器,通过文件流的方式
            url = TencentCOSUtils.uploadFile(bucketName, savePath, inputStream);
            inputStream.close();
        } catch (Exception e) {
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
        } finally {
            // 防止内存溢出，关闭文件流
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
                }
            }
        }
        // result.put("url", url);
        result.put("oldFileName", oldFileName + suffix);
        result.put("newFileName", newFileName + suffix);
        result.put("maxSize", maxSize);
//        result.put("cosKey", savePath);   // coskey不用返回
        result.put("dataPath", dataPath);
        result.put("fileUrl", fileUrl);
        return result;
    }

    private Map<String, Object> watermark(
            Long fileNo, Map<String, Object> result,
            String bucketName, Integer orgId, Integer type
    ) {

        if(orgId==null || orgId<=0) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "机构编号不允许为空!");
        }

        // 准备新的结果
        Map<String, Object> newResult = new LinkedHashMap<>(18);

        // 获取要处理的org列表
        List<Integer> orgList = new ArrayList<>();

        if(type==2){
            // 获批信图
            orgList = websiteCaseExternalService.getSharingOrgList(orgId);
        }
        else if(type==5){
            // 顾问牌照图
            orgList.add(orgId);
        }

        for( Integer currentOrgId : orgList ) {

            // 找出水印图
            Long watermarkImageNo = orgExternalService.getWatermarkNo(orgId);
            String newSavePath = watermarkImageExternalService.watermarkImage(watermarkImageNo, fileNo, currentOrgId, false, type);

            if(currentOrgId.equals(orgId)) {
                newResult.put("oldFileName", result.get("oldFileName").toString());
                newResult.put("newFileName", result.get("newFileName").toString());
                newResult.put("maxSize", result.get("maxSize").toString());
                newResult.put("dataPath", newSavePath);
                newResult.put("fileUrl", fileUrlBase + newSavePath);
            }
        }

        return newResult;
    }

    /**
     * 网络图片转成字节数组
     */
    private byte[] imageUrlToBytes(String imageUrl) {

        byte[] imageBytes = null;

        if(StringUtils.isBlank(imageUrl)) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "图片地址不允许为空");
        }

        try {
            URL url = new URL(imageUrl);
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(3 * 1000);
            InputStream inStream = conn.getInputStream();

            ByteArrayOutputStream outStream = new ByteArrayOutputStream();

            // 创建一个Buffer字符串
            byte[] buffer = new byte[1024];
            // 每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            // 使用一个输入流从buffer里把数据读取出来
            while ((len = inStream.read(buffer)) != -1) {
                // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                outStream.write(buffer, 0, len);
            }

            imageBytes = outStream.toByteArray();

            inStream.close();
            outStream.close();

        } catch (Exception e) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "数据异常，请联系管理员");
        }

        if(imageBytes==null || imageBytes.length==0) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "数据异常，请联系管理员");
        }

        return imageBytes;
    }
}
