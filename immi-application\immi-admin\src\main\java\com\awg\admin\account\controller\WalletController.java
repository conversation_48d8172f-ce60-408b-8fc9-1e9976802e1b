package com.awg.admin.account.controller;

import com.awg.account.dto.WalletInfoDto;
import com.awg.account.dto.WalletTransactionItemDto;
import com.awg.account.service.IWalletService;
import com.awg.account.vo.AdjustBalanceVo;
import com.awg.account.vo.QueryWalletTransactionVo;
import com.awg.comm.dto.ProductItemDto;
import com.awg.comm.vo.QueryProductVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 230 )
@Api( tags = {"钱包相关接口"} )
@RestController
@RequestMapping( "/account/wallet" )
public class WalletController extends BaseController {

    @Resource
    private IWalletService walletService;

    @ApiOperation( "B端钱包信息" )
    @PostMapping( "/business/walletInfo/{walletNo}" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WalletInfoDto.class )
    })
    public DataResult businessWalletInfo(@PathVariable( value = "walletNo" ) String walletNo){
        AssertUtils.isTrue(StringUtils.isBlank(walletNo), "钱包编号不能为空");
        return renderSuccess(walletService.getBusinessWalletInfo(walletNo));
    }

    @ApiOperation( "B端钱包交易流水" )
    @PostMapping( "/business/walletTransactionList" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WalletTransactionItemDto.class )
    })
    public DataResult businessWalletTransaction(@RequestBody QueryWalletTransactionVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<WalletTransactionItemDto> result = walletService.businessWalletTransactionList(vo);

        return renderSuccess(result);
    }

    @ApiOperation( "B端钱包手动增减" )
    @PostMapping( "/business/adjustChange" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult businessAdjustChange(@RequestBody AdjustBalanceVo vo){
        ValidationUtils.validate(vo);
        walletService.adjustBusinessBalance(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "C端钱包信息" )
    @PostMapping( "/consumer/walletInfo/{walletNo}" )
    @ApiOperationSupport(order = 40)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WalletInfoDto.class )
    })
    public DataResult consumerWalletInfo(@PathVariable( value = "walletNo" ) String walletNo){
        AssertUtils.isTrue(StringUtils.isBlank(walletNo), "钱包编号不能为空");
        return renderSuccess(walletService.getConsumerWalletInfo(walletNo));
    }

    @ApiOperation( "C端钱包交易流水" )
    @PostMapping( "/consumer/walletTransactionList" )
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WalletTransactionItemDto.class )
    })
    public DataResult consumerWalletTransaction(@RequestBody QueryWalletTransactionVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<WalletTransactionItemDto> result = walletService.consumerWalletTransactionList(vo);

        return renderSuccess(result);
    }

    @ApiOperation( "c端钱包手动增减" )
    @PostMapping( "/consumer/adjustChange" )
    @ApiOperationSupport(order = 60)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult consumerAdjustChange(@RequestBody AdjustBalanceVo vo){
        ValidationUtils.validate(vo);
        walletService.adjustConsumerBalance(vo, getUserLoginInfoEo());
        return renderSuccess();
    }
}
