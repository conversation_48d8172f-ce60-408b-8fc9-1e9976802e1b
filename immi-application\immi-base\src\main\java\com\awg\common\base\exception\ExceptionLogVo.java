package com.awg.common.base.exception;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2020/11/12 11:25
 * @version: V1.0
 **/
@Data
public class ExceptionLogVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty ( value = "异常id" )
    private String id;

    /**
     * 异常来源编码
     */
    @ApiModelProperty ( value = "异常来源编码[1：app-consumer  2：app-provider 3：admin-consumer 4：admin-provider]" )
    private Integer code;

    /**
     * 标题
     */
    @ApiModelProperty ( value = "标题" )
    private String title;

    /**
     * 异常类路径
     */
    @ApiModelProperty ( value = "异常类路径" )
    private String classPath;

    /**
     * 异常方法
     */
    @ApiModelProperty ( value = "异常方法" )
    private String method;

    /**
     * 异常发生的行号
     */
    @ApiModelProperty ( value = "异常发生的行号" )
    private Integer lineNumber;

    /**
     * 异常内容
     */
    @ApiModelProperty ( value = "异常内容" )
    private String content;

    /**
     * 创建日期
     */
    protected Date createTime;

}
