package com.awg.crm.vo;

import com.awg.crm.entity.CrmLeadsGovernmentScreenshot;
import com.awg.mybatis.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "政府费参数")
public class CrmLeadsGovernmentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "政府费id")
    private Integer id;

    @ApiModelProperty(value = "潜客id")
    private Integer leadsId;

    @NotNull(message = "费用名称不能为空")
    @Size(max = 100, message = "费用名称长度不能超过100个字符")
    @ApiModelProperty(value = "费用名称", required = true)
    private String feeName;

    @NotNull(message = "实付金额不能为空")
    @DecimalMax(value = "100000000", message = "实付金额不能超过1亿")
    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal actualAmount;

    @NotNull(message = "金额单位不能为空")
    @ApiModelProperty(value = "金额单位", required = true)
    private String currencyUnit;

    @ApiModelProperty("支付截图")
    private List<CrmLeadsGovernmentScreenshotVo> screenshots;

}
