package com.awg.crm.service;

import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.crm.dto.CrmLeadsGovernmentDto;
import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.vo.CrmLeadsGovernmentVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * @ProjectName
 * @ClassName CrmLeadsGovernmentService
 * @Author: ZhouJun
 * @Date: 2025-07-09
 * @Description:
 **/
public interface CrmLeadsGovernmentService extends IService<CrmLeadsGovernment> {

    /**
     * 根据潜客id查询政府费列表
     * @param leadsId
     */
    List<CrmLeadsGovernmentDto> listByLeadsId(Long leadsId);


    /**
     * 根据政府费id查询政府费
     * @param id
     */
    CrmLeadsGovernmentDto getById(Long id);


    /**
     * 新增政府费
     * @param crmLeadsGovernmentVo
     * @return
     */
    void createCrmLeadsGovernment(CrmLeadsGovernmentVo crmLeadsGovernmentVo, UserLoginInfoEo userLoginInfoEo);


    /**
     * 根据政府费id删除政府费
     * @param leadsGovernmentId
     */
    void deleteCrmLeadsGovernmentByld( Long leadsGovernmentId, UserLoginInfoEo userLoginInfoEo);


    /**
     * 根据政府费id删除政府费
     * @param crmLeadsGovernmentVo
     */
    void updateCrmLeadsGovernment( CrmLeadsGovernmentVo crmLeadsGovernmentVo , UserLoginInfoEo userLoginInfoEo);





//    /**
//     * 根据Id查询
//     *
//     * @param id
//     * @return
//     */
//    CrmLeadsGovernment getCrmLeadsGovernmentById(String id);
//
//    /**
//     * 批量查询
//     * @param bean
//     * @return
//     */
//    List<CrmLeadsGovernment> listCrmLeadsGovernment(CrmLeadsGovernment bean);
//
//    /**
//     * 分页查询
//     */
//    IPage<CrmLeadsGovernment> listCrmLeadsGovernmentByPage(Page<CrmLeadsGovernment> page, CrmLeadsGovernment bean);
//
//    /**
//     * 新增
//     * @param bean
//     */
//    Integer addCrmLeadsGovernment(CrmLeadsGovernment bean);
//
//    /**
//     * 根据id修改
//     * @param bean
//     */
//    Integer modifyCrmLeadsGovernmentById(CrmLeadsGovernment bean);
//
//    /**
//     * 根据id删除
//     * @param id
//     */
//    Integer removeCrmLeadsGovernmentById(String id);

}
