package com.awg.comm.mapper;

import com.awg.comm.dto.PlatformSyncProductDto;
import com.awg.comm.entity.NewProduct;
import com.awg.comm.entity.Product;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface NewProductMapper extends BaseMapper<NewProduct> {



    /**
     * 查询当前平台商品的所有B端同步商品信息
     * @param platformProductNo 平台商品编号
     * @return 平台同步商品信息列表
     */
    List<PlatformSyncProductDto> getPlatformSyncProducts(@Param("platformProductNo") Long platformProductNo);

    /**
     * 更新商品的平台同步版本号
     * @param productNo 商品编号
     * @param platformSyncVid 平台同步版本号
     * @return 更新影响的行数
     */
    int updatePlatformSyncVid(@Param("productNo") String productNo, @Param("platformSyncVid") Integer platformSyncVid);
}
