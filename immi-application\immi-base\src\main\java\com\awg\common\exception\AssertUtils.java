package com.awg.common.exception;

import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * <b>AssertUtil</b> is
 * 断言工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/4/15 17:16
 */

@SuppressWarnings ( "unused" )
@Slf4j
public class AssertUtils {

    /**
     * <p>
     * 该方法会通过反射获取异常枚举定义的内容，所以该参数应该是个枚举且属性为code和msg
     * </p>
     *
     * @param obj: 这里参数实际上应该为定义的异常枚举
     * @description: 异常枚举内容获取
     * @author: 夜晓
     * @date: 2021/4/16
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     */
    private static Map<String, Object> getExceptionContent(Object obj) {
        Map<String, Object> content = new HashMap<>(32);

        try {
            //code
            Method codeMethod = obj.getClass().getMethod("getCode");
            Object code = codeMethod.invoke(obj);
            content.put("code", code);
            //msg
            Method msgMethod = obj.getClass().getMethod("getMsg");
            Object msg = msgMethod.invoke(obj);
            content.put("msg", msg);
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            log.error("异常枚举解析出错：{0}", e.fillInStackTrace());
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "异常枚举解析出错");
        }

        return content;
    }

    /**
     * 如果为真，则抛出异常
     *
     * @param expression 判断条件
     * @param message    expression = true，抛出异常信息
     */
    public static void isTrue(boolean expression, Integer code, String message) {
        if (expression) {
            throw new BusinessException(code, message);
        }
    }

    public static void isTrue(boolean expression, Object exception) {
        if (expression) {
            throw new BusinessException(getExceptionContent(exception));
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }

    /**
     * 如果为假，则抛出异常
     *
     * @param expression 判断条件
     * @param message    expression = false，抛出异常信息
     */
    public static void isFalse(boolean expression, Integer code, String message) {
        if (!expression) {
            throw new BusinessException(code, message);
        }
    }

    public static void isFalse(boolean expression, Object exception) {
        if (!expression) {
            throw new BusinessException(getExceptionContent(exception));
        }
    }

    public static void isFalse(boolean expression, String message) {
        if (!expression) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }

    public static void isFalse(boolean expression) {
        if (!expression) {
            throw new BusinessException(BaseResponseCode.CODE_ERROR);
        }
    }

    /**
     * 为空
     *
     * @param object  判断对象
     * @param message 为空抛出异常信息
     */
    public static void isNull(Object object, Integer code, String message) {
        if (object == null) {
            throw new BusinessException(code, message);
        }
    }

    public static void isNull(Object object, Object exception) {
        if (object == null) {
            throw new BusinessException(getExceptionContent(exception));
        }
    }

    public static void isNull(Object object, String message) {
        if (object == null) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }

    public static void isNull(Object object) {
        if (object == null) {
            throw new BusinessException(BaseResponseCode.WEB_ERROR.getCode(), BaseResponseCode.WEB_ERROR.getMsg());
        }
    }

    /**
     * 不为空
     *
     * @param object  判断对象
     * @param message 不为空抛出异常信息
     */
    public static void notNull(Object object, Integer code, String message) {
        if (object != null) {
            throw new BusinessException(code, message);
        }
    }

    public static void notNull(Object object, Object exception) {
        if (object != null) {
            throw new BusinessException(getExceptionContent(exception));
        }
    }

    public static void notNull(Object object, String message) {
        if (object != null) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }

    /**
     * 为空字符串
     *
     * @param text    字符串
     * @param message 为空字符串抛出异常
     */
    public static void noLength(String text, Integer code, String message) {
        if (StringUtils.isBlank(text)) {
            throw new BusinessException(code, message);
        }
    }

    public static void noLength(String text, Object exception) {
        if (StringUtils.isBlank(text)) {
            throw new BusinessException(getExceptionContent(exception));
        }
    }

    public static void noLength(String text, String message) {
        if (StringUtils.isBlank(text)) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }

    /**
     * 数组为空抛出异常
     *
     * @param array   数组
     * @param message 为空抛出异常
     */
    public static void isEmpty(Object[] array, Integer code, String message) {
        if (ObjectUtils.isEmpty(array)) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 没有空对象
     *
     * @param array   数组
     * @param message 包含空对象抛出异常
     */
    public static void existNullElements(Object[] array, Integer code, String message) {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    throw new BusinessException(code, message);
                }
            }
        }
    }

    /**
     * 为空抛出异常
     *
     * @param collection 集合
     * @param message    为空抛出异常
     */
    public static void isEmpty(Collection<?> collection, Integer code, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(code, message);
        }
    }

    public static void isEmpty(Collection<?> collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }

    /**
     * map不为空
     *
     * @param map     hash表
     * @param message 为空抛出异常
     */
    public static void isEmpty(Map<?, ?> map, Integer code, String message) {
        if (CollectionUtils.isEmpty(map)) {
            throw new BusinessException(code, message);
        }
    }

    public static void isEmpty(Map<?, ?> map, String message) {
        if (CollectionUtils.isEmpty(map)) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), message);
        }
    }
}
