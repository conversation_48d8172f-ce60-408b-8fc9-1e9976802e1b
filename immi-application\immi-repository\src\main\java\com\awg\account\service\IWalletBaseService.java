package com.awg.account.service;

import com.awg.account.entity.Wallet;
import com.awg.account.eo.ConsumeByWalletEo;
import com.awg.account.vo.AdjustBalanceVo;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 钱包基础 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface IWalletBaseService extends IService<Wallet> {

    /**
     * <p>
     * 调整余额
     * </p>
     *
     * @param vo
     * @param userLoginInfoEo
     */
    void adjustBalance(AdjustBalanceVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 会员注册赠送
     * </p>
     *
     * @param walletNo
     */
    void registerGive(String walletNo);

    /**
     * <p>
     * 根据钱包编号获取钱包
     * </p>
     *
     * @return:
     */
    Wallet getWalletByWalletNo(String walletNo);

    /**
     * <p>
     * 获取会员钱包
     * </p>
     *
     * @return:
     */
    Wallet getMemberWallet(Integer memberId);

    Wallet getMemberWalletSubMethod(Integer memberId);

    /**
     * <p>
     * 消费
     * </p>
     *
     * @param vo
     * @param userLoginInfoEo
     */
    void consumeByWallet(ConsumeByWalletEo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 订单佣金
     * </p>
     *
     * @param commission
     * @param memberId
     * @param orderNo
     */
    void orderCommission(BigDecimal commission, Integer memberId, String orderNo, boolean isSelf );

    /**
     * <p>
     * 订单退款
     * </p>
     *
     * @param loginInfoEo
     * @param refundAmount
     * @param memberId
     * @param orderNo
     */
    void orderRefund(MemberLoginInfoEo loginInfoEo, BigDecimal refundAmount, Integer memberId, String orderNo, UserLoginInfoEo userLoginInfoEo );

    /**
     * <p>
     * 订单返佣金额到账
     * </p>
     *
     * @param userLoginInfoEo
     * @param orderNo
     */
    List<Integer> orderCommissionArrival(UserLoginInfoEo userLoginInfoEo, String orderNo );

    /**
     * <p>
     * 订单返佣金额作废
     * </p>
     *
     * @param userLoginInfoEo
     * @param orderNo
     */
    List<Integer> orderCommissionVoid(UserLoginInfoEo userLoginInfoEo, String orderNo, String remarks );

}
