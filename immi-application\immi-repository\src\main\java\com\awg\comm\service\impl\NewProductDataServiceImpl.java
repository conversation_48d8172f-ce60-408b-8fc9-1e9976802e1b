package com.awg.comm.service.impl;

import com.awg.comm.entity.NewProductData;
import com.awg.comm.mapper.NewProductDataMapper;
import com.awg.comm.service.NewProductDataService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 商品数据表 服务实现类 - 新版本
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class NewProductDataServiceImpl extends ServiceImpl<NewProductDataMapper, NewProductData> implements NewProductDataService {



    @Override
    public NewProductData getByProductNoAndVid(String productNo, Integer productVid) {
        return this.getOne(Wrappers.<NewProductData>lambdaQuery()
                .eq(NewProductData::getProductNo, productNo)
                .eq(NewProductData::getProductVid, productVid)
        );
    }



}
