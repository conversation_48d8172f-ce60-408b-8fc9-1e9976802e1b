package com.awg.admin.qs.controller;

import com.awg.system.dto.LoginInfoDto;
import com.awg.bp.dto.BusinessTypeDto;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.qs.dto.*;
import com.awg.qs.service.IQuestionnaireService;
import com.awg.qs.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 30 )
@Api( tags = {"问卷相关接口"} )
@RestController
@RequestMapping( "/qs/questionnaire" )
public class QuestionnaireController  extends BaseController {

    // 引入服务类
    @Resource
    private IQuestionnaireService questionnaireService;

    @ApiOperation( "获取问卷填写记录列表详情" )
    @PostMapping( "/getFillList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = FillListDto.class )
    })
    public DataResult getFillList(@RequestBody FillListVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(questionnaireService.getQuestionnaireSubmitList(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "获取问卷填写记录详情" )
    @PostMapping( "/getFillDetail" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = SubmitDetailDto.class )
    })
    public DataResult getFillDetail(@RequestBody GetSubmitDetailVo vo) {
        ValidationUtils.validate(vo);
        SubmitDetailDto submitDetailDto = questionnaireService.getQuestionaireSubmitDetail(vo, getUserLoginInfoEo());
        return renderSuccess(submitDetailDto);
    }

    @ApiOperation( "编辑问卷填写记录" )
    @PostMapping( "/editFillRecord" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = SubmitDto.class )
    })
    public DataResult getFillDetail(@RequestBody EditFillVo vo) {
        ValidationUtils.validate(vo);
        SubmitDto submitDto = questionnaireService.editFillRecord(vo, getUserInfoId(), getIpAddrPro(), getUserLoginInfoEo());
        return renderSuccess(submitDto);
    }

    @ApiOperation( "查询问卷列表" )
    @PostMapping( "/getQuestionnaireList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionnaireDto.class )
    })
    public DataResult getQuestionnaireList(@RequestBody QueryQuestionnaireVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
        }
        else {
            vo.setOrgId(1);
        }

        ValidationUtils.validate(vo);
        BasePageResult<QuestionnaireDto> result = questionnaireService.getQuestionnaireList(vo);
        return renderSuccess(result);
    }

    @ApiOperation( "获取所有问卷" )
    @PostMapping( "/getAllQuestionnaire" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionnaireDto.class )
    })
    public DataResult getAllQuestionnaireList() {
        Integer orgId = 1;
        if(getCurrentOrgType().equals(5)) {
            orgId = getCurrentOrgId();
        }
        return renderSuccess(questionnaireService.getAllQuestionnaireList(orgId));
    }

    @ApiOperation( "查询问卷详情" )
    @PostMapping( "/getQuestionnaireDetail" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionDetailDto.class )
    })
    public DataResult getQuestionnaireDetail(@RequestBody QuestionnaireNoVo vo) {
        Integer orgId = 1;
        if(getCurrentOrgType().equals(5)) {
            orgId = getCurrentOrgId();
        }
        vo.setOrgId(orgId);
        ValidationUtils.validate(vo);
        QuestionDetailDto questionDetailDto = questionnaireService.getQuestionnaireDetail(vo);
        return renderSuccess(questionDetailDto);
    }

    @ApiOperation( "添加问卷" )
    @PostMapping( "/addQuestionnaire" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult addQuestionnaire(@RequestBody QuestionnaireVo vo) {
        Integer orgId = 1;
        if(getCurrentOrgType().equals(5)) {
            orgId = getCurrentOrgId();
        }
        vo.setOrgId(orgId);
        ValidationUtils.validate(vo);
        // 迭代检验回答
        for (QuestionVo questionVo : vo.getEntrys()) {
            ValidationUtils.validate(questionVo);
        }
        return renderSuccess(questionnaireService.addQuestionnaire(vo));
    }

    @ApiOperation( "修改问卷" )
    @PostMapping( "/updateQuestionnaire" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult updateQuestionnaire(@RequestBody QuestionnaireVo vo) {
        Integer orgId = 1;
        if(getCurrentOrgType().equals(5)) {
            orgId = getCurrentOrgId();
        }
        vo.setOrgId(orgId);
        ValidationUtils.validate(vo);
        // 迭代检验回答
        for (QuestionVo questionVo : vo.getEntrys()) {
            ValidationUtils.validate(questionVo);
        }
        return renderSuccess(questionnaireService.updateQuestionnaire(vo));
    }

    @ApiOperation( "删除问卷" )
    @PostMapping( "/delQuestionnaire" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult delQuestionnaire(@RequestBody QuestionnaireNoVo vo) {
        Integer orgId = 1;
        if(getCurrentOrgType().equals(5)) {
            orgId = getCurrentOrgId();
        }
        vo.setOrgId(orgId);
        ValidationUtils.validate(vo);
        questionnaireService.delQuestionnaire(vo);
        return renderSuccess();
    }

    @ApiOperation( "设置问卷逻辑" )
    @PostMapping( "/setSkipOptions" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult setSkipOptions(@RequestBody SetSkipOptionsVo vo) {
        Integer orgId = 1;
        if(getCurrentOrgType().equals(5)) {
            orgId = getCurrentOrgId();
        }
        vo.setOrgId(orgId);
        ValidationUtils.validate(vo);

        // 循环检验
        for (SkipOptionsVo skipOptionsVo : vo.getEntrys()) {
            ValidationUtils.validate(skipOptionsVo);
        }

        questionnaireService.setSkipOptions(vo);
        return renderSuccess();
    }
}
