package com.awg.externalAPI.website.controller;

import com.awg.bp.eo.BusinessEo;
import com.awg.bp.externalService.IBusinessExternalService;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.*;
import com.awg.website.service.IWebsiteCaseService;
import com.awg.website.vo.OfficialBaseVo;
import com.awg.website.vo.QueryCaseVo;
import com.awg.website.vo.QueryNewsVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@ApiSupport( order = 90 )
@Api( tags = {"官网-案例接口"} )
@RestController
@RequestMapping( "/externalAPI/website/case" )
public class ExternalWebsiteCaseController extends BaseController {

    @Resource
    private IWebsiteCaseService websiteCaseService;

    @Resource
    private IBusinessExternalService businessExternalService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取案例页数据" )
    @PostMapping( "/getData" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = CaseDto.class )
    })
    public DataResult getData(@RequestBody QueryCaseVo vo) {

        CaseDataDto caseDataDto = new CaseDataDto();
        vo.setRegion("CA");

        if(vo.getOrgId()==null) {
            vo.setOrgId(1);
        }
        vo.setWatermarkOrgId(vo.getOrgId());

        BasePageResult<CaseDto> caseResult = websiteCaseService.queryCaseList(vo, vo.getOrgId(),1);

        // 循环填入完整路径
        for (CaseDto caseDto : caseResult.getData()) {
            for( CaseApprovalLetterDto caseApprovalLetterDto : caseDto.getApprovalLetterList() ){
                caseApprovalLetterDto.setApprovalLetterFileUrl(
                        orgExternalService.getFileUrlByDomain( vo.getOrgId(), caseApprovalLetterDto.getApprovalLetterFilePath())
                );
            }
        }

        caseDataDto.setCaseData(caseResult);

        return DataResult.success(caseDataDto);
    }

    @ApiOperationSupport(order = 5)
    @ApiOperation( "获取业务列表" )
    @PostMapping( "/businessList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessEo.class )
    })
    public DataResult businessTypeList(@RequestBody OfficialBaseVo vo) {
        ValidationUtils.validate(vo);
        return DataResult.success(businessExternalService.getBusinessListAll());
    }
}
