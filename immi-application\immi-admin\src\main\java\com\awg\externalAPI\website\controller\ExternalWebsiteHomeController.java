package com.awg.externalAPI.website.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.*;
import com.awg.website.enums.DisplayTargetEnum;
import com.awg.website.service.*;
import com.awg.website.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@ApiSupport( order = 70 )
@Api( tags = {"官网-首页接口"} )
@RestController
@RequestMapping( "/externalAPI/website/home" )
public class ExternalWebsiteHomeController extends BaseController {

    @Resource
    private IWebsiteBannerService websiteBannerService;

    @Resource
    private IWebsiteNewsService websiteNewsService;

    @Resource
    private IWebsiteProjectService websiteProjectService;

    @Resource
    private IOrgExternalService orgExternalService;

    @Resource
    private IWebsiteCaseService websiteCaseService;

    @Resource
    private IWebsiteAdviserService websiteAdviserService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取首页数据" )
    @PostMapping( "/getData" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = HomeDataDto.class )
    })
    public DataResult getData(@RequestBody OfficialBaseVo vo) {
        ValidationUtils.validate(vo);
        HomeDataDto homeDataDto = new HomeDataDto();

        Integer orgId = vo.getOrgId();
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(orgId);

        // banner列表
        QueryBannerVo bannerVo = new QueryBannerVo();
        bannerVo.setOrgId(orgId);
        bannerVo.setPageNo(1);
        bannerVo.setPageSize(100);
        bannerVo.setDisplayTarget(DisplayTargetEnum.HOME_PAGE.getCode());
        bannerVo.setDisplayFlag(TrueFalseEnum.TRUE.getCode());
        BasePageResult<BannerDto> bannerResult = websiteBannerService.queryBannerList(bannerVo);
        // 循环填入banner完整路径
        for (BannerDto bannerDto : bannerResult.getData()) {
            bannerDto.setImageUrl(orgExternalService.getFileUrlByDomain(orgId, bannerDto.getImagePath()));
        }
        homeDataDto.setBannerList(bannerResult.getData());

        // 新闻列表
        QueryNewsVo newsVo = new QueryNewsVo();
        newsVo.setOrgId(orgId);
        newsVo.setPageNo(1);
        newsVo.setPageSize(20);
        newsVo.setHomeDisplayFlag(TrueFalseEnum.TRUE.getCode());
        BasePageResult<NewsDto> newsResult = websiteNewsService.queryNewsList(newsVo, userLoginInfoEo, 1);
        // 循环填入完整路径
        for (NewsDto newsDto : newsResult.getData()) {
            newsDto.setCoverImageUrl(orgExternalService.getFileUrlByDomain( orgId ,newsDto.getCoverImagePath()));
        }
        homeDataDto.setNewsList(newsResult.getData());

        // 项目列表
        QueryProjectVo projectVo = new QueryProjectVo();
        projectVo.setOrgId(orgId);
        projectVo.setPageNo(1);
        projectVo.setPageSize(20);
        projectVo.setHomeDisplayFlag(TrueFalseEnum.TRUE.getCode());
        BasePageResult<ProjectDto> projectResult = websiteProjectService.queryProjectList(projectVo, userLoginInfoEo);
        // 循环填入完整路径
        for (ProjectDto projectDto : projectResult.getData()) {
            projectDto.setPreviewImageUrl(orgExternalService.getFileUrlByDomain( orgId, projectDto.getPreviewImagePath()));
        }
        homeDataDto.setProjectList(projectResult.getData());

        // 案例列表
        QueryCaseVo caseVo = new QueryCaseVo();
        caseVo.setPageNo(1);
        caseVo.setPageSize(20);
        caseVo.setRegion("CA");
        caseVo.setHomeDisplayFlag(TrueFalseEnum.TRUE.getCode());
        caseVo.setOrgId(orgId);
        if(caseVo.getOrgId()==null) {
            caseVo.setOrgId(1);
        }
        caseVo.setWatermarkOrgId(caseVo.getOrgId());
        BasePageResult<CaseDto> caseResult = websiteCaseService.queryCaseList(caseVo, caseVo.getOrgId(), 1);
        homeDataDto.setCaseList(caseResult.getData());
        // 循环填入完整路径
        for (CaseDto caseDto : caseResult.getData()) {
            for( CaseApprovalLetterDto caseApprovalLetterDto : caseDto.getApprovalLetterList() ){
                caseApprovalLetterDto.setApprovalLetterFileUrl(
                        orgExternalService.getFileUrlByDomain( orgId, caseApprovalLetterDto.getApprovalLetterFilePath())
                );
            }
        }

        // 办公室列表
        List<OfficeDto> officeDtoList = websiteAdviserService.getOfficeListAll(userLoginInfoEo);
        homeDataDto.setOfficeList(officeDtoList);

        // 团队列表
        QueryAdviserVo adviserVo = new QueryAdviserVo();
        adviserVo.setOrgId(orgId);
        adviserVo.setPageNo(1);
        adviserVo.setPageSize(20);
        adviserVo.setHomeDisplayFlag(TrueFalseEnum.TRUE.getCode());

        if(!StringUtils.isBlank(vo.getTeamOfficeNo())){
            adviserVo.setOfficeNo(vo.getTeamOfficeNo());
        }

        BasePageResult<AdviserDto> teamResult = websiteAdviserService.queryAdviserList(adviserVo, userLoginInfoEo);

        // 图片填入官网路径
        for (AdviserDto adviserDto : teamResult.getData()) {
            adviserDto.setPhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getPhotoUrl()) ));
            adviserDto.setQrcodeUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getQrcodeUrl()) ));
            adviserDto.setLicensePhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getLicensePhotoUrl()) ));
        }
        homeDataDto.setTeamList(teamResult.getData());


        return renderSuccess(homeDataDto);
    }
}
