package com.awg.common.spring;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/**
 * <p>
 * <b>SpringContextUtils</b> is spring bean 工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/6/14 11:53
 */
@Component
public class SpringContextUtils implements ApplicationContextAware {

    private static ApplicationContext context;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        SpringContextUtils.setContext(applicationContext);
    }

    public static <T> T getBean(Class<T> requiredType) {
        return SpringContextUtils.getContext().getBean(requiredType);
    }

    private static ApplicationContext getContext() {
        return context;
    }

    private static void setContext(ApplicationContext context) {
        SpringContextUtils.context = context;
    }

    public static String getProperty(String key) {
        return context.getEnvironment().getProperty(key);
    }
}