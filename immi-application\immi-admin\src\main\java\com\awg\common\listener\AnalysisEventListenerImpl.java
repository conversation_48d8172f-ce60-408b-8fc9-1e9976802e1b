package com.awg.common.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.exception.AssertUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * <b>AnalysisEventListenerImpl</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/7/15 10:27
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AnalysisEventListenerImpl<T> extends AnalysisEventListener<T> {

    private List<Object> data = new ArrayList<>();

    /**
     * <p>
     * 每解析一行内容, 都会回调该方法
     * </p>
     *
     * @param obj:             excel实体映射对象
     * @param analysisContext: 解析内容
     * @description: 每解析一行内容, 都会回调该方法
     * @author: 夜晓
     * @date: 2021/7/15
     * @return: void
     */
    @Override
    public void invoke(Object obj, AnalysisContext analysisContext) {
        // 效验是否超过10000行数据
        if (data.size() > 1000) {
            data.clear();
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "excel过大, 数据行不要超过1000行");
        }

        data.add(obj);
    }

    /**
     * @param analysisContext: 解析内容
     * @description: 解析完成后回调
     * @author: 夜晓
     * @date: 2021/7/15
     * @return: void
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        AssertUtils.isEmpty(data, BaseResponseCode.INVALID_PARAMETERS.getCode(), "无效的excel");
    }
}
