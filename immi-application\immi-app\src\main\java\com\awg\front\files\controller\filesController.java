package com.awg.front.files.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileType;
import com.awg.common.utils.PathFormat;
import com.awg.thirdparty.sdk.cos.TencentCOSUtils;
import com.awg.utils.date.DateUtils;
import com.awg.utils.random.IdWorker;
import com.awg.utils.random.RandomString;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicResponseParameters;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 * 文件 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 20 )
@Api( tags = {"文件相关接口"} )
@RestController
@RequestMapping( "/files" )
public class filesController extends BaseController {

    @Value( "${cos.key.prefix}" )
    private String cosKeyPrefix;

    @Value( "${cos.key.fileServiceUrlBase}" )
    private String fileUrlBase;

    @ApiOperationSupport( order = 10 )
    @ApiOperation( value = "附件上传【type是类型，0=普通，1头像】" )
    @PostMapping( "/upload" )
    @DynamicResponseParameters(
            name = "mapUploadToImages",
            properties = {
                    @DynamicParameter( name = "url", value = "文件远程访问路径" ),
                    @DynamicParameter ( name = "cosKey", value = "文件远程相对路径" ),
                    @DynamicParameter ( name = "oldFileName", value = "文件名字[旧]" ),
                    @DynamicParameter ( name = "newFileName", value = "文件名字[新]" ),
                    @DynamicParameter ( name = "maxSize", value = "文件大小[单位：kb]" )
            } )
    public DataResult uploadToBrand(
            @RequestParam( "uploadFile" ) MultipartFile uploadFile,
            @RequestParam( value = "type", defaultValue = "0") Integer type
    ) {

        if(type==null) {
            type = 0;
        }

        // 文件大小
        long fileSize = uploadFile.getSize();

        String[] suffixList = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tif", ".tiff", ".svg"};
        String suffixTest = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
        if (Arrays.asList(suffixList).contains(suffixTest)) {
            // 图片类型
            AssertUtils.isTrue(fileSize>1048576L*10L, "图片大小不能超过10M");
        }
        else {
            // 非图片
            AssertUtils.isTrue(fileSize>1048576L*100L, "文件大小不能超过100M");
        }

        // 必须登录
        Integer loginUid = super.getCurrentMemberId();
        AssertUtils.isTrue(loginUid==null || loginUid<=0, "请登录后再操作");

        String newFileName = "";
        String suffix = "";
        try {
            String oldFileName = uploadFile.getOriginalFilename();
            suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
            oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());

            // 文件名字[新]
            newFileName =  oldFileName;

        } catch (Exception e) {
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
        }

        // 随机加密数
        String randomKey = RandomString.getRandomCode(12) + RandomString.getRandomCode(12) + IdWorker.getRandomNo();

        //cos存储同名字
        String bucketName = "file-immi";

        // 保存到数据库的路径
        String dataPath = "/app/client/files" + "/member/" + super.getCurrentMemberId() + "/files/"
                + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;

        if(type.equals(1)) {
            dataPath = "/app/client/files" + "/member/" + super.getCurrentMemberId() +  "/avatar/"
                    + DateUtils.getCustomDateStr(0) + "/" + randomKey + "/" + newFileName;
        }

        // cos保存路径
        String savePath = cosKeyPrefix + dataPath;

        // 完整的路径
        String fileUrl = fileUrlBase + dataPath;

        // 上传一份到备份存储桶
        this.uploadToKodo(uploadFile, newFileName, savePath, "immi-private-657593", dataPath, fileUrl);

        // 上传到使用桶
        Map<String, Object> result = this.uploadToKodo(uploadFile, newFileName, savePath, bucketName, dataPath, fileUrl);

        // 如果是tiff格式的文件，需要转换成png格式
        if (".tiff".equals(suffix)) {
            String rotateSavePath =  dataPath +".png";

            // 转换图片格式
            TencentCOSUtils.tiff2png(
                    bucketName,
                    cosKeyPrefix + result.get("dataPath").toString(),
                    cosKeyPrefix + rotateSavePath
            );
            result.put("dataPath", rotateSavePath);
            result.put("fileUrl", fileUrlBase + rotateSavePath);
            result.put("newFileName", newFileName+".png");
        }

        return renderSuccess(result);

    }

    private Map<String, Object> uploadToKodo(MultipartFile uploadFile, String newFileName, String savePath, String bucketName,
                                             String dataPath, String fileUrl) {
        Map<String, Object> result = new LinkedHashMap<>(18);
        // 文件名字
        String oldFileName = null;
        // 文件大小[单位：kb 1KB=1024字节 1MG=1024KB ]
        long maxSize = 0L;

        // 文件后缀
        String suffix = null;
        // 文件上传到cos后台访问域名
        String url = null;
        InputStream inputStream = null;
        try {
            if (uploadFile == null) {
                // 附件为空
                throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "附件不允许为空!");
            }
            maxSize = uploadFile.getSize();

            oldFileName = uploadFile.getOriginalFilename();
            suffix = FileType.getSuffixByFilename(uploadFile.getOriginalFilename());
            oldFileName = oldFileName.substring(0, oldFileName.length() - suffix.length());
            savePath = savePath + suffix;
            savePath = PathFormat.parse(savePath, oldFileName);
            dataPath = dataPath + suffix;
            dataPath = PathFormat.parse(dataPath, oldFileName);
            fileUrl = fileUrl + suffix;
            fileUrl = PathFormat.parse(fileUrl, oldFileName);
            inputStream = uploadFile.getInputStream();
            // 将文件上传到腾讯云服务器,通过文件流的方式
            url = TencentCOSUtils.uploadFile(bucketName, savePath, inputStream);
            inputStream.close();
        } catch (Exception e) {
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
        } finally {
            // 防止内存溢出，关闭文件流
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传文件异常，请联系管理员!");
                }
            }
        }
        // result.put("url", url);
        result.put("oldFileName", oldFileName + suffix);
        result.put("newFileName", newFileName + suffix);
        result.put("maxSize", maxSize);
//        result.put("cosKey", savePath);   // coskey不用返回
        result.put("dataPath", dataPath);
        result.put("fileUrl", fileUrl);
        return result;
    }
}
