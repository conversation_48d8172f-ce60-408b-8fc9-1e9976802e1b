package com.awg.common.exception.handler;

import com.awg.common.base.exception.*;
import com.awg.common.base.result.DataResult;
import com.awg.common.constant.UserTokenConstant;
import com.awg.common.exception.ExceptionUtils;
import com.awg.common.redis.RedisUtils;
//import com.awg.log.service.IExceptionLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.util.Enumeration;

/**
 * RestExceptionHandler
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2020年3月18日
 */
@RestControllerAdvice
@Slf4j
public class RestExceptionHandler {

    @Resource
    private RedisUtils redisUtils;

//    @Resource
//    private IExceptionLogService exceptionLogService;

    /**
     * 系统繁忙，请稍候再试"
     */
    @ExceptionHandler ( Exception.class )
    public DataResult handleException(Exception e, HttpServletRequest request) {
        log.error("Exception,exception:{}", e, e);
        ExceptionLogVo logVo = ExceptionUtils.inserErrorLog(e, ExceptionTypeEnum.ADMIN.getCode());

        //定位url
        if (redisUtils.exists(UserTokenConstant.DEBUG_KYE)) {
            //获取请求url和参数
            String url = request.getRequestURL().toString();
            //请求参数
            Enumeration enu = request.getParameterNames();
            StringBuilder sb = new StringBuilder();
            while (enu.hasMoreElements()) {
                String paraName = (String) enu.nextElement();
                sb.append(paraName).append("=").append(request.getParameter(paraName));
            }
            log.error("请求url：{} ", url);
            log.error("请求参数：{}", sb.toString());
            logVo.setContent(logVo.getContent() + "请求url:" + url + "请求参数：" + sb.toString());
        }

//        redisUtils.lPush(ExceptionConstant.LOG_REDIS_KEY, logVo);
        //将错误日志保存到db
        // exceptionLogService.saveErrorLog(logVo);
        //开发环境，将详细异常暴露给前端，方便调试
        DataResult result = null;
        if (redisUtils.exists(ExceptionConstant.ERRO_SHOW_FLAG)) {
            result = DataResult.getResult(BaseResponseCode.CODE_ERROR.getCode(), e.getMessage());
            //常见异常信息处理
            ExceptionUtils.exceptionHandler(e, result);
            result.setLogId(logVo.getId());
            return result;
        } else {
            result = DataResult.getResult(BaseResponseCode.CODE_ERROR);
            result.setLogId(logVo.getId());
            return result;
        }
    }

    /**
     * 自定义全局异常处理
     */
    @ExceptionHandler ( value = BusinessException.class )
    public DataResult businessExceptionHandler(BusinessException e) {
        log.error("BusinessException,exception:{}", e, e);
        return new DataResult(e.getMessageCode(), e.getDetailMessage());
    }


    // 捕获 Spring 方法参数校验异常（@Validated + @Min 等）
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public DataResult handleConstraintViolationException(ConstraintViolationException e) {
        String errorMsg = e.getConstraintViolations().iterator().next().getMessage();
        return new DataResult(BaseResponseCode.INVALID_PARAMETERS.getCode(), errorMsg);
    }

    /**
     * 处理 @Valid 校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public DataResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String errorMsg = e.getBindingResult().getFieldErrors().get(0).getDefaultMessage();
        return new DataResult(BaseResponseCode.INVALID_PARAMETERS.getCode(), errorMsg);
    }
}
