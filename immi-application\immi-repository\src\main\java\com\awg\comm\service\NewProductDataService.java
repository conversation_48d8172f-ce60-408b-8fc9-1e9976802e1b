package com.awg.comm.service;

import com.awg.comm.entity.NewProductData;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 商品数据表 服务类 - 新版本
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface NewProductDataService extends IService<NewProductData> {



    /**
     * 根据商品编号和版本号查询商品数据
     * @param productNo 商品编号
     * @param productVid 商品版本号
     * @return 商品数据
     */
    NewProductData getByProductNoAndVid(String productNo, Integer productVid);


}
