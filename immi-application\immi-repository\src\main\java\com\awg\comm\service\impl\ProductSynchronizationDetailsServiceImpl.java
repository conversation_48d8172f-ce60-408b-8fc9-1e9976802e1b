package com.awg.comm.service.impl;

import com.awg.comm.entity.ProductSynchronizationDetails;
import com.awg.comm.mapper.ProductSynchronizationDetailsMapper;
import com.awg.comm.service.IProductSynchronizationDetailsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 商品同步细节表 服务实现类
 */
@Service
public class ProductSynchronizationDetailsServiceImpl extends ServiceImpl<ProductSynchronizationDetailsMapper, ProductSynchronizationDetails> implements IProductSynchronizationDetailsService {
    // 可根据业务需求添加自定义方法实现
} 