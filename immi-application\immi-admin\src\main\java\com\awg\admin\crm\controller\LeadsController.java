package com.awg.admin.crm.controller;

import com.awg.comm.vo.DisplayChangeVo;
import com.awg.comm.vo.DistrictSortVo;
import com.awg.comm.vo.ProductKeywordVo;
import com.awg.comm.vo.QueryDistrictVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.dto.*;
import com.awg.crm.entity.*;
import com.awg.crm.externalService.ILeadsCustomSortExternalService;
import com.awg.crm.externalService.ILeadsExternalService;
import com.awg.crm.mapper.LeadsAreaMapper;
import com.awg.crm.mapper.LeadsCrossMapper;
import com.awg.crm.service.ILeadsService;
import com.awg.crm.service.ILeadsStatusConfigService;
import com.awg.crm.service.IPersonnelAssignmentService;
import com.awg.crm.vo.*;
import com.awg.ipaddr.externalService.IIpaddrLocationExternalService;
import com.awg.qs.externalService.IQsExternalService;
import com.awg.system.eo.UserMsgCursorEo;
import com.awg.system.externalService.IUserMsgViewedExternalService;
import com.awg.utils.date.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 37 )
@Api( tags = {"潜客相关接口"} )
@RestController
@RequestMapping( "/crm/leads" )
public class LeadsController extends BaseController {

    @Resource
    private ILeadsService leadsService;

    @Resource
    private IPersonnelAssignmentService personnelAssignmentService;

    @Resource
    private IUserMsgViewedExternalService userMsgViewedExternalService;

    @Resource
    private IIpaddrLocationExternalService iIpaddrLocationExternalService;

    @Resource
    private ILeadsExternalService leadsExternalService;

    @Resource
    private LeadsAreaMapper leadsAreaMapper;

    @Resource
    private ILeadsStatusConfigService leadsStatusConfigService;

    @Resource
    private LeadsCrossMapper leadsCrossMapper;
    @Resource
    private ILeadsCustomSortExternalService leadsCustomSortExternalService;

    @ApiOperation( "获取潜客列表" )
    @PostMapping( "/getLeadsList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LeadsDto.class )
    })
    public DataResult getLeadsList(@RequestBody QueryLeadsVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<LeadsDto> result = leadsService.getLeadsList(vo, getCurrentOrgId(), getUserInfoId(),
                getCurrentRoleType(), getCurrentOrgType(), getUid());

        // 通过该接口调用时，刷新消息已读【简单处理，没有关键词时且为第一页时便更新】
        if( StringUtils.isBlank(vo.getKeyword()) && !vo.getPageNo().equals(1) ) {
            UserMsgCursorEo userMsgCursorEo = new UserMsgCursorEo();
            userMsgCursorEo.setLeadsCursor( (int)DateUtils.getCurrentTimestamp() );
            userMsgCursorEo.setKbsCursor(null);
            userMsgViewedExternalService.updateUserMsgCursor(getUserLoginInfoEo().getUserInfoId(), userMsgCursorEo);
        }

        return renderSuccess(result);
    }

    @ApiOperation( "公司潜客id转潜客id" )
    @PostMapping( "/orgLeadsIdToLeadsId/{orgLeadsId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LeadsIdDto.class )
    })
    public DataResult orgLeadsIdToLeadsId(@PathVariable( value = "orgLeadsId" ) Integer orgLeadsId){
        return renderSuccess(leadsCrossMapper.orgLeadsIdToLeadsId(getCurrentOrgId(), getCurrentOrgType(), orgLeadsId));
    }

    @ApiOperation( "获取潜客信息" )
    @PostMapping( "/getLeadsInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Leads.class )
    })
    public DataResult getLeadsInfo(@RequestBody GetLeadsInfoVo vo){
        ValidationUtils.validate(vo);
        LeadsDto result = leadsService.getLeadsInfo(vo.getLeadsId(), getUserLoginInfoEo());
        return renderSuccess(result);
    }

    @ApiOperation( "编辑潜客信息" )
    @PostMapping( "/editLeadsInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult editLeadsInfo(@RequestBody EditLeadsVo vo){
        ValidationUtils.validate(vo);
        leadsService.editLeadsInfo(vo, getCurrentOrgId(), getCurrentOrgType(), getUserInfoId());
        return renderSuccess();
    }

    @ApiOperation( "潜客排序" )
    @PostMapping( "/sortLeads" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortLeads(@RequestBody LeadsSortVo vo){
        ValidationUtils.validate(vo);
        leadsCustomSortExternalService.sortLeadsCustomSort(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "新增潜客" )
    @PostMapping( "/addLeadsInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AddLeadsDto.class )
    })
    public DataResult addLeadsInfo(@RequestBody AddLeadsVo vo){
        ValidationUtils.validate(vo);
        return renderSuccess(leadsService.addLeadsInfo(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "检查重复潜客信息" )
    @PostMapping( "/checkDuplicateLeadsInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AddLeadsDto.class )
    })
    public DataResult checkDuplicateLeadsInfo(@RequestBody CheckDuplicateVo vo){
        return renderSuccess(leadsService.checkDuplicateLeadsInfo(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "删除潜客" )
    @PostMapping( "/delLeads/{leadsId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addLeadsInfo(@PathVariable( value = "leadsId" ) Integer leadsId){
        AssertUtils.isTrue(leadsId <= 0, "id不合法");
        return renderSuccess(leadsService.delLeads(leadsId, getUserInfoId(), getCurrentOrgId(), getCurrentOrgType()));
    }

    @ApiOperationSupport(order = 2)
    @ApiOperation( "查询字段-角色-关联" )
    @PostMapping( "/getFieldRoleRelation/{fieldCode}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = FieldRoleRelationDto.class )
    })
    public DataResult getFieldRoleRelation(@PathVariable( value = "fieldCode" ) Integer fieldCode) {
        return renderSuccess(leadsService.getFieldRoleRelation(fieldCode, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 3)
    @ApiOperation( "更新字段-角色-关联" )
    @PostMapping( "/updateFieldRoleRelation" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateFieldRoleRelation(@RequestBody FieldRoleRelationVo vos) {
        ValidationUtils.validate(vos);
        leadsService.updateFieldRoleRelation(vos, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 6)
    @ApiOperation( "获取潜客标签列表" )
    @PostMapping( "/tagListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LeadsTagDto.class )
    })
    public DataResult tagListAll() {
        return renderSuccess(leadsService.getLeadsTagListAll(getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 7)
    @ApiOperation( "更新潜客标签列表" )
    @PostMapping( "/updateTagList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateTagList(@RequestBody List<AddLeadsTagVo> vos) {

        // 迭代校验列表
        vos.forEach( item -> {
            ValidationUtils.validate(item);
        });

        leadsService.updateLeadsTagList(vos, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 8)
    @ApiOperation( "潜客标签关系保存【打标签】" )
    @PostMapping( "/leadsTagSave" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult leadsTagSave(@RequestBody LeadsTagSaveVo vo) {
        ValidationUtils.validate(vo);
        leadsService.leadsTagSave(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 9)
    @ApiOperation( "潜客关联业务保存" )
    @PostMapping( "/leadsBusinessSave" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult leadsBusinessSave(@RequestBody LeadsBusinessSaveVo vo) {
        ValidationUtils.validate(vo);
        leadsService.leadsBusinessSave(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 310)
    @ApiOperation( "获取潜客来源列表" )
    @PostMapping( "/sourceListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LeadsSource.class )
    })
    public DataResult sourceListAll(@RequestBody QueryDistrictVo vo) {
        if(getCurrentOrgType().equals(5)) {
            vo.setOrgId(getCurrentOrgId());
        }
        else {
            vo.setOrgId(1);
        }

        ValidationUtils.validate(vo);
        return renderSuccess(leadsService.getSourceAll(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 320)
    @ApiOperation( "添加来源" )
    @PostMapping( "/addSource" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addSource(@RequestBody ProductKeywordVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(leadsService.addSource(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 330)
    @ApiOperation( "更新来源" )
    @PostMapping( "/updateSource" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateSource(@RequestBody ProductKeywordVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(leadsService.updateSource(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 340)
    @ApiOperation( "删除来源" )
    @PostMapping( "/deleteSource/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult deleteSource(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id==null || id<=0, "请先传入id");
        leadsService.deleteSource(id, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 355)
    @ApiOperation( "排序来源" )
    @PostMapping( "/sortSource" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortSource(@RequestBody DistrictSortVo vo) {
        vo.setPid(0);
        ValidationUtils.validate(vo);
        leadsService.sortSource(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 365)
    @ApiOperation( "显示/隐藏来源" )
    @PostMapping( "/displayChangeSource" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult displayChangeSource(@RequestBody DisplayChangeVo vo) {
        ValidationUtils.validate(vo);
        leadsService.displayChangeSource(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 500)
    @ApiOperation( "查询所有状态配置" )
    @PostMapping( "/getLeadsStatusConfigAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LeadsSource.class )
    })
    public DataResult getLeadsStatusConfigAll() {
        return renderSuccess(leadsStatusConfigService.getLeadsStatusConfigAll(getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 510)
    @ApiOperation( "状态排序" )
    @PostMapping( "/sortStatus" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortStatus(@RequestBody DistrictSortVo vo) {
        ValidationUtils.validate(vo);
        leadsStatusConfigService.sortStatus(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 520)
    @ApiOperation( "状态开关" )
    @PostMapping( "/statusOpenChange" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult statusOpenChange(@RequestBody OpenChangeVo vo) {
        ValidationUtils.validate(vo);
        leadsStatusConfigService.openChange(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "查询潜客日志（不分页）" )
    @PostMapping( "/getLeadsLogList/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LeadsLogDto.class )
    })
    public DataResult getLeadsLogList(@PathVariable( value = "id" ) Integer id){
        AssertUtils.isTrue(id <= 0, "id不合法");
        return renderSuccess(leadsService.getLeadsLogList(id, getCurrentOrgId(), getCurrentOrgType(),getUserInfoId()));
    }

    @ApiOperation( "提交留言" )
    @PostMapping( "/addLeadsMessage" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addLeadsMessage(@RequestBody AddLeadsMessageVo vo){
        ValidationUtils.validate(vo);
        return renderSuccess(leadsService.addLeadsMessage(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "删除留言" )
    @PostMapping( "/delLeadsMessage/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delLeadsMessage(@PathVariable( value = "id" ) Integer id){
        AssertUtils.isTrue(id <= 0, "id不合法");
        return renderSuccess(leadsService.delLeadsMessage(id, getUserLoginInfoEo()));
    }

    @ApiOperation( "添加附件" )
    @PostMapping( "/addAttachment" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addAttachment(@RequestBody AddAttachmentVo vo){
        ValidationUtils.validate(vo);
        return renderSuccess(leadsService.addAttachment(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "删除附件" )
    @PostMapping( "/delAttachment/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delAttachment(@PathVariable( value = "id" ) Integer id){
        AssertUtils.isTrue(id <= 0, "id不合法");
        return renderSuccess(leadsService.delAttachment(id, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "查询潜客附件列表" )
    @PostMapping( "/getLeadsAttachment/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Attachment.class )
    })
    public DataResult getLeadsAttachment(@PathVariable( value = "id" ) Integer id){
        AssertUtils.isTrue(id <= 0, "id不合法");
        return renderSuccess(leadsService.getLeadsAttachment(id, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "切换标星状态" )
    @PostMapping( "/switchStarFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchStarFlagVo vo){
        ValidationUtils.validate(vo);
        leadsService.switchStarFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "设置客户意向等级" )
    @PostMapping( "/setIntentionLevel" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult setIntentionLevel(@RequestBody IntentionLevelVo vo){
        ValidationUtils.validate(vo);
        leadsService.setIntentionLevel(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "分配人员" )
    @PostMapping( "/assignPersonnel" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult assignPersonnel(@RequestBody AssignPersonnelVo vo){
        ValidationUtils.validate(vo);
        personnelAssignmentService.assignPersonnel(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "移除分配的人员" )
    @PostMapping( "/removePersonnel" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult removePersonnel(@RequestBody RemovePersonnelVo vo){
        ValidationUtils.validate(vo);
        personnelAssignmentService.removePersonnel(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "更改渠道" )
    @PostMapping( "/channelChange" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult channelChange(@RequestBody ChannelChangeVo vo){
        ValidationUtils.validate(vo);
        leadsService.channelChange(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "更新潜客所属地区" )
    @PostMapping( "/updateLeadsArea" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateLeadsArea(@RequestBody UpdateLeadsAreaVo vo){
        ValidationUtils.validate(vo);
        return renderSuccess(leadsExternalService.updateLeadsArea(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "填充地区id（临时接口）" )
    @PostMapping( "/fillAreaId/{password}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult fillAreaId(@PathVariable( value = "password" ) String password){

        if(password.equals("daoidjeiodejduoeaheaiod9eu38ey3s")){
            // 获取全部leadsArea信息
            List<LeadsArea> leadsAreaList = leadsAreaMapper.selectList(Wrappers.<LeadsArea>lambdaQuery()
                    .eq(LeadsArea::getIsDelete, TrueFalseEnum.FALSE.getCode())
            );

            // 循环迭代填入
            for( LeadsArea leadsArea : leadsAreaList ){
                String AreaId = iIpaddrLocationExternalService.getAreaIdByCode(leadsArea.getRegion(), leadsArea.getDistrict());
                leadsArea.setAreaId(AreaId);
                leadsAreaMapper.updateById(leadsArea);
            }

        }

        return renderSuccess();
    }

}