/*
 * JedisUtils.java 2018年9月27日
 */
package com.awg.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * <b>JedisUtils</b> is 封装redis操作类
 * </p>
 *
 * <AUTHOR>
 * @since 2018年9月27日上午11:28:29
 */
@Slf4j
@Service
public class RedisUtils {

    @Resource
    RedisTemplate<String, Object> redisTemplate;


    /* -------------------key相关操作--------------------- */

    /**
     * 批量删除对应的value
     *
     * @param: keys
     */
    public void remove(final String... keys) {
        for (String key : keys) {
            remove(key);
        }
    }

    /**
     * 批量删除对应的value
     *
     * @param: keys
     */
    public void remove(Collection<?> keys) {
        for (Object key : keys) {
            if (exists(String.valueOf(key))) {
                remove(String.valueOf(key));
            }
        }
    }

    /**
     * 批量删除key
     *
     * @param: pattern
     */
    public void removePattern(final String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        if (!CollectionUtils.isEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 删除对应的value
     *
     * @param: key
     */
    public void remove(final String key) {
        if (exists(key)) {
            redisTemplate.delete(key);
        }
    }

    /**
     * 查找匹配的key
     *
     * @param: pattern
     * @return: key
     */
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 判断缓存中是否有对应的value
     *
     * @param: key
     * @return: boolean
     */
    public boolean exists(final String key) {
        Boolean isExist = redisTemplate.hasKey(key);
        return isExist != null && isExist;
    }

    /**
     * 读取缓存
     *
     * @param: key
     * @return:
     */
    public Object get(final String key) {
        Object result = null;
        ValueOperations<String, Object> operations = redisTemplate.opsForValue();
        result = operations.get(key);
        return result;
    }


    /* -------------------string相关操作--------------------- */

    /**
     * 写入缓存
     *
     * @param: key
     * @param: value
     * @return: boolean
     */
    public boolean set(final String key, Object value) {
        return set(key, value, null, null);
    }

    /**
     * 写入缓存
     *
     * @param: key
     * @param: value
     * @return: boolean
     */
    public boolean set(final String key, Object value, Long expireTime, TimeUnit timeUnit) {
        boolean result = false;
        try {
            ValueOperations<String, Object> operations = redisTemplate.opsForValue();
            operations.set(key, value);
            //设置过期时间
            if (expireTime != null && timeUnit != null) {
                redisTemplate.expire(key, expireTime, timeUnit);
            }
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /* -------------------hash相关操作------------------------- */

    /**
     * 哈希 添加
     *
     * @param: key
     * @param: hashKey
     * @param: value
     */
    public void hmSet(String key, Object hashKey, Object value) {
        HashOperations<String, Object, Object> hash = redisTemplate.opsForHash();
        hash.put(key, hashKey, value);
    }

    public void hPutAll(String key, Map<?, Object> maps) {
        Map<String, Object> newMap = new HashMap<>(16);
        maps.forEach((k, v) -> newMap.put(String.valueOf(k), v));
        hPutAll(key, newMap, null, null);
    }

    public void hPutAll(String key, Map<?, Object> maps, Long expires) {
        Map<String, Object> newMap = new HashMap<>(16);
        maps.forEach((k, v) -> newMap.put(String.valueOf(k), v));
        hPutAll(key, newMap, expires, TimeUnit.SECONDS);
    }

    public void hPutAll(String key, Map<String, Object> maps, Long expires, TimeUnit timeUnit) {
        redisTemplate.opsForHash().putAll(key, maps);
        if (expires != null && timeUnit != null) {
            //设置过期时间
            redisTemplate.expire(key, expires, timeUnit);
        } else {
            //如果不传入时间，则永久存在
            redisTemplate.persist(key);
        }
    }

    /**
     * 获取所有给定字段的值
     *
     * @param: key
     * @return: value
     */
    public Map<Object, Object> hGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    public Map<String, Object> hGetAll(Object key) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(String.valueOf(key));

        Map<String, Object> result = new HashMap<>(16);
        entries.forEach((k, v) -> result.put(String.valueOf(k), v));
        return result;
    }

    /**
     * 哈希获取数据
     *
     * @param: key
     * @param: hashKey
     * @return:
     */
    public Object hmGet(String key, Object hashKey) {
        HashOperations<String, Object, Object> hash = redisTemplate.opsForHash();
        return hash.get(key, hashKey);
    }

    /* ------------------------list相关操作---------------------------- */

    /**
     * 列表添加
     *
     * @param: k
     * @param: v
     */
    public void lPush(String k, Object v) {
        ListOperations<String, Object> list = redisTemplate.opsForList();
        list.leftPush(k, v);
    }

    /**
     * 列表获取
     *
     * @param: k
     * @param: l
     * @param: l1
     * @return:
     */
    public List<Object> lRange(String k, long l, long l1) {
        ListOperations<String, Object> list = redisTemplate.opsForList();
        return list.range(k, l, l1);
    }

    /**
     * 移出并获取列表的第一个元素
     *
     * @return 删除的元素
     * @param: key
     */
    public Object lLeftPop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }

    /**
     * 删除集合中值等于value得元素
     *
     * @param: key
     * @param: index index=0, 删除所有值等于value的元素; index>0, 从头部开始删除第一个值等于value的元素; index<0, 从尾部开始删除第一个值等于value的元素;
     * @param: value
     * @return: 索引值
     */
    public Long lRemove(String key, long index, String value) {
        return redisTemplate.opsForList().remove(key, index, value);
    }

    /**
     * 裁剪list (保留指定索引数据)
     *
     * @param: key
     * @param: start
     * @param: end
     */
    public void lTrim(String key, long start, long end) {
        redisTemplate.opsForList().trim(key, start, end);
    }

    /**
     * 获取列表长度
     *
     * @param: key
     * @return: 长度
     */
    public Long lLen(String key) {
        return redisTemplate.opsForList().size(key);
    }


    /* --------------------set相关操作-------------------------- */

    /**
     * set添加元素
     *
     * @param: key
     * @param: values
     * @return:
     */
    public Long sAdd(String key, String... values) {
        return redisTemplate.opsForSet().add(key, values);
    }

    /**
     * set移除元素
     *
     * @param: key
     * @param: values
     * @return:
     */
    public Long sRemove(String key, Object... values) {
        return redisTemplate.opsForSet().remove(key, values);
    }

    /**
     * 移除并返回集合的一个随机元素
     *
     * @param: key
     * @return:
     */
    public String sPop(String key) {
        return redisTemplate.opsForSet().pop(key).toString();
    }

    /**
     * 获取集合的大小
     *
     * @param: key
     * @return:
     */
    public Long sSize(String key) {
        return redisTemplate.opsForSet().size(key);
    }


    /* ------------------zSet相关操作-------------------------------- */

    /**
     * 有序集合添加
     *
     * @param: key
     * @param: value
     * @param: score
     */
    public void zAdd(String key, Object value, double score) {
        ZSetOperations<String, Object> zSet = redisTemplate.opsForZSet();
        zSet.add(key, value, score);
    }

    /**
     * 有序集合获取
     *
     * @param: key
     * @param: beginScore
     * @param: endScore
     * @return:
     */
    public Set<Object> rangeByScore(String key, double beginScore, double endScore) {
        ZSetOperations<String, Object> zSet = redisTemplate.opsForZSet();
        return zSet.rangeByScore(key, beginScore, endScore);
    }

    /**
     * @param key:
     * @description:返回当前key所对应的剩余过期时间
     * @author: yangqiang
     * @date: 2022/5/19 12:05
     * @return: void
     **/
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }
    
}
