package com.awg.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b> WalletActionCategoryEnum </b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-05
 */

@Getter
@AllArgsConstructor
public enum WalletActionCodeEnum {

    // 操作代码（501=后台手动增减钱包，601=订单退还，301订单返佣金额到账，701=注册赠送）
    MANUAL_OPERATION(501, "后台手动增减钱包"),
    BUY_PRODUCT(201, "购买商品使用钱包"),
    ORDER_COMMISSION(401, "订单返佣"),
    ORDER_CASHBACK(402, "订单返现"),
    ORDER_COMMISSION_VOID(405, "订单返佣作废"),
    ORDER_CASHBACK_VOID(406, "订单返现作废"),
    ORDER_REFUND(601, "订单退还"),
    ORDER_COMMISSION_ARRIVAL(301, "返佣金额到账"),
    ORDER_CASHBACK_ARRIVAL(302, "返现金额到账"),
    REGISTER_GIFT(701, "注册赠送")
    ;

    private final Integer code;
    private final String label;

    public static WalletActionCodeEnum parse(Integer code) {
        return Arrays.stream( WalletActionCodeEnum.values() )
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    public static WalletActionCodeEnum parse(String label) {
        return Arrays.stream(WalletActionCodeEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }
}
