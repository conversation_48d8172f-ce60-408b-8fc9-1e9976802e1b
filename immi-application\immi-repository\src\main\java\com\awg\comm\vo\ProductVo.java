package com.awg.comm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * <b>ProductVo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-12
 */

@Data
@ApiModel(value = "商品参数")
public class ProductVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号，编辑时必填")
    private String productNo;

    @ApiModelProperty(value = "来源商品编号")
    private String sourceNo;

    @ApiModelProperty(value = "商品名称")
    @Size(max = 150, message = "商品名称长度不能超过150个字符")
    @NotBlank(message = "商品名称不能为空")
    private String name;

    @ApiModelProperty(value = "商品描述")
    @Size(max = 2000, message = "商品描述长度不能超过2000个字符")
    @NotBlank(message = "商品描述不能为空")
    private String description;

    @ApiModelProperty(value = "关联项目(业务)")
    private String relatedBusinessNo;

    @ApiModelProperty(value = "关联子级项目(业务)")
    private String relatedChildBusinessNo;

    @ApiModelProperty(value = "注意事项")
    private String notes;

    @ApiModelProperty(value = "费用描述")
    private String feeDescription;

    @ApiModelProperty(value = "优惠说明")
    private String discountDescription;

    @ApiModelProperty(value = "服务条款")
    private String termsOfService;

    @ApiModelProperty(value = "价格，0表示免费")
    private BigDecimal price;

    @ApiModelProperty(value = "平台交付价格，0表示免费")
    private BigDecimal platformDeliveryPrice;

    @ApiModelProperty(value = "必须材料列表")
    @NotNull(message = "必须材料列表不能为空")
    private List<MaterialVo> requiredMaterialList;

    @ApiModelProperty(value = "可选材料列表")
    @NotNull(message = "可选材料列表不能为空")
    private List<MaterialVo> optionalMaterialList;

    @ApiModelProperty(value = "分组材料列表（N选M材料）")
    @NotNull(message = "分组材料列表不能为空")
    private List<MaterialGroupVo> materialGroupList;

    @ApiModelProperty(value = "文案人员列表(user_info_id)")
    @NotNull(message = "文案人员列表不能为空")
    private List<Integer> copywriterList;

    @ApiModelProperty(value = "商品素材")
    @NotNull(message = "商品素材列表不能为空")
    private List<ProductBannerVo> productBannerList;

    @ApiModelProperty(value = "商品pdf")
    @NotNull(message = "商品pdf列表不能为空")
    private List<ProductPdfVo> pdfList;

    @ApiModelProperty(value = "关键词id列表，是使用,分隔的字符串")
    @Size(max = 2000, message = "关键词id列表长度不能超过2000个字符")
    private String keywordIds;

    @ApiModelProperty(value = "销售咨询类型（0=购买，1=咨询，2=无）")
    private Integer salesConsultationType;

    @ApiModelProperty(value = "购买按钮文案")
    private String purchaseButtonText;

    @ApiModelProperty(value = "促销按钮文案")
    private String promotionButtonText;

    @ApiModelProperty(value = "咨询按钮文案")
    private String consultationButtonText;

    @ApiModelProperty(value = "咨询顾问微信")
    private String consultantWechat;

    @ApiModelProperty(value = "咨询顾问二维码")
    private String consultantQrcode;

    @ApiModelProperty(value = "学校logo")
    private String schoolLogo;

    @ApiModelProperty(value = "商品封面")
    private String cover;

    @ApiModelProperty(value = "二级分类id")
    private Integer secondaryCategory;

//    @ApiModelProperty(value = "B端佣金设置")
//    @NotNull(message = "B端佣金设置不能为空")
//    private CommissionVo bCommission;
//
//    @ApiModelProperty(value = "C端佣金设置")
//    @NotNull(message = "C端佣金设置不能为空")
//    private CommissionVo cCommission;

    @ApiModelProperty(value = "可用状态，0下架，1上架")
    @Min(value = 0, message = "可用性状态不合法")
    @Max(value = 1, message = "可用性状态不合法")
    private Integer availabilityStatus;

    @ApiModelProperty(value = "库存开关标志，0关闭，1开启")
    @Min(value = 0, message = "库存开关标志不合法")
    @Max(value = 1, message = "库存开关标志不合法")
    @NotNull(message = "库存开关不能为空")
    private Integer stockOpenFlag;

    @ApiModelProperty(value = "促销开关标志，0关闭，1开启")
    @Min(value = 0, message = "促销开关标志不合法")
    @Max(value = 1, message = "促销开关标志不合法")
    @NotNull(message = "促销开关不能为空")
    private Integer promoOpenFlag;

    @ApiModelProperty(value = "库存（可重置库存）")
    @Min(value = 0, message = "库存不合法")
    private Integer stock;

    @ApiModelProperty(value = "促销库存")
    private String promoStock;

    @ApiModelProperty(value = "是否改变库存，布尔值")
    @NotNull(message = "是否改变库存不能为空")
    private Boolean changeStock;

    @ApiModelProperty(value = "是否改变促销库存，布尔值")
    @NotNull(message = "是否改变促销库存不能为空")
    private Boolean changePromoStock;

    @ApiModelProperty(value = "促销价格")
    private BigDecimal promoPrice;

    @ApiModelProperty(value = "第二件优惠价")
    private BigDecimal secondDiscountPrice;

    @ApiModelProperty(value = "第三件优惠价")
    private BigDecimal thirdDiscountPrice;

    @ApiModelProperty(value = "分类（1=签证类，2=留学类，3=本地服务）")
    @Min(value = 1, message = "分类不合法")
    @Max(value = 3, message = "分类不合法")
    @NotNull(message = "分类不能为空")
    private Integer category;

    @ApiModelProperty(value = "教育阶段（1=中小学，2=college，3=本科，4=研究生）")
    @Min(value = 1, message = "教育阶段不合法")
    @Max(value = 4, message = "教育阶段不合法")
    private Integer educationalStage;

    @ApiModelProperty(value = "地区id")
    private Integer districtId;

    @ApiModelProperty(value = "优惠券列表")
    @NotNull(message = "优惠券列表不能为空")
    private List<CouponTemplateVo> couponList;

    @ApiModelProperty(value = "多个购买优惠开关标志，0关闭，1开启")
    @Min(value = 0, message = "多个购买优惠开关标志不合法")
    @Max(value = 1, message = "多个购买优惠开关标志不合法")
    @NotNull(message = "多个购买优惠开关不能为空")
    private Integer multipleDiscountFlag;

    @ApiModelProperty(value = "赠送优惠券开关标志，0关闭，1开启")
    @Min(value = 0, message = "赠送优惠券开关标志不合法")
    @Max(value = 1, message = "赠送优惠券开关标志不合法")
    @NotNull(message = "赠送优惠券开关不能为空")
    private Integer couponGiftFlag;

    @ApiModelProperty(value = "交付类型，0=自己交付，1=平台交付")
    private Integer deliveryType;

    @ApiModelProperty(value = "0=默认，1=B端查看更新")
    private Integer type = 0;



}