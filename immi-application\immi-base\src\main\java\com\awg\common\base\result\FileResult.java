package com.awg.common.base.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>FileResult</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023/09/13 16:37
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("通用文件信息")
public class FileResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "路径")
    private String path;

    @ApiModelProperty(value = "url")
    private String url;

    @ApiModelProperty(value = "uid，前端自定义使用")
    private String uid;
}
