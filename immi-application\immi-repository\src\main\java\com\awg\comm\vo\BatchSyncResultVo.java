package com.awg.comm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "批量同步结果")
public class BatchSyncResultVo {

    @ApiModelProperty(value = "成功同步数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败同步数量")
    private Integer failCount;

    @ApiModelProperty(value = "总数量")
    private Integer totalCount;

    @ApiModelProperty(value = "是否全部成功")
    private Boolean allSuccess;

    @ApiModelProperty(value = "失败的商品编号列表")
    private List<String> failedProductNos;

    public BatchSyncResultVo() {
        this.successCount = 0;
        this.failCount = 0;
        this.totalCount = 0;
        this.allSuccess = true;
        this.failedProductNos = new ArrayList<>();
    }

    public BatchSyncResultVo(Integer successCount, Integer failCount) {
        this.successCount = successCount;
        this.failCount = failCount;
        this.totalCount = successCount + failCount;
        this.allSuccess = failCount == 0;
        this.failedProductNos = new ArrayList<>();
    }
}
