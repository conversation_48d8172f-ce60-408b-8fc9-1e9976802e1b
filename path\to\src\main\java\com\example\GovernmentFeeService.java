public void editGovernmentFee(GovernmentFeeDto dto) {
    // 1. 更新政府费信息
    GovernmentFee fee = governmentFeeRepository.findById(dto.getId());
    // 更新政府费字段...

    // 2. 记录编辑日志
    EditLog editLog = new EditLog();
    editLog.setGovernmentFeeId(fee.getId());
    editLog.setDescription("编辑了政府费");
    editLog.setCreatedAt(new Date().getTime() / 1000); // 存储 Unix 时间戳
    editLogRepository.save(editLog);

    // 3. 处理截图
    List<Screenshot> currentScreenshots = screenshotRepository.findByGovernmentFeeId(fee.getId());
    List<Screenshot> newScreenshots = dto.getScreenshots();

    // 删除不再存在的截图
    for (Screenshot cs : currentScreenshots) {
        if (!newScreenshots.contains(cs)) {
            screenshotRepository.delete(cs);
        }
    }

    // 新增或更新截图，并记录到 EditLogScreenshot
    for (Screenshot ns : newScreenshots) {
        ns.setGovernmentFeeId(fee.getId());
        screenshotRepository.save(ns);

        // 记录到 EditLogScreenshot
        EditLogScreenshot editLogScreenshot = new EditLogScreenshot();
        editLogScreenshot.setLogId(editLog.getId());
        editLogScreenshot.setImageUri(ns.getImageUri());
        editLogScreenshot.setCreatedAt((int) (new Date().getTime() / 1000));
        editLogScreenshot.setUpdatedAt((int) (new Date().getTime() / 1000));
        editLogScreenshotRepository.save(editLogScreenshot);
    }
}