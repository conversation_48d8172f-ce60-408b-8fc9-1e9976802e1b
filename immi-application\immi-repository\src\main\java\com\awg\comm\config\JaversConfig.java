package com.awg.comm.config;

import com.awg.comm.dto.MaterialDto;
import com.awg.comm.dto.MaterialGroupDto;
import com.awg.comm.entity.CompareProductEntity;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Javers 配置类
 */
@Configuration
public class JaversConfig {
    /**
     * 创建 Javers Bean 实例
     * 注册需要进行对比的实体类
     */
    @Bean
    public Javers javers() {
        return JaversBuilder.javers()
//                .registerEntity(CompareProductEntity.class)     // 添加这行
//                .registerEntity(MaterialGroupDto.class)  // 注册材料分组实体，自动识别 @Id 注解
//                .registerEntity(MaterialDto.class)       // 注册材料实体，自动识别 @Id 注解
                .build();
    }
}
