package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b>销售咨询类型枚举</b>
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum SalesConsultationTypeEnum {

    /**
     * 销售咨询类型（0=购买，1=咨询，2=无）
     */
    PURCHASE(0, "购买"),
    CONSULTATION(1, "咨询"),
    NONE(2, "无");

    private final Integer code;
    private final String label;

    /**
     * 根据code获取枚举
     *
     * @param code 销售咨询类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static SalesConsultationTypeEnum parse(Integer code) {
        return Arrays.stream(SalesConsultationTypeEnum.values())
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据label获取枚举
     *
     * @param label 销售咨询类型名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static SalesConsultationTypeEnum parse(String label) {
        return Arrays.stream(SalesConsultationTypeEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的销售咨询类型代码
     *
     * @param code 销售咨询类型代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValid(Integer code) {
        return parse(code) != null;
    }

    /**
     * 判断是否为购买类型
     *
     * @param code 销售咨询类型代码
     * @return true表示是购买类型，false表示不是
     */
    public static boolean isPurchase(Integer code) {
        SalesConsultationTypeEnum type = parse(code);
        return type != null && type == PURCHASE;
    }

    /**
     * 判断是否为咨询类型
     *
     * @param code 销售咨询类型代码
     * @return true表示是咨询类型，false表示不是
     */
    public static boolean isConsultation(Integer code) {
        SalesConsultationTypeEnum type = parse(code);
        return type != null && type == CONSULTATION;
    }

    /**
     * 判断是否为无类型
     *
     * @param code 销售咨询类型代码
     * @return true表示是无类型，false表示不是
     */
    public static boolean isNone(Integer code) {
        SalesConsultationTypeEnum type = parse(code);
        return type != null && type == NONE;
    }

    /**
     * 获取所有销售咨询类型代码
     *
     * @return 所有销售咨询类型代码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(SalesConsultationTypeEnum.values())
                .map(SalesConsultationTypeEnum::getCode)
                .toArray(Integer[]::new);
    }

    /**
     * 获取所有销售咨询类型名称
     *
     * @return 所有销售咨询类型名称数组
     */
    public static String[] getAllLabels() {
        return Arrays.stream(SalesConsultationTypeEnum.values())
                .map(SalesConsultationTypeEnum::getLabel)
                .toArray(String[]::new);
    }
}
