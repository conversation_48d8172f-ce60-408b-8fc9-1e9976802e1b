package com.awg.common.sms;

import com.awg.common.redis.RedisUtils;
import com.awg.common.sms.constant.Constant;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * <b>SmsUtil</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/9/29 11:28
 */

@Component
public class SmsCodeUtil {

    @Resource
    private RedisUtils redisUtils;

    /**
     * <p>
     * 保存验证码
     * </p>
     *
     * @param phone : 手机号
     * @author: 夜晓
     * @date: 2021-09-29
     * @return: void
     */
    public String generateCode(String phone) {
        String key = Constant.SMS_CODE + phone;
        //如果未过期则返回
        if (redisUtils.exists(key)) {
            return String.valueOf(redisUtils.get(key));
        }

        String code = RandomStringUtils.randomNumeric(6);
        redisUtils.set(key, code, Constant.EXPIRE, TimeUnit.SECONDS);
        return code;
    }

    /**
     * <p>
     * 效验验证码是否正确
     * </p>
     *
     * @param code  : 验证码
     * @param phone : 手机号
     * @author: 夜晓
     * @date: 2021-09-29
     * @return: boolean
     */
    public boolean validateCode(String code, String phone) {
        boolean isExist = redisUtils.exists(Constant.SMS_CODE + phone);
        if (!isExist) {
            return false;
        }

        String result = String.valueOf(redisUtils.get(Constant.SMS_CODE + phone));
        boolean isEffective = StringUtils.equals(code, result);
        // 验证码只可被使用一次，效验完成后移除
        if (isEffective) {
            redisUtils.remove(Constant.SMS_CODE + phone);
        }
        return isEffective;
    }
}
