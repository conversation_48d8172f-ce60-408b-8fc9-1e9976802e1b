package com.awg.externalAPI.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.mybatis.entity.BaseEntity;
import com.awg.system.entity.OrgInfo;
import com.awg.system.eo.OrgInfoEo;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.BannerDto;
import com.awg.website.dto.WebsiteConfigDto;
import com.awg.website.entity.WebsiteBaseConfig;
import com.awg.website.enums.DisplayTargetEnum;
import com.awg.website.mapper.WebsiteBaseConfigMapper;
import com.awg.website.service.IWebsiteBannerService;
import com.awg.website.service.IWebsiteDirectoryService;
import com.awg.website.vo.GetBannerDataVo;
import com.awg.website.vo.QueryBannerVo;
import com.awg.website.vo.QueryDirectoryListVo;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-04
 */
@ApiSupport( order = 65 )
@Api( tags = {"官网-公共接口"} )
@RestController
@RequestMapping( "/externalAPI/website/commons" )
public class ExternalWebsiteCommonsController extends BaseController {

    @Resource
    private IWebsiteDirectoryService websiteDirectoryService;

    @Resource
    private WebsiteBaseConfigMapper websiteBaseConfigMapper;

    @Resource
    private IWebsiteBannerService websiteBannerService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取配置" )
    @PostMapping( "/getConfig" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult getConfig(@RequestBody(required = false) GetBannerDataVo vo) {

        // 获取域名
        String domain = getDomainHost(vo==null ? null : vo.getHost());

        // 根据域名查找匹配机构
        OrgInfoEo orgInfoEo = orgExternalService.getOrgInfoByDomain(domain);

        if(orgInfoEo==null) {
            orgInfoEo = orgExternalService.getOrgInfoByOrgId(1);
        }

        WebsiteConfigDto websiteConfigDto = new WebsiteConfigDto();

        if(orgInfoEo == null) {
            // 返回默认
            websiteConfigDto.setOrgId(1);
            websiteConfigDto.setPhoneDesc("3345678");
        }
        else {
            websiteConfigDto.setOrgId(orgInfoEo.getOrgId());
            websiteConfigDto.setPhoneDesc("3345678");
        }

        // 获取基础配置配置信息
        WebsiteBaseConfig websiteBaseConfig = websiteBaseConfigMapper.selectOne(Wrappers.<WebsiteBaseConfig>lambdaQuery()
                .eq(BaseEntity::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .eq(WebsiteBaseConfig::getOrgId, websiteConfigDto.getOrgId())
        );

        if(websiteBaseConfig!=null) {
            BeanUtils.copyProperties(websiteBaseConfig, websiteConfigDto);
            websiteConfigDto.setCustomerServiceQrCode(orgExternalService.getFileUrlByDomain(websiteConfigDto.getOrgId(), websiteBaseConfig.getQrcodeImage()));
        }

        websiteConfigDto.setOfficialWebsiteDomain(orgInfoEo.getOfficialWebsiteDomain());

        websiteConfigDto.setIcon(orgExternalService.getFileUrlByDomain(websiteConfigDto.getOrgId(), websiteConfigDto.getIcon()));
        websiteConfigDto.setLogo(orgExternalService.getFileUrlByDomain(websiteConfigDto.getOrgId(), websiteConfigDto.getLogo()));
        websiteConfigDto.setFooterImage(orgExternalService.getFileUrlByDomain(websiteConfigDto.getOrgId(), websiteConfigDto.getFooterImage()));


        return renderSuccess(websiteConfigDto);
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "目录列表" )
    @PostMapping( "/getDirectoryList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult getDirectoryList(@RequestBody QueryDirectoryListVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteDirectoryService.directoryList(vo, vo.getOrgId()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "banner列表" )
    @PostMapping( "/getBannerList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult getBannerList(@RequestBody GetBannerDataVo vo) {
        ValidationUtils.validate(vo);

        QueryBannerVo bannerVo = new QueryBannerVo();
        bannerVo.setOrgId(vo.getOrgId());
        bannerVo.setPageNo(1);
        bannerVo.setPageSize(100);
        bannerVo.setDisplayTarget(vo.getDisplayTarget());
        bannerVo.setDisplayFlag(TrueFalseEnum.TRUE.getCode());
        BasePageResult<BannerDto> bannerResult = websiteBannerService.queryBannerList(bannerVo);

        // 循环填入banner完整路径
        for (BannerDto bannerDto : bannerResult.getData()) {
            bannerDto.setImageUrl(orgExternalService.getFileUrlByDomain(vo.getOrgId(), bannerDto.getImagePath()));
        }

        return renderSuccess(bannerResult.getData());
    }
}
