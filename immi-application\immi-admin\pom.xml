<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.awg.saas</groupId>
        <artifactId>immi-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <artifactId>immi-admin</artifactId>
    <version>1.0-SNAPSHOT</version>

    <dependencies>

        <dependency>
            <groupId>com.awg.saas</groupId>
            <artifactId>immi-repository</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- spring test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <!-- 插件管理 -->
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>

            </plugin>


            <!-- maven打包跳过test -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 针对不同环境下配置文件的打包方法 -->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-resources-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>copy-resources</id>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>copy-resources</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <outputDirectory>src/main/resources</outputDirectory>-->
<!--                            <resources>-->
<!--                                <resource>-->
<!--                                    <directory>src/main/resources/config/${configPath}</directory>-->
<!--                                    <filtering>true</filtering>-->
<!--                                    <includes>-->
<!--                                        <include>*.properties</include>-->
<!--                                        <include>*.xml</include>-->
<!--                                    </includes>-->
<!--                                </resource>-->
<!--                            </resources>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <!--默认是dev环境  -->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <configPath>dev</configPath>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <configPath>test</configPath>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <configPath>pre</configPath>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <configPath>staging</configPath>
            </properties>
        </profile>
    </profiles>

</project>