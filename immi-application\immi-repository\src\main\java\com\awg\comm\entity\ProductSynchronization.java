package com.awg.comm.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品同步表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "comm_product_synchronization")
public class ProductSynchronization extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 机构id */
    private Integer orgId;

    /** 用户信息id */
    private Integer userInfoId;

    /** 机构类型 */
    private Integer orgType;

    /** 同步类型（1: 咨询, 2: 无支付咨询入口） */
    private Integer synchronizationType;

    /** 咨询按钮文案 */
    private String consultationButtonText;

    /** 咨询顾问微信号 */
    private String consultantWechat;

    /** 咨询顾问二维码 */
    private String consultantQrcode;

} 