<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.awg.saas</groupId>
    <artifactId>immi-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <awg.platform.version>1.0-SNAPSHOT</awg.platform.version>
        <spring-boot.version>2.7.10</spring-boot.version>
        <log4j.version>2.17.2</log4j.version>
		<commons-codec.version>1.15</commons-codec.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!--业务模块,公用基类，例如：BaseController、BasePageVo、BasePageResult等-->
            <dependency>
                <groupId>com.awg.saas</groupId>
                <artifactId>immi-base</artifactId>
                <version>${awg.platform.version}</version>
            </dependency>

            <!--框架工具类子模块-->
            <dependency>
                <groupId>com.awg.saas</groupId>
                <artifactId>immi-commons-utils</artifactId>
                <version>${awg.platform.version}</version>
            </dependency>
			
			<!--第三方类子模块-->
			<dependency>
			    <groupId>com.awg.saas</groupId>
			    <artifactId>immi-commons-thirdparty-sdk</artifactId>
			    <version>${awg.platform.version}</version>
			</dependency>

            <!--mybatis plus-->
            <dependency>
                <groupId>com.awg.saas</groupId>
                <artifactId>immi-mybatis-plus</artifactId>
                <version>${awg.platform.version}</version>
            </dependency>

            <!--spring boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--spring aop-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- SpringBoot整合Web组件 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- log4j -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.springframework.boot</groupId>-->
    <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--                <executions>-->
    <!--                    <execution>-->
    <!--                        <goals>-->
    <!--                            <goal>repackage</goal>-->
    <!--                        </goals>-->
    <!--                    </execution>-->
    <!--                </executions>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->

</project>
