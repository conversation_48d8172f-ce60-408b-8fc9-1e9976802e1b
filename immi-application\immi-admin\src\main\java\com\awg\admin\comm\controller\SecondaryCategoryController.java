package com.awg.admin.comm.controller;

import com.awg.comm.dto.DistrictDto;
import com.awg.comm.entity.ProductSecondaryCategory;
import com.awg.comm.service.IDistrictInfoService;
import com.awg.comm.service.IProductSecondaryCategoryService;
import com.awg.comm.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 223 )
@Api( tags = {"商品二级分类相关接口"} )
@RestController
@RequestMapping( "/comm/secondaryCategory" )
public class SecondaryCategoryController extends BaseController {

    @Resource
    private IProductSecondaryCategoryService productSecondaryCategoryService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取二级分类列表【全部】" )
    @PostMapping( "/listAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductSecondaryCategory.class )
    })
    public DataResult listAll(@RequestBody QueryDistrictVo vo) {
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        List<ProductSecondaryCategory> result = productSecondaryCategoryService.getSecondaryCategoryAll(vo);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "添加" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addSecondaryCategory(@RequestBody SecondaryCategoryVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productSecondaryCategoryService.addSecondaryCategory(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "更新" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateSecondaryCategory(@RequestBody SecondaryCategoryVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productSecondaryCategoryService.updateSecondaryCategory(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除" )
    @PostMapping( "/delete/{secondaryCategory}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult deleteSecondaryCategory(@PathVariable( value = "secondaryCategory" ) Integer secondaryCategory) {
        AssertUtils.isTrue(secondaryCategory==null || secondaryCategory<=0, "请先传入id");
        productSecondaryCategoryService.deleteSecondaryCategory(secondaryCategory, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 55)
    @ApiOperation( "排序" )
    @PostMapping( "/sort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortSecondaryCategory(@RequestBody DistrictSortVo vo) {
        vo.setPid(0);
        ValidationUtils.validate(vo);
        productSecondaryCategoryService.sortSecondaryCategory(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 65)
    @ApiOperation( "显示/隐藏" )
    @PostMapping( "/displayChange" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult displayChange(@RequestBody DisplayChangeVo vo) {
        ValidationUtils.validate(vo);
        productSecondaryCategoryService.displayChange(vo, getUserLoginInfoEo());
        return renderSuccess();
    }
}
