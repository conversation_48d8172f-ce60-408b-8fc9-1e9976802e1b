package com.awg.common.base.page;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2019-11-17 22:16
 * @version: V1.0
 **/
public class BasePageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 业务描述
     */
    @ApiModelProperty(value = "业务描述")
    private String msg = "操作成功";

    /**
     * 当前第几页
     */
    @ApiModelProperty(value = "当前第几页")
    private Integer pageNo;

    /**
     * 分页参数-每页显示的总记录数
     */
    @ApiModelProperty(value = "每页显示的总记录数")
    private Integer pageSize;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Long count;

    /**
     * 业务数据集合
     */
    @ApiModelProperty(value = "业务数据集合")
    private List<T> data;


    public BasePageResult(BasePageVo basePageVo) {
        this.count = 0L;
        this.data = new ArrayList<T>();
        this.pageNo = basePageVo.getPageNo();
        this.pageSize = basePageVo.getPageSize();
    }

    public BasePageResult(Long count, List<T> data, BasePageVo basePageVo) {
        this.count = count;
        this.data = data;
        this.pageNo = basePageVo.getPageNo();
        this.pageSize = basePageVo.getPageSize();
    }

    public BasePageResult(Long count, List<T> data, Integer pageNo, Integer pageSize) {
        this.count = count;
        this.data = data;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public BasePageResult(String msg, Integer pageNo, Integer pageSize, Long count, List<T> data) {
        this.msg = msg;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.count = count;
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}