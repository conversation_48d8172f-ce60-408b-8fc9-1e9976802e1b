需求：平台的商品列表中每一行的商品数据都有个同步到B端按钮，当点击后，会把当前点击的商品同步到没有手动导入过平台商品的B端机构，当平台商品点击了同步到B端按钮后，该按钮变暗，不可变点击，同时B端机构那边多出一条同步过来的商品数据。如果平台那边编辑了该商品数据，那么该商品的同步到B端变成更新到B端，同时后台逻辑为：如果B端机构那边没有对同步过来的这条商品做编辑修改操作，那么直接继续和平台的这个修改了的商品进行同步，其实就是修改B端机构的那个商品，只不过数据和平台商品一样，但是如果更新到B端的时候，发现B端修改了数据，那么平台的这个商品就会对比(平台当前编辑后的商品和上一个版本商品对比，看那个地方修改了，把这个修改了的地方通知给B端，B端手动进行同步(就是这个修改了的地方可以进行手动同步))。然后平台商品如果继续修改，那么按钮就又可以进行：更新到B端，但是这个时候就不判断B端那边有没有修改了，都是平台这边当前版本和上一个版本对比，把修改了的地方告诉B端就行了，B端那边手动看想不想改。

平台机构、B端机构、其他机构(可以理解为账号、平台是最高权限)

商品有两个表comm_product(关键字段：id、商品编号、版本号、机构id(1是平台、2是B端、还有其他))和comm_product_data(关键字段、商品编号、版本号)

登录平台只能看到平台的商品、登录B端只能看B端的商品

创建一个商品的时候comm_product表会新增一条数据、comm_product_data也会新增一条数据 他们共同组成一条单独的商品，他们的商品编号都是一样的，但是如果对该商品编辑修改后，comm_product表中的版本号会变同时comm_product_data表会新增一条数据，版本号会和comm_product中的版本号对应，显示该新增的数据是最新数据

然后现在的需求是：在平台端显示所有的商品列表，每个平台商品旁边都有一个同步到B端按钮(可以点击),当点击后该商品会同步数据到所有的B端机构中(注意，可以理解为机构是一个个账号，所以是有多个的，但是这里不是说直接同步所有机构，所有条件的(只有B端机构那边开启了同步功能的机构才能同步商品过来))，然后呢同步玩了后，如果这个时候我编辑修改了平台的某一个商品，那么该商品旁边的同步到B端按钮就会变成更新到B端，点击后，进行判断：如果之前同步过去的商品在B端机构那边没有手动修改过，那么直接同步(其实就是修改成和平台这边一样的数据，注意是数据副本，不是同一份)，如果发现B端那边也修改了，那么不直接同步，由B端那边人员手动进行同步(就是B端那边商品旁边也有一个小按钮：数据可更新，点击后弹出进入商品详情页面，平台那边是改的那个数据，那么这个数据旁边就有个小按钮：同步)



注意：B端机构那边有一个对应的商品同步表来判断该B断机构是否开启了同步功能





表结构：

CREATE TABLE `comm_product`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `short_code` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短码',
  `availability_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '可用状态(0=未上架，1=已上架)',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `category` int(11) NOT NULL DEFAULT 1 COMMENT '分类（1=签证类，2=申校类，3=本地服务）',
  `resettable_stock` int(11) NOT NULL DEFAULT 0 COMMENT '可重置库存',
  `initial_stock` int(11) NOT NULL DEFAULT 0 COMMENT '初始库存-用于重置库存',
  `promo_stock` int(11) NULL DEFAULT NULL COMMENT '促销库存',
  `stock_open_flag` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存开启标志',
  `promo_open_flag` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '促销开启标志',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  `is_sync` tinyint(2) NOT NULL DEFAULT 0 COMMENT '同步状态(0:可同步,1:已同步,2:可更新)',
  `sync_source` tinyint(2) NOT NULL DEFAULT 0 COMMENT '数据来源（0=手动导入，1=平台同步）',
  `last_vid` int(11) NULL DEFAULT NULL COMMENT '上一个编辑的商品版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `product_no`(`product_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 478 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;





DROP TABLE IF EXISTS `comm_product_data`;
CREATE TABLE `comm_product_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_no` bigint(20) NOT NULL COMMENT '商品编号',
  `product_vid` int(11) NOT NULL COMMENT '商品版本id',
  `source_no` bigint(20) NOT NULL DEFAULT 0 COMMENT '来源编号',
  `org_id` int(11) NOT NULL DEFAULT 1 COMMENT '机构id',
  `category` int(11) NOT NULL DEFAULT 1 COMMENT '分类（1=签证类，2=申校类）',
  `sales_consultation_type` int(11) NOT NULL DEFAULT 0 COMMENT '销售咨询类型（0=购买，1=咨询，2=无）',
  `secondary_category` int(11) NOT NULL DEFAULT 0 COMMENT '二级分类id',
  `educational_stage` int(11) NULL DEFAULT NULL COMMENT '教育阶段（1=中小学，2=college，3=本科，4=研究生）',
  `district_id` int(11) NULL DEFAULT NULL COMMENT '地区id',
  `name` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `school_logo` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '学校logo',
  `cover` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `cover_width` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品封面图宽度',
  `cover_height` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品封面图高度',
  `purchase_button_text` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '立即购买' COMMENT '购买按钮文案',
  `promotion_button_text` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '立即抢购' COMMENT '促销按钮文案',
  `consultation_button_text` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '立即咨询' COMMENT '咨询按钮文案',
  `consultant_wechat` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '咨询顾问微信',
  `consultant_qrcode` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '咨询顾问二维码',
  `keyword_ids` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词id列表',
  `price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '价格，0表示免费',
  `platform_delivery_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '平台交付价格',
  `promo_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '促销价格，0表示免费，null表示不支持',
  `second_discount_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '第二件优惠价',
  `third_discount_price` decimal(12, 2) UNSIGNED NULL DEFAULT NULL COMMENT '第三件优惠价',
  `related_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联业务编号',
  `related_child_business_no` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联的子级业务编号',
  `multiple_discount_flag` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '多个购买优惠开关',
  `coupon_gift_flag` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '赠送优惠券开关',
  `source_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源类型，0=自建，1=平台',
  `delivery_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '交付类型，0=自己交付，1=平台交付，2=导入编辑',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `fee_description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '费用描述',
  `discount_description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠说明',
  `notes` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '注意事项',
  `terms_of_service` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务条款',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id，系统操作的话为0',
  `created_at` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `updated_at` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除（0:否，1:是）',
  `is_modified` tinyint(4) NULL DEFAULT 0 COMMENT 'B端是否修改过此商品(0:否,1:是)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `product_no_product_vid`(`product_no`, `product_vid`) USING BTREE,
  INDEX `product_no`(`product_no`) USING BTREE,
  INDEX `product_vid`(`product_vid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2679 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品数据表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;