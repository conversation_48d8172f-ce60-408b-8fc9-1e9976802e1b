package com.awg.account.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * <b>AdjustBalanceVo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16
 */

@Data
@ApiModel(value = "调整钱包参数")
public class AdjustBalanceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型（1=增加，2=减少）", required = true)
    @NotNull(message = "类型不能为空")
    @Min(value = 1, message = "类型不合法")
    @Max(value = 2, message = "类型不合法")
    private Integer type;

    @ApiModelProperty(value = "金额", required = true)
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空")
    private String remarks;

    @ApiModelProperty(value = "钱包编号", required = true)
    @NotBlank(message = "钱包编号不能为空")
    private String walletNo;
}
