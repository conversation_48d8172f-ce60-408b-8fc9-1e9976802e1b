package com.awg.bp.entity;

import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 业务表 - 新版本（支持MyBatis-Plus逻辑删除）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bp_business")
public class NewBusiness extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 父级编号
     */
    private String parentNo;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 所属类型（0: 中介，1平台）
     */
    private Integer ownerType;

    /**
     * 是否来自平台
     */
    private Integer fromPlatformFlag;

    /**
     * 展示标志[0不展示，1展示]
     */
    private Integer displayFlag;

    /**
     * 删除id，未删除为0，用于软删除的唯一索引
     */
    private Integer deletedId;

    /**
     * 业务类型id
     */
    private Integer typeId;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 归属业务编号【已废弃】
     */
    private String parentBusinessNo;

    /**
     * 区域（国家/地区）
     */
    private String region;

    /**
     * 是否禁用（0:否，1:是）
     */
    private Integer disabledFlag;

    /**
     * 排序
     */
    @TableField("`order`")
    private Integer order;
}