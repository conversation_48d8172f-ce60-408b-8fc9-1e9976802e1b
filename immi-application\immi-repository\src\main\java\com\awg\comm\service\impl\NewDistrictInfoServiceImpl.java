package com.awg.comm.service.impl;

import com.awg.comm.dto.DistrictDto;
import com.awg.comm.entity.NewDistrictInfo;
import com.awg.comm.mapper.NewDistrictInfoMapper;
import com.awg.comm.service.NewDistrictInfoService;
import com.awg.comm.vo.DisplayChangeVo;
import com.awg.comm.vo.DistrictInfoVo;
import com.awg.comm.vo.DistrictSortVo;
import com.awg.comm.vo.QueryDistrictVo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 地区信息表 服务实现类 - 新版本
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class NewDistrictInfoServiceImpl extends ServiceImpl<NewDistrictInfoMapper, NewDistrictInfo> implements NewDistrictInfoService {

    @Resource
    private NewDistrictInfoMapper newDistrictInfoMapper;

    @Resource
    private IOrgExternalService orgExternalService;

    /**
     * <p>
     * 获取地区列表
     * </p>
     *
     * @return:
     */
    @Override
    public List<DistrictDto> getDistrictListAll(QueryDistrictVo vo) {
        List<DistrictDto> result = newDistrictInfoMapper.getDistrictListAll(vo, "CA");
        return generateTree(result, 0, 0);
    }

    /**
     * <p>
     * 添加地区
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addDistrictInfo(DistrictInfoVo vo, UserLoginInfoEo userLoginInfoEo) {
        Integer targetId = vo.getPid();

        if(targetId > 0) {
            NewDistrictInfo targetItem = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                    .eq(NewDistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
                    .and(wrapper -> wrapper.eq(NewDistrictInfo::getId, targetId)
                            .or().eq(NewDistrictInfo::getFromPlatformId, targetId))
            );
            AssertUtils.isNull(targetItem, "目标不存在");
            vo.setPid(targetItem.getId());
        }

        // 名称不能重复
        NewDistrictInfo districtInfo = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                .eq(NewDistrictInfo::getName, vo.getName())
                .eq(NewDistrictInfo::getPid, vo.getPid())
                .eq(NewDistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
                .eq(NewDistrictInfo::getRegionCode, "CA")
        );
        AssertUtils.notNull(districtInfo, "名称重复");

        // 找出当前最大的排序值order
        NewDistrictInfo maxOrder = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                .eq(NewDistrictInfo::getPid, vo.getPid())
                .eq(NewDistrictInfo::getRegionCode, "CA")
                .orderByDesc(NewDistrictInfo::getOrder)
                .last("limit 1")
        );

        // 创建
        districtInfo = new NewDistrictInfo();
        districtInfo.setName(vo.getName());
        districtInfo.setPid(vo.getPid());
        districtInfo.setOrgId(userLoginInfoEo.getOrgId());
        districtInfo.setRegionCode("CA");
        newDistrictInfoMapper.insert(districtInfo);

        districtInfo.setOrder(districtInfo.getId());

        if(maxOrder != null) {
            // 排序排到最后
            districtInfo.setOrder(maxOrder.getOrder() + 1);
        }

        newDistrictInfoMapper.updateById(districtInfo);

        if(userLoginInfoEo.getOrgType().equals(3)) {
            List<Integer> orgIdList = orgExternalService.getOrgIdListByType(5);

            for (Integer orgId : orgIdList) {
                NewDistrictInfo districtInfoP = null;
                if(districtInfo.getPid() > 0) {
                    districtInfoP = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                            .eq(NewDistrictInfo::getOrgId, orgId)
                            .eq(NewDistrictInfo::getFromPlatformId, districtInfo.getPid())
                    );
                }

                maxOrder = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                        .eq(NewDistrictInfo::getPid, districtInfoP == null ? vo.getPid() : districtInfoP.getId())
                        .eq(NewDistrictInfo::getOrgId, orgId)
                        .eq(NewDistrictInfo::getRegionCode, "CA")
                        .orderByDesc(NewDistrictInfo::getOrder)
                        .last("limit 1")
                );

                NewDistrictInfo itemNew = new NewDistrictInfo();
                BeanUtils.copyProperties(districtInfo, itemNew, new String[]{"id", "updatedAt"});

                itemNew.setOrgId(orgId);
                itemNew.setFromPlatformId(districtInfo.getId());
                itemNew.setDisplayFlag(0);

                if(districtInfo.getPid() > 0) {
                    itemNew.setPid(districtInfoP.getId());
                }

                newDistrictInfoMapper.insert(itemNew);

                itemNew.setOrder(itemNew.getId());

                if(maxOrder != null) {
                    // 排序排到最后
                    itemNew.setOrder(maxOrder.getOrder() + 1);
                }

                newDistrictInfoMapper.updateById(itemNew);
            }
        }

        return districtInfo.getId();
    }

    /**
     * <p>
     * 修改地区
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateDistrictInfo(DistrictInfoVo vo, UserLoginInfoEo userLoginInfoEo) {
        Integer districtId = vo.getDistrictId();
        AssertUtils.isTrue(districtId == null || districtId <= 0, "请先传入要修改的地区id");

        Integer targetId = vo.getPid();

        if(targetId > 0) {
            NewDistrictInfo targetItem = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                    .eq(NewDistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
                    .and(wrapper -> wrapper.eq(NewDistrictInfo::getId, targetId)
                            .or().eq(NewDistrictInfo::getFromPlatformId, targetId))
            );
            AssertUtils.isNull(targetItem, "目标不存在");
            vo.setPid(targetItem.getId());
        }

        // 地区不存在
        NewDistrictInfo districtInfo = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                .eq(NewDistrictInfo::getId, vo.getDistrictId())
                .eq(NewDistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
        );
        AssertUtils.isNull(districtInfo, "地区信息不存在");

        // 名称不能重复
        NewDistrictInfo districtInfo2 = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                .eq(NewDistrictInfo::getName, vo.getName())
                .eq(NewDistrictInfo::getPid, vo.getPid())
                .eq(NewDistrictInfo::getOrgId, userLoginInfoEo.getOrgId())
                .eq(NewDistrictInfo::getRegionCode, "CA")
                .ne(NewDistrictInfo::getId, districtInfo.getId())
        );
        AssertUtils.notNull(districtInfo2, "名称重复");

        districtInfo.setName(vo.getName());
        districtInfo.setPid(vo.getPid());
        newDistrictInfoMapper.updateById(districtInfo);

        if(userLoginInfoEo.getOrgType().equals(3)) {
            List<NewDistrictInfo> list = newDistrictInfoMapper.selectList(Wrappers.<NewDistrictInfo>lambdaQuery()
                    .ne(NewDistrictInfo::getOrgId, 1)
                    .eq(NewDistrictInfo::getFromPlatformId, districtInfo.getId())
            );
            for (NewDistrictInfo item : list) {
                item.setName(vo.getName());

                if(districtInfo.getPid() > 0) {
                    NewDistrictInfo districtInfoP = newDistrictInfoMapper.selectOne(Wrappers.<NewDistrictInfo>lambdaQuery()
                            .eq(NewDistrictInfo::getOrgId, item.getOrgId())
                            .eq(NewDistrictInfo::getFromPlatformId, districtInfo.getPid())
                    );

                    item.setPid(districtInfoP.getId());
                }

                newDistrictInfoMapper.updateById(item);
            }
        }

        return districtInfo.getId();
    }

    /**
     * <p>
     * 删除地区信息
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDistrictInfo(Integer districtId, UserLoginInfoEo userLoginInfoEo) {
        // 使用MyBatis-Plus逻辑删除
        removeById(districtId);
    }

    /**
     * <p>
     * 地区信息排序
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sortDistrictInfo(DistrictSortVo vo, UserLoginInfoEo userLoginInfoEo) {
        // 实现排序逻辑
        // TODO: 根据具体需求实现
    }

    /**
     * <p>
     * 修改地区展示状态
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeDisplayFlag(DisplayChangeVo vo, UserLoginInfoEo userLoginInfoEo) {
        // 实现展示状态修改逻辑
        // TODO: 根据具体需求实现
    }

    /**
     * 生成树形结构
     */
    private List<DistrictDto> generateTree(List<DistrictDto> list, Integer pid, Integer level) {
        List<DistrictDto> result = new ArrayList<>();
        for (DistrictDto item : list) {
            if (item.getPid().equals(pid)) {
                item.setChildren(generateTree(list, item.getDistrictId(), level + 1));
                result.add(item);
            }
        }
        return result;
    }
}