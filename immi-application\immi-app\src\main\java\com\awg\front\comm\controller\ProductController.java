package com.awg.front.comm.controller;

import com.awg.client.eo.MemberLoginInfoPlusEo;
import com.awg.client.vo.GetMemberInviteCodeVo;
import com.awg.comm.dto.*;
import com.awg.comm.entity.ProductSecondaryCategory;
import com.awg.comm.service.IDistrictInfoService;
import com.awg.comm.service.IProductKeywordService;
import com.awg.comm.service.IProductSecondaryCategoryService;
import com.awg.comm.service.impl.AppModuleConfigServiceImpl;
import com.awg.comm.service.IProductService;
import com.awg.comm.vo.GetProductQrcodeVo;
import com.awg.comm.vo.QueryDistrictVo;
import com.awg.comm.vo.QueryProductVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 70 )
@Api( tags = {"商品-相关接口"} )
@RestController
@RequestMapping( "/comm/product" )
@Slf4j
public class ProductController extends BaseController {

    @Resource
    private IProductService productService;

    @Resource
    private IDistrictInfoService districtInfoService;

    @Resource
    private IProductKeywordService productKeywordService;

    @Resource
    private IProductSecondaryCategoryService productSecondaryCategoryService;

    @Resource
    private AppModuleConfigServiceImpl appModuleConfigService;

    @ApiOperation( "商品列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductMinDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult list(@RequestBody QueryProductVo vo) {
        AssertUtils.isTrue(vo.getOrgId()==null || vo.getOrgId()<=0, "公司id不能为空");
        ValidationUtils.validate(vo);
        vo.setAvailabilityStatus(1);
        BasePageResult<ProductMinDto> result = productService.getProductMiniList(vo);
        return renderSuccess(result);
    }

    @ApiOperation( "商品详情" )
    @PostMapping( "/detail/{productNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductInfoMiniDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult detail(@PathVariable( value = "productNo" ) String productNo) {
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        // 是否已经登录
        boolean isLogin = false;

        try {
            Integer memberId = getCurrentMemberId();
            if(memberId!=null && memberId>0) {
                isLogin = true;
            }
        } catch (Exception e) {
            isLogin = false;
        }

        return renderSuccess(productService.getProductDetailMini(productNo, isLogin, null, null));
    }

    @ApiOperation( "获取商品小程序码" )
    @PostMapping( "/getProductQrcodeUrl" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult getProductQrcodeUrl(@RequestBody GetProductQrcodeVo vo) {
        String inviteCode = productService.getProductQrcodeUrl(vo);
        return renderSuccess(inviteCode);
    }

    @ApiOperation( "地区信息列表" )
    @PostMapping( "/districtInfoList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = DistrictDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult districtInfoList(@RequestBody QueryDistrictVo vo) {
        ValidationUtils.validate(vo);
        List<DistrictDto> result = districtInfoService.getDistrictListAll(vo);
        return renderSuccess(result);
    }

    @ApiOperation( "获取关键词列表" )
    @PostMapping( "/getProductKeywordAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = DistrictDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult getProductKeywordAll(@RequestBody QueryDistrictVo vo){
        return renderSuccess(productKeywordService.getProductKeywordAll(vo, null));
    }

    @ApiOperation( "获取二级分类列表" )
    @PostMapping( "/getSecondaryCategoryAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductSecondaryCategory.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult getSecondaryCategoryAll(@RequestBody QueryDistrictVo vo){
        return renderSuccess(productSecondaryCategoryService.getSecondaryCategoryAll(vo));
    }

    @ApiOperation( "获取小程序模块配置" )
    @PostMapping( "/getAppModuleConfig/{orgId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AppModuleConfigDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult getAppModuleConfig(@PathVariable ( value = "orgId" ) Integer orgId){
        return renderSuccess(appModuleConfigService.getAppModuleConfig(orgId));
    }


}
