package com.awg.comm.mapstruct;
import com.awg.comm.entity.ProductSynchronization;
import com.awg.comm.vo.ProductSynchronizationCreateVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

//@Mapper(componentModel = "spring") // 推荐用spring，自动注入
public interface ProductSynchronizationMapstruct {
//    Mappers.getMapper(ProductSynchronizationMapstruct.class);




    // vo -> entity
    ProductSynchronization toEntity(ProductSynchronizationCreateVo vo);

    // entity -> vo
    ProductSynchronizationCreateVo toCreateVo(ProductSynchronization entity);


}