package com.awg.account.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * <b>WalletTransactionItemDto</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel(value = "钱包交易流水信息")
public class WalletTransactionItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "余额快照")
    private BigDecimal balanceSnapshot;

    @ApiModelProperty(value = "余额改变值")
    private BigDecimal balanceChange;

    @ApiModelProperty(value = "可用余额快照")
    private BigDecimal availableBalanceSnapshot;

    @ApiModelProperty(value = "可用余额改变值")
    private BigDecimal availableBalanceChange;

    @ApiModelProperty(value = "待到账金额快照")
    private BigDecimal pendingBalanceSnapshot;

    @ApiModelProperty(value = "待到账金额改变值")
    private BigDecimal pendingBalanceChange;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "操作类型名称")
    private String actionCategoryLabel;

    @ApiModelProperty(value = "操作类型")
    private Integer actionCategory;

    @ApiModelProperty(value = "操作/行为名称")
    private String actionLabel;

    @ApiModelProperty(value = "操作/行为代码")
    private Integer actionCode;

    @ApiModelProperty(value = "分佣等级（一级分佣还是二级分佣）")
    private Integer commissionLevel;

    @ApiModelProperty(value = "分佣接受人类型（0=自己，1=B端，2=C端）")
    private Integer commissionRecipientType;

    @ApiModelProperty(value = "交易时间（时间戳）")
    private Integer createdAt;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态（0=待付款、1=已完成、2=已结算，5已取消，6=已过期，7=已作废，9订单异常）")
    private Integer orderStatus;
}
