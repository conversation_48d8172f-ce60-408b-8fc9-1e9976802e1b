package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b>商品同步状态枚举</b>
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ProductSyncStatusEnum {

    /**
     * 商品同步状态（0=未同步，1=已同步，2=更新同步到B端）
     */
    NOT_SYNCED(0, "未同步"),
    SYNCED(1, "已同步"),
    UPDATED_TO_B_END(2, "更新同步到B端");

    private final Integer code;
    private final String label;

    /**
     * 根据code获取枚举
     *
     * @param code 同步状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProductSyncStatusEnum parse(Integer code) {
        return Arrays.stream(ProductSyncStatusEnum.values())
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据label获取枚举
     *
     * @param label 同步状态名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProductSyncStatusEnum parse(String label) {
        return Arrays.stream(ProductSyncStatusEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的同步状态代码
     *
     * @param code 同步状态代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValid(Integer code) {
        return parse(code) != null;
    }

    /**
     * 判断是否未同步
     *
     * @param code 同步状态代码
     * @return true表示未同步，false表示已同步
     */
    public static boolean isNotSynced(Integer code) {
        ProductSyncStatusEnum status = parse(code);
        return status != null && status == NOT_SYNCED;
    }

    /**
     * 判断是否已同步
     *
     * @param code 同步状态代码
     * @return true表示已同步，false表示未同步
     */
    public static boolean isSynced(Integer code) {
        ProductSyncStatusEnum status = parse(code);
        return status != null && status == SYNCED;
    }

    /**
     * 判断是否为更新同步到B端状态
     *
     * @param code 同步状态代码
     * @return true表示更新同步到B端，false表示不是
     */
    public static boolean isUpdatedToBEnd(Integer code) {
        ProductSyncStatusEnum status = parse(code);
        return status != null && status == UPDATED_TO_B_END;
    }

    /**
     * 获取所有同步状态代码
     *
     * @return 所有同步状态代码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(ProductSyncStatusEnum.values())
                .map(ProductSyncStatusEnum::getCode)
                .toArray(Integer[]::new);
    }

    /**
     * 获取所有同步状态名称
     *
     * @return 所有同步状态名称数组
     */
    public static String[] getAllLabels() {
        return Arrays.stream(ProductSyncStatusEnum.values())
                .map(ProductSyncStatusEnum::getLabel)
                .toArray(String[]::new);
    }
}
