package com.awg.admin.system.controller;

import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.mybatis.entity.BaseEntity;
import com.awg.system.dto.*;
import com.awg.system.entity.Org;
import com.awg.system.entity.UserPreferences;
import com.awg.system.eo.UserMinEo;
import com.awg.system.eo.UserMinPlusEo;
import com.awg.system.eo.UserMsgCursorEo;
import com.awg.system.eo.UserUnreadMsgEo;
import com.awg.system.externalService.IUserMsgViewedExternalService;
import com.awg.system.mapper.UserPreferencesMapper;
import com.awg.system.service.IUserService;
import com.awg.system.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.utils.date.DateUtils;
import com.awg.wechat.externalService.IWeappExternalService;
import com.awg.wechat.service.IAppletAuthorizerService;
import com.awg.wechat.vo.CreateWeappQrcodeVo;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import com.awg.common.validator.ValidationUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport ( order = 10 )
@Api ( tags = {"企业用户相关接口"} )
@RestController
@RequestMapping ( "/system/businessUser" )
public class BusinessUserController extends BaseController {

    // 引入企业用户服务
    @Resource
    private IUserService userService;

    @Resource
    private IUserMsgViewedExternalService userMsgViewedExternalService;

    @Resource
    private IAppletAuthorizerService appletAuthorizerService;

    @Resource
    private IWeappExternalService weappExternalService;

    @Resource
    private UserPreferencesMapper userPreferencesMapper;

    @Authority( AuthorityEnum.NOCHECK )
    @ApiOperation ( "注册" )
    @PostMapping ( "/register" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LoginInfoDto.class )
    })
    public DataResult register(@RequestBody RegisterVo vo) {
        ValidationUtils.validate(vo);
        LoginInfoDto loginInfoDto = userService.register(vo, getIpAddrPro());
        return renderSuccess(loginInfoDto);
    }

    @Authority( AuthorityEnum.NOCHECK )
    @ApiOperation ( "登录" )
    @PostMapping ( "/login" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LoginInfoDto.class )
    })
    public DataResult login(@RequestBody LoginVo vo) {
        ValidationUtils.validate(vo);
        LoginInfoDto loginInfoDto = userService.login(vo, getIpAddrPro());
        return renderSuccess(loginInfoDto);
    }

    @ApiOperation ( "退出登录" )
    @PostMapping ( "/logout" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult logout() {
        Integer userId = getUid();
        userService.logout(userId, getLoginHashKey());
        return renderSuccess(userId);
    }

    @ApiOperation ( "获取用户登录信息" )
    @PostMapping ( "/userLoginInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserLoginInfoDto.class )
    })
    public DataResult userLoginInfo() {
        Integer userId = getUid();
        UserLoginInfoDto userLoginInfoDto = userService.getUserLoginInfo(userId, getLoginHashKey());
        return renderSuccess(userLoginInfoDto);
    }

    @ApiOperation ( "获取当前用户邀请链接列表" )
    @PostMapping ( "/my/inviteUrlList" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserInviteInfo.class )
    })
    public DataResult myInviteUrlList() {
        List<UserInviteInfo> inviteUrlList = userService.getUserInviteUrlList(getUserInfoId(), getUserLoginInfoEo());
        return renderSuccess(inviteUrlList);
    }

    @ApiOperation ( "修改当前用户头像" )
    @PostMapping ( "/my/avatarUpdate" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult myAvatarUpdate(@RequestBody UpdateUserVo vo) {
        userService.myAvatarUpdate(vo.getAvatar(), getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation ( "修改当前用户密码" )
    @PostMapping ( "/my/passwordUpdate" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult myPasswordUpdate(@RequestBody ResetMyPasswordVo vo) {
        ValidationUtils.validate(vo);

        UserLoginInfoEo userLoginInfoEo = getUserLoginInfoEo();

        ResetPasswordVo resetPasswordVo = new ResetPasswordVo();
        resetPasswordVo.setUserId(userLoginInfoEo.getUserId());
        resetPasswordVo.setPassword(vo.getPassword());
        ValidationUtils.validate(resetPasswordVo);

        userService.resetPassword(resetPasswordVo, userLoginInfoEo);
        return renderSuccess();
    }

    @ApiOperation ( "切换登录机构" )
    @PostMapping ( "/switchOrg" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = LoginInfoDto.class )
    })
    public DataResult switchOrg(@RequestBody SwitchOrgVo vo) {
        ValidationUtils.validate(vo);
        LoginInfoDto loginInfoDto = userService.switchOrg(getUid(), vo.getOrgId(), getLoginTimeKey());
        return renderSuccess(loginInfoDto);
    }

    @ApiOperation( "获取用户列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserDto.class )
    })
    public DataResult list(@RequestBody QueryUserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userService.getUserList(vo, getCurrentOrgId(), getUserLoginInfoEo()));
    }

    @ApiOperation( "获取所有人员" )
    @PostMapping( "/all" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserMinPlusEo.class )
    })
    public DataResult all(@RequestBody QueryUserMinVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userService.getAllUserList(vo, getCurrentOrgId(), getCurrentOrgType()));
    }

    @ApiOperation( "获取用户信息" )
    @PostMapping( "/info/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserInfoDto.class )
    })
    public DataResult info(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "用户id不合法");
        return renderSuccess(userService.getUserInfo(id, getCurrentOrgId()));
    }

    @ApiOperation( "添加人员" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult add(@RequestBody AddUserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userService.addOrgUser(vo, getCurrentOrgId(), getUid()));
    }

    @ApiOperation( "修改人员" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult update(@RequestBody UpdateUserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userService.updateOrgUser(vo, getCurrentOrgId()));
    }

    @ApiOperation( "删除人员" )
    @PostMapping( "/del/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult del(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "人员id不合法");
        userService.delOrgUser(id, getCurrentOrgId());
        return renderSuccess();
    }

    @ApiOperation( "恢复人员" )
    @PostMapping( "/recover/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult recover(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "人员id不合法");
        userService.recoverOrgUser(id, getCurrentOrgId());
        return renderSuccess();
    }

    @ApiOperation( "获取剩余席位" )
    @PostMapping( "/getRemainingSeats" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class)
    })
    public DataResult getRemainingSeats(@RequestBody OrgInfoVo vo) {

        if((!getCurrentOrgType().equals(3)) || vo.getOrgId()==null) {
            vo.setOrgId(getCurrentOrgId());
        }

        ValidationUtils.validate(vo);
        return renderSuccess(userService.getRemainingSeats(vo.getOrgId()));
    }

    @ApiOperation ( "获取小程序邀请码" )
    @PostMapping ( "/appletsInviteCode/{userInfoId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AppletsInviteCodeDto.class )
    })
    public DataResult appletsInviteCode(@PathVariable( value = "userInfoId" ) Integer userInfoId) {
        AssertUtils.isTrue(userInfoId <= 0, "人员id不合法");
        AppletsInviteCodeDto appletsInviteCodeDto = userService.getAppletsInviteCode(userInfoId, getCurrentOrgId());
        return renderSuccess(appletsInviteCodeDto);
    }

    @ApiOperation ( "获取小程序邀请码【测试环境调用】" )
    @PostMapping ( "/appletsInviteCodeTest" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult appletsInviteCodeTest(@RequestBody CreateWeappQrcodeVo vo) {
        ValidationUtils.validate(vo);

        // 验证密码
        AssertUtils.isTrue(!vo.getPassword().equals("siwusiw97s8duwis75xhzjske185a"), "密码错误");

        Integer orgId = appletAuthorizerService.getOrgId(vo.getAppid());
        AssertUtils.isTrue(orgId==null || orgId <= 0, "该小程序未绑定");

        // 获取小程序码
        String codeUrl = weappExternalService.createMerchantWeappQrcode(
                vo.getIsForceProduce(), vo.getPage(), vo.getParameters(), orgId, vo.getTimeKey(), vo.getPx(), vo.getEnvVersion());
        return renderSuccess(codeUrl);
    }

    @ApiOperation( "重置密码" )
    @PostMapping( "/resetPassword" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult resetPassword(@RequestBody ResetPasswordVo vo) {
        ValidationUtils.validate(vo);
        userService.resetPassword(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "获取未读消息" )
    @PostMapping( "/getUserUnreadMsgCount" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserUnreadMsgEo.class )
    })
    public DataResult getUserUnreadMsgCount() {
        UserUnreadMsgEo userUnreadMsgEo = userMsgViewedExternalService.getUserUnreadMsgCount(getUserLoginInfoEo());
        return renderSuccess(userUnreadMsgEo);
    }

    @ApiOperation( "更新消息已读" )
    @PostMapping( "/updateMsgRead" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateMsgRead(@RequestBody UpdateMsgReadVo vo) {
        UserMsgCursorEo userMsgCursorEo = new UserMsgCursorEo();
        userMsgCursorEo.setLeadsCursor( vo.getLeadsFlag() ? (int)DateUtils.getCurrentTimestamp() : null );
        userMsgCursorEo.setKbsCursor( vo.getKbsFlag() ? (int)DateUtils.getCurrentTimestamp() : null );
        userMsgViewedExternalService.updateUserMsgCursor(getUserLoginInfoEo().getUserInfoId(), userMsgCursorEo);
        return renderSuccess();
    }

    @ApiOperation( "潜客表格配置-获取" )
    @PostMapping( "/leadsTableConfig/get" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult leadsTableConfigGet() {
        Map<String, Object> result = new HashMap<>(6);

        // 获取当前用户偏好
        UserPreferences currentUserPreferences = userPreferencesMapper.selectOne(Wrappers.<UserPreferences>lambdaQuery()
                .eq(UserPreferences::getUserId, getUid())
                .eq(BaseEntity::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        result.put("leadsTableConfig", currentUserPreferences.getLeadsTableConfig());

        return renderSuccess(result);
    }

    @ApiOperation( "潜客表格配置-保存" )
    @PostMapping( "/leadsTableConfig/set" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult leadsTableConfigSet(@RequestBody Map<String, Object> result) {

        // 获取当前用户偏好
        UserPreferences currentUserPreferences = userPreferencesMapper.selectOne(Wrappers.<UserPreferences>lambdaQuery()
                .eq(UserPreferences::getUserId, getUid())
                .eq(BaseEntity::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        currentUserPreferences.setLeadsTableConfig(String.valueOf(result.get("leadsTableConfig")));
        userPreferencesMapper.updateById(currentUserPreferences);

        return renderSuccess();
    }

    @ApiOperation( "潜客表格列冻结配置-获取" )
    @PostMapping( "/leadsColumnsFreezeConfig/get" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult leadsColumnsFreezeConfigGet() {
        Map<String, Object> result = new HashMap<>(6);

        // 获取当前用户偏好
        UserPreferences currentUserPreferences = userPreferencesMapper.selectOne(Wrappers.<UserPreferences>lambdaQuery()
                .eq(UserPreferences::getUserId, getUid())
                .eq(BaseEntity::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        result.put("leadsColumnsFreezeConfig", currentUserPreferences.getLeadsColumnsFreezeConfig());

        return renderSuccess(result);
    }

    @ApiOperation( "潜客表格列冻结配置-保存" )
    @PostMapping( "/leadsColumnsFreezeConfig/set" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult leadsColumnsFreezeConfigSet(@RequestBody Map<String, Object> result) {

        // 获取当前用户偏好
        UserPreferences currentUserPreferences = userPreferencesMapper.selectOne(Wrappers.<UserPreferences>lambdaQuery()
                .eq(UserPreferences::getUserId, getUid())
                .eq(BaseEntity::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        currentUserPreferences.setLeadsColumnsFreezeConfig(String.valueOf(result.get("leadsColumnsFreezeConfig")));
        userPreferencesMapper.updateById(currentUserPreferences);

        return renderSuccess();
    }

}
