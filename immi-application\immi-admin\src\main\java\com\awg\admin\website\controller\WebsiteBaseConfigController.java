package com.awg.admin.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.website.dto.BannerDto;
import com.awg.website.dto.WebsiteBaseConfigDto;
import com.awg.website.service.IWebsiteBaseConfigService;
import com.awg.website.vo.QueryBannerVo;
import com.awg.website.vo.UpdateBannerVo;
import com.awg.website.vo.UpdateBaseConfigVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@ApiSupport( order = 49 )
@Api( tags = {"官网管理-基础配置相关接口"} )
@RestController
@RequestMapping( "/website/baseConfig" )
public class WebsiteBaseConfigController extends BaseController {

    @Resource
    private IWebsiteBaseConfigService websiteBaseConfigService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取基础配置" )
    @PostMapping( "/get" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = WebsiteBaseConfigDto.class )
    })
    public DataResult getConfig() {
        return renderSuccess(websiteBaseConfigService.getBaseConfig(getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "设置基础配置" )
    @PostMapping( "/set" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult setConfig(@RequestBody UpdateBaseConfigVo vo) {
        ValidationUtils.validate(vo);
        websiteBaseConfigService.setBaseConfig(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

}
