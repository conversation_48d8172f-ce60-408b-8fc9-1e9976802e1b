package com.awg.account.service;

import com.awg.account.dto.WalletInfoDto;
import com.awg.account.dto.WalletTransactionItemDto;
import com.awg.account.entity.Wallet;
import com.awg.account.vo.AdjustBalanceVo;
import com.awg.account.vo.MemberWalletInfoVo;
import com.awg.account.vo.QueryWalletTransactionVo;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 钱包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface IWalletService extends IService<Wallet> {

    /**
     * <p>
     * 获取钱包信息(B端)
     * </p>
     *
     * @return:
     */
    WalletInfoDto getBusinessWalletInfo(String walletNo);

    /**
     * <p>
     * 获取钱包信息(C端)
     * </p>
     *
     * @return:
     */
    WalletInfoDto getConsumerWalletInfo(String walletNo);

    /**
     * <p>
     * 获取交易流水列表（B端）
     * </p>
     *
     * @return:
     */
    BasePageResult<WalletTransactionItemDto> businessWalletTransactionList(QueryWalletTransactionVo vo);


    /**
     * <p>
     * 获取交易流水列表（C端）
     * </p>
     *
     * @return:
     */
    BasePageResult<WalletTransactionItemDto> consumerWalletTransactionList(QueryWalletTransactionVo vo);

    /**
     * <p>
     * 手动调整钱包余额（B端）
     * </p>
     *
     * @return:
     */
    void adjustBusinessBalance(AdjustBalanceVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 手动调整钱包余额（C端）
     * </p>
     *
     * @return:
     */
    void adjustConsumerBalance(AdjustBalanceVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 获取会员钱包信息
     * </p>
     *
     * @return:
     */
    WalletInfoDto getMemberWalletInfo(MemberWalletInfoVo vo, MemberLoginInfoEo loginInfoEo);
}
