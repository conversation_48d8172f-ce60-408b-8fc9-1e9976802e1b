package com.awg.comm.dto;

import com.awg.comm.vo.MaterialImageVo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * <b>MaterialDto</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel(value = "材料信息")
public class MaterialDto implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "材料编号")
    private String materialNo;

    @ApiModelProperty(value = "材料清单")
    private String title;

    @ApiModelProperty(value = "库id（仅材料库中的有效）")
    private Integer libraryId;

    @ApiModelProperty(value = "材料类型（0=必须材料，1=N选M材料，2=非必须材料）")
    private Integer materialType;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "来源（0=用户填写，1=素材库素材）")
    private Integer source;

    @ApiModelProperty(value = "材料模板图文件编号列表")
    private List<MaterialImageDto> materialImageList;
}
