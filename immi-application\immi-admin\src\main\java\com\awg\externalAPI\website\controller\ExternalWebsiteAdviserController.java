package com.awg.externalAPI.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.AdviserDataDto;
import com.awg.website.dto.AdviserDto;
import com.awg.website.dto.ProjectDataDto;
import com.awg.website.dto.ProjectDto;
import com.awg.website.service.IWebsiteAdviserService;
import com.awg.website.vo.AdviserDataVo;
import com.awg.website.vo.QueryAdviserVo;
import com.awg.website.vo.QueryProjectVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@ApiSupport( order = 115 )
@Api( tags = {"官网-团队接口"} )
@RestController
@RequestMapping( "/externalAPI/website/team" )
public class ExternalWebsiteAdviserController extends BaseController {

    @Resource
    private IWebsiteAdviserService websiteAdviserService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取团队页数据" )
    @PostMapping( "/getData" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AdviserDataDto.class )
    })
    public DataResult getData(@RequestBody AdviserDataVo vo) {

        Integer orgId = vo.getOrgId();
        AssertUtils.isTrue(orgId<=0, "机构ID不能为空");
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());

        AdviserDataDto adviserDataDto = new AdviserDataDto();

        // 填入所有办公室
        adviserDataDto.setOfficeList(websiteAdviserService.getOfficeListAll(userLoginInfoEo));

        // 准备参数
        QueryAdviserVo adviserVo = new QueryAdviserVo();
        adviserVo.setOrgId(orgId);
        adviserVo.setPageNo(1);
        adviserVo.setPageSize(1000);

        // 管理团队列表
        adviserVo.setIdentityId(1);
        BasePageResult<AdviserDto> teamResult = websiteAdviserService.queryAdviserList(adviserVo, userLoginInfoEo);
        // 图片填入官网路径
        for (AdviserDto adviserDto : teamResult.getData()) {
            adviserDto.setPhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getPhotoUrl()) ));
            adviserDto.setQrcodeUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getQrcodeUrl()) ));
            adviserDto.setLicensePhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getLicensePhotoUrl()) ));
        }
        adviserDataDto.setManagerList(teamResult.getData());

        // 持牌顾问列表
        adviserVo.setIdentityId(2);
        teamResult = websiteAdviserService.queryAdviserList(adviserVo, userLoginInfoEo);
        // 图片填入官网路径
        for (AdviserDto adviserDto : teamResult.getData()) {
            adviserDto.setPhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getPhotoUrl()) ));
            adviserDto.setQrcodeUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getQrcodeUrl()) ));
            adviserDto.setLicensePhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getLicensePhotoUrl()) ));
        }
        adviserDataDto.setLicensedList(teamResult.getData());

        // 资深顾问列表
        adviserVo.setIdentityId(3);
        String officeNo = vo.getExperiencedOfficeNo();
        if(!StringUtils.isBlank(officeNo)) {
            adviserVo.setOfficeNo(officeNo);
        }
        teamResult = websiteAdviserService.queryAdviserList(adviserVo, userLoginInfoEo);
        // 图片填入官网路径
        for (AdviserDto adviserDto : teamResult.getData()) {
            adviserDto.setPhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getPhotoUrl()) ));
            adviserDto.setQrcodeUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getQrcodeUrl()) ));
            adviserDto.setLicensePhotoUrl(orgExternalService.getFileUrlByDomain( orgId, FileBaseUtil.getRelativeUrl(adviserDto.getLicensePhotoUrl()) ));
        }
        adviserDataDto.setExperiencedList(teamResult.getData());

        return renderSuccess(adviserDataDto);
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "查询团队数据-不分页" )
    @PostMapping( "/queryList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AdviserDataDto.class )
    })
    public DataResult queryList(@RequestBody QueryAdviserVo vo) {
        vo.setPageNo(1);
        vo.setPageSize(1000);
        ValidationUtils.validate(vo);
        vo.setPageSize(20000);

        AssertUtils.isTrue(vo.getOrgId()<=0, "机构ID不能为空");
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());

        return renderSuccess(websiteAdviserService.queryAdviserListAll(vo, userLoginInfoEo));
    }
}
