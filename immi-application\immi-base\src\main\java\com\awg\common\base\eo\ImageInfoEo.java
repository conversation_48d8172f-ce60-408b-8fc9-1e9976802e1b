package com.awg.common.base.eo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * <p>
 * <b>ImageInfoEo</b> is 图片信息数据对象
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "图片信息数据")
public class ImageInfoEo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 图片高度
     */
    private Integer height;
}