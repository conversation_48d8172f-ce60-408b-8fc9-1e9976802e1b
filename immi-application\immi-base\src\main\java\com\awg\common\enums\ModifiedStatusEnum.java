package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b>修改状态枚举</b>
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ModifiedStatusEnum {

    /**
     * 修改状态（0=未修改，1=已修改）
     */
    UNMODIFIED(0, "未修改"),
    MODIFIED(1, "已修改");

    private final Integer code;
    private final String label;

    /**
     * 根据code获取枚举
     *
     * @param code 修改状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ModifiedStatusEnum parse(Integer code) {
        return Arrays.stream(ModifiedStatusEnum.values())
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据label获取枚举
     *
     * @param label 修改状态名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ModifiedStatusEnum parse(String label) {
        return Arrays.stream(ModifiedStatusEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的修改状态代码
     *
     * @param code 修改状态代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValid(Integer code) {
        return parse(code) != null;
    }

    /**
     * 判断是否已修改
     *
     * @param code 修改状态代码
     * @return true表示已修改，false表示未修改
     */
    public static boolean isModified(Integer code) {
        ModifiedStatusEnum status = parse(code);
        return status != null && status == MODIFIED;
    }

    /**
     * 判断是否未修改
     *
     * @param code 修改状态代码
     * @return true表示未修改，false表示已修改
     */
    public static boolean isUnmodified(Integer code) {
        ModifiedStatusEnum status = parse(code);
        return status != null && status == UNMODIFIED;
    }

    /**
     * 获取所有修改状态代码
     *
     * @return 所有修改状态代码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(ModifiedStatusEnum.values())
                .map(ModifiedStatusEnum::getCode)
                .toArray(Integer[]::new);
    }

    /**
     * 获取所有修改状态名称
     *
     * @return 所有修改状态名称数组
     */
    public static String[] getAllLabels() {
        return Arrays.stream(ModifiedStatusEnum.values())
                .map(ModifiedStatusEnum::getLabel)
                .toArray(String[]::new);
    }
}
