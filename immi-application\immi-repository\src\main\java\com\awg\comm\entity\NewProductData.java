package com.awg.comm.entity;

import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 商品数据表 - 新版本（支持MyBatis-Plus逻辑删除）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "comm_product_data")
public class NewProductData extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 商品编号
     */
    private Long productNo;

    /**
     * 商品版本id
     */
    private Integer productVid;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 来源商品编号
     */
    private Long sourceNo;

    /**
     * 分类（1=签证类，2=申校类）
     */
    private Integer category;

    /**
     * 销售咨询类型（0=购买，1=咨询，2=无）
     */
    private Integer salesConsultationType;

    /**
     * 二级分类id
     */
    private Integer secondaryCategory;

    /**
     * 关键词id列表
     */
    private String keywordIds;

    /**
     * 购买按钮文案
     */
    private String purchaseButtonText;

    /**
     * 促销按钮文案
     */
    private String promotionButtonText;

    /**
     * 咨询按钮文案
     */
    private String consultationButtonText;

    /**
     * 咨询顾问微信
     */
    private String consultantWechat;

    /**
     * 咨询顾问二维码
     */
    private String consultantQrcode;

    /**
     * 学校logo
     */
    private String schoolLogo;

    /**
     * 商品封面
     */
    private String cover;

    /**
     * 封面宽度
     */
    private Integer coverWidth;

    /**
     * 封面高度
     */
    private Integer coverHeight;

    /**
     * 教育阶段（1=中小学，2=college，3=本科，4=研究生）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer educationalStage;

    /**
     * 地区id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer districtId;

    /**
     * 名称
     */
    private String name;

    /**
     * 价格，0表示免费
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal price;

    /**
     * 平台交付价格
     */
    private BigDecimal platformDeliveryPrice;

    /**
     * 促销价格，0表示免费，null表示不支持
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal promoPrice;

    /**
     * 第二件优惠价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal secondDiscountPrice;

    /**
     * 第三件优惠价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal thirdDiscountPrice;

    /**
     * 关联业务编号
     */
    private String relatedBusinessNo;

    /**
     * 关联子级业务编号
     */
    private String relatedChildBusinessNo;

    /**
     * 描述
     */
    private String description;

    /**
     * 优惠说明
     */
    private String discountDescription;

    /**
     * 注意事项
     */
    private String notes;

    /**
     * 费用描述
     */
    private String feeDescription;

    /**
     * 服务条款
     */
    private String termsOfService;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 多个购买优惠开关
     */
    private Integer multipleDiscountFlag;

    /**
     * 赠送优惠券开关
     */
    private Integer couponGiftFlag;

    /**
     * 来源类型，0=自建，1=平台
     */
    private Integer sourceType;

    /**
     * 交付类型，0=自己交付，1=平台交付
     */
    private Integer deliveryType;


    /**
     * 上一个编辑商品编号
     */
    private Integer lastVid;


}
