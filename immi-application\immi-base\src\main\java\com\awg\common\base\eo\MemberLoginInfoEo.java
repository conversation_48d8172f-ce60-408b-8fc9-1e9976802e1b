package com.awg.common.base.eo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>UserLoginInfoEo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "用户信息数据")
public class MemberLoginInfoEo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    private Integer memberId;

    /**
     * 会员编号
     */
    private Long memberNo;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * ip
     */
    private String ip;
}
