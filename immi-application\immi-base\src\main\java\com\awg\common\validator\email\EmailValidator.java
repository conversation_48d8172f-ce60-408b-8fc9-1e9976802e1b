package com.awg.common.validator.email;

import com.awg.utils.check.EmailCheckUtil;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <p>
 * <b>EmailValidator</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/4/14 16:33
 */

public class EmailValidator implements ConstraintValidator<ValidationEmail, String> {

    @Override
    public void initialize(ValidationEmail constraintAnnotation) {
    }

    @Override
    public boolean isValid(String email, ConstraintValidatorContext context) {
        if (StringUtils.isEmpty(email)) {
            context.buildConstraintViolationWithTemplate("邮箱不可为空").addConstraintViolation();
            return false;
        }

        return EmailCheckUtil.check(email);
    }
}
