package com.awg.admin.ord.controller;

import com.awg.client.dto.ConsumerMemberItemDto;
import com.awg.client.entity.LevelAssessmentRecord;
import com.awg.client.vo.QueryCMemberVo;
import com.awg.comm.dto.ProductInfoDto;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.ord.dto.OrderItemDto;
import com.awg.ord.dto.OrderJoinLeadsDto;
import com.awg.ord.entity.MemberCoupon;
import com.awg.ord.entity.Order;
import com.awg.ord.mapper.MemberCouponMapper;
import com.awg.ord.mapper.OrderMapper;
import com.awg.ord.service.IOrderService;
import com.awg.ord.vo.QueryOrderVo;
import com.awg.utils.date.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 320 )
@Api( tags = {"电商订单相关接口"} )
@RestController
@RequestMapping( "/ord/order" )
public class OrderController extends BaseController {

    @Resource
    private IOrderService orderService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private MemberCouponMapper memberCouponMapper;

    @ApiOperation( "订单列表" )
    @PostMapping( "/list" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderItemDto.class )
    })
    public DataResult orderList(@RequestBody QueryOrderVo vo){
        vo.setOrgId(getCurrentOrgId());
        ValidationUtils.validate(vo);
        BasePageResult<OrderItemDto> result = orderService.orderList(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "订单商品详情" )
    @PostMapping( "/productDetail/{orderNo}" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductInfoDto.class )
    })
    public DataResult productDetail(@PathVariable( value = "orderNo" ) String orderNo){
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");
        return renderSuccess(orderService.orderProductDetail(orderNo, null, getUserLoginInfoEo()));
    }

    @ApiOperation( "订单结算" )
    @PostMapping( "/orderSettle/{orderNo}" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult orderSettle(@PathVariable( value = "orderNo" ) String orderNo){
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // 必须登录
        Integer userInfoId = getUserLoginInfoEo().getUserInfoId();
        AssertUtils.isTrue(userInfoId == null || userInfoId<=0, "用户未登录");

        orderService.orderSettle(orderNo, getUserLoginInfoEo());

        return renderSuccess();
    }

    @ApiOperation( "订单作废" )
    @PostMapping( "/orderVoid/{orderNo}" )
    @ApiOperationSupport(order = 35)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult orderVoid(@PathVariable( value = "orderNo" ) String orderNo){
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // 必须登录
        Integer userInfoId = getUserLoginInfoEo().getUserInfoId();
        AssertUtils.isTrue(userInfoId == null || userInfoId<=0, "用户未登录");

        orderService.orderVoid(orderNo, getUserLoginInfoEo());

        return renderSuccess();
    }

    @ApiOperation( "订单建档" )
    @PostMapping( "/joinLeads/{orderNo}" )
    @ApiOperationSupport(order = 37)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderJoinLeadsDto.class )
    })
    public DataResult joinLeads(@PathVariable( value = "orderNo" ) String orderNo){
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // 必须登录
        Integer userInfoId = getUserLoginInfoEo().getUserInfoId();
        AssertUtils.isTrue(userInfoId == null || userInfoId<=0, "用户未登录");

        return renderSuccess(orderService.joinLeads(orderNo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "订单过期-定时任务" )
    @Authority( AuthorityEnum.NOCHECK )
    @GetMapping( value = "/orderCheck/siowi98wisujdkwo9as17782" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult orderCheck() {
        // 当前时间戳
        Integer currentTime = (int) DateUtils.getCurrentTimestamp();

        // 获取30分钟前的订单，但不超过90分钟，以防不断调用
        List<Order> list = orderMapper.selectList(Wrappers.<Order>lambdaQuery()
                .eq(Order::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .eq(Order::getStatus, 0)
                .gt(Order::getMemberId, 0)
                .le(Order::getCreatedAt, currentTime-1800)
                .ge(Order::getCreatedAt, currentTime-5400)
        );

        // 循环检测等级
        for(Order order : list) {
            orderService.orderCancel(order.getOrderNo().toString(), null, true);
        }

        return renderSuccess();
    }

    @ApiOperationSupport(order = 45)
    @ApiOperation( "优惠券过期-定时任务" )
    @Authority( AuthorityEnum.NOCHECK )
    @GetMapping( value = "/couponCheck/siowi98wisujdkwo9as17782" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult couponCheck() {
        // 当前时间戳
        Long currentTime = (long) DateUtils.getCurrentTimestamp();

        List<MemberCoupon> list = memberCouponMapper.selectList(Wrappers.<MemberCoupon>lambdaQuery()
                .eq(MemberCoupon::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .eq(MemberCoupon::getStatus, 0)
                .le(MemberCoupon::getExpiryTimestamp, currentTime)
        );

        for(MemberCoupon memberCoupon : list) {
            memberCouponMapper.update(null, Wrappers.<MemberCoupon>lambdaUpdate()
                    .eq(MemberCoupon::getCouponNo, memberCoupon.getCouponNo())
                    .eq(MemberCoupon::getStatus, 0)
                    .set(MemberCoupon::getStatus, 2)
            );
        }

        return renderSuccess();
    }
}
