package com.awg.common.base.exception;

/**
 * 错误码
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2020年3月18日
 */
public enum BaseResponseCode {

    /**
     * 返回前端统一错误码
     */

    SUCCESS(200, "请求接口正常"),

    SERVICE_MAINTAIN(4000, "服务器维护中"),

    LOGIN_AUTH(4001, "登录失效, 请重新登录"),

    USER_NOT_REGISTER(4002, "用户未注册"),

    USER_IS_EXISTS(4003, "用户已存在"),

    PASSWORD_ERROR(4004, "密码错误"),

    LOGIN_DIFF_PLACE(4004, "您的账号在别处登录，请重新登录"),

    USER_NAME_DISABLE(4005, "账号被禁用"),

    USER_NAME_ROLE_DISABLE(4006, "权限不足"),

    CODE_ERROR(5001, "服务器开小差"),

    SERVE_NETWORK_ERROR(6001, "服务网络异常"),

    RETRY(7000, "重试操作"),

    INVALID_PARAMETERS(7001, "参数异常"),

    ILLEGAL_REQUEST(7002, "非法请求"),

    INVALID_PARAMETERS_TYPE_MISMATCH(7003, "参数类型不匹配"),

    VER_ERROR(7005, "版本过旧，请尝试刷新页面进入新版本"),

    WEB_ERROR(7004, "前端参数传输有误,请核查id是否传正确");
    
    Integer code;
    String msg;

    BaseResponseCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
