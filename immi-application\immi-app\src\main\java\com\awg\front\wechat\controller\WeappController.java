package com.awg.front.wechat.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.wechat.dto.AuthorizerAppletDto;
import com.awg.wechat.service.IAppletAuthorizerService;
import com.awg.wechat.service.IWeappService;
import com.awg.wechat.vo.WeappLoginVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 50 )
@Api( tags = {"微信小程序-相关接口"} )
@RestController
@RequestMapping( "/wechat/weapp" )
@Slf4j
public class WeappController extends BaseController {

    @Resource
    private IWeappService weappService;

    @Resource
    private IAppletAuthorizerService appletAuthorizerService;

    @ApiOperation( "微信登录-小程序" )
    @PostMapping( "/login" )
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult login(@RequestBody WeappLoginVo vo) {
        ValidationUtils.validate(vo);

        Integer orgId = appletAuthorizerService.getOrgId(vo.getAppid());
        AssertUtils.isTrue(orgId==null || orgId <= 0, "该小程序未绑定");

        return renderSuccess(weappService.login(vo, orgId));
    }

    @ApiOperation( "添加临时邀请记录" )
    @PostMapping( "/addTempInviteRecord" )
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult addTempInviteRecord(@RequestBody WeappLoginVo vo) {
        ValidationUtils.validate(vo);

        Integer orgId = appletAuthorizerService.getOrgId(vo.getAppid());
        AssertUtils.isTrue(orgId==null || orgId <= 0, "该小程序未绑定");

        weappService.addTempInviteRecord(vo, orgId);
        return renderSuccess();
    }

    @ApiOperation ( "获取公司id" )
    @GetMapping( "/getOrgId/{appid}" )
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult getAuthorizerAppletInfo(@PathVariable ( value = "appid" ) String appid) {
        return renderSuccess(appletAuthorizerService.getOrgId(appid));
    }
}
