package com.awg.admin.system.controller;

import com.awg.common.base.page.BasePageResult;
import com.awg.system.dto.OrgInfoDto;
import com.awg.system.dto.OrgSkinDto;
import com.awg.system.dto.RoleDto;
import com.awg.system.entity.Org;
import com.awg.system.entity.ServicePlans;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.system.service.IOrgService;
import com.awg.system.service.IUserOrgService;
import com.awg.system.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 20 )
@Api( tags = {"组织机构相关接口"} )
@RestController
@RequestMapping( "/system/org" )
public class OrgController extends BaseController {
    @Resource
    private IUserOrgService userOrgService;

    @Resource
    private IOrgService orgService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperation( "获取机构列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrgInfoDto.class )
    })
    public DataResult list(@RequestBody QueryOrgVo vo) {
        ValidationUtils.validate(vo);

        BasePageResult<OrgInfoDto> result = orgService.getOrgList(vo);
        return renderSuccess(result);
    }

    @ApiOperation( "创建机构" )
    @PostMapping( "/create" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Org.class)
    })
    public DataResult createOrg(@RequestBody CreateOrgVo vo) {
        ValidationUtils.validate(vo);
        Org org = userOrgService.createOrg(vo, getUid(), getIpAddrPro(), getUserInfoId());
        return renderSuccess(org);
    }

    @ApiOperation( "获取所有机构" )
    @PostMapping( "/all" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Org.class)
    })
    public DataResult orgAllList() {
        return renderSuccess(orgService.getOrgAll());
    }

    @ApiOperation( "获取所有服务套餐" )
    @PostMapping( "/servicePlansAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ServicePlans.class)
    })
    public DataResult getServicePlansListAll() {
        return renderSuccess(orgExternalService.getServicePlansListAll());
    }

    @ApiOperation( "更新机构" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Org.class)
    })
    public DataResult updateOrg(@RequestBody UpdateOrgVo vo) {
        ValidationUtils.validate(vo);
        Org org = userOrgService.updateOrg(vo, getUid(), getUserInfoId(), getIpAddrPro(), getCurrentOrgId(), getCurrentOrgType());
        return renderSuccess(org);
    }

    @ApiOperation( "获取机构信息" )
    @PostMapping( "/info" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Org.class)
    })
    public DataResult info(@RequestBody OrgInfoVo vo) {

        if((!getCurrentOrgType().equals(3)) || vo.getOrgId()==null) {
            vo.setOrgId(getCurrentOrgId());
        }

        ValidationUtils.validate(vo);
        OrgInfoDto orgInfo = orgService.getOrgInfo(vo);
        return renderSuccess(orgInfo);
    }

    @ApiOperation( "获取机构皮肤" )
    @PostMapping( "/getSkin" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrgSkinDto.class)
    })
    public DataResult getSkin(@RequestBody(required = false) OrgSkinVo orgSkinVo) {

        OrgSkinVo vo = new OrgSkinVo();
        if(orgSkinVo!=null) {
            BeanUtils.copyProperties( orgSkinVo, vo );
        }

        if((!getCurrentOrgType().equals(3)) || vo.getOrgId()==null) {
            vo.setOrgId(getCurrentOrgId());
        }

        ValidationUtils.validate(vo);
        return renderSuccess(orgExternalService.getSkin(vo));
    }

    @ApiOperation( "设置机构皮肤" )
    @PostMapping( "/setSkin" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrgSkinDto.class)
    })
    public DataResult setSkin(@RequestBody OrgSkinVo vo) {

        if((!getCurrentOrgType().equals(3)) || vo.getOrgId()==null) {
            vo.setOrgId(getCurrentOrgId());
        }

        ValidationUtils.validate(vo);
        return renderSuccess(orgExternalService.setSkin(vo));
    }
}
