package com.awg.common.base.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2020/11/20 18:12
 * @version: V1.0
 **/
@Data
public class SortVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id", required = true)
    @NotBlank(message = "业务id不可为空")
    private String id;

    /**
     * 排序编号
     */
    @ApiModelProperty(value = "排序索引值", required = true)
    @NotNull(message = "排序索引不可为空")
    private Integer sort;

}
