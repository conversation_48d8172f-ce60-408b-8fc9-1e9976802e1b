package com.awg.admin.system.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.result.DataResult;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.externalService.ILeadsExternalService;
import com.awg.system.dto.ChannelUserDto;
import com.awg.system.dto.SaveUserNoticeVo;
import com.awg.system.dto.UserNoticeDto;
import com.awg.system.service.IUserNoticeService;
import com.awg.system.vo.AddRoleVo;
import com.awg.system.vo.QueryChannelUserVo;
import com.awg.system.vo.UserNoticeVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 12 )
@Api( tags = {"用户通知相关接口"} )
@RestController
@RequestMapping( "/system/userNotice" )
public class UserNoticeController extends BaseController {

    @Resource
    private IUserNoticeService userNoticeService;

    @Resource
    private ILeadsExternalService leadsExternalService;

    @ApiOperation( "用户通知列表" )
    @ApiOperationSupport(order = 10)
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserNoticeDto.class )
    })
    public DataResult list() {
        UserLoginInfoEo userLoginInfoEo = getUserLoginInfoEo();
        Map<String, Object> result = new HashMap<>(6);

        result.put("newLeadsNoticeList", userNoticeService.getUserNoticeListByOrgId(userLoginInfoEo.getOrgId()));
        result.put("leadStatusNoticeList", leadsExternalService.getFollowUpNoticeConfigListAll(userLoginInfoEo));
        return renderSuccess(result);
    }

    @ApiOperation( "保存用户通知" )
    @ApiOperationSupport(order = 20)
    @PostMapping( "/save" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult save(@RequestBody SaveUserNoticeVo vo) {
        ValidationUtils.validate(vo);

        userNoticeService.saveUserNotice(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

}
