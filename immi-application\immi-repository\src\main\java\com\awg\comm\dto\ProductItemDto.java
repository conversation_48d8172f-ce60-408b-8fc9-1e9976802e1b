package com.awg.comm.dto;

import com.awg.comm.entity.ProductKeyword;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * <b>ProductItemDto</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel(value = "商品信息")
public class ProductItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品id")
    private Integer productId;

    @ApiModelProperty(value = "商品编号")
    private String productNo;

    @ApiModelProperty(value = "商品版本id")
    private Integer productVid;

    @ApiModelProperty(value = "分类（1=签证类，2=申校类）")
    private Integer category;

    @ApiModelProperty(value = "销售咨询类型（0=购买，1=咨询，2=无）")
    private Integer salesConsultationType;

    @ApiModelProperty(value = "购买按钮文案")
    private String purchaseButtonText;

    @ApiModelProperty(value = "促销按钮文案")
    private String promotionButtonText;

    @ApiModelProperty(value = "咨询按钮文案")
    private String consultationButtonText;

    @ApiModelProperty(value = "咨询顾问微信")
    private String consultantWechat;

    @ApiModelProperty(value = "咨询顾问二维码")
    private String consultantQrcode;

    @ApiModelProperty(value = "学校logo")
    private String schoolLogo;

    @ApiModelProperty(value = "商品封面")
    private String cover;

    @ApiModelProperty(value = "二级分类id")
    private Integer secondaryCategory;

    @ApiModelProperty(value = "二级分类名称")
    private String secondaryCategoryName;

    @ApiModelProperty(value = "教育阶段（1=中小学，2=college，3=本科，4=研究生）")
    private Integer educationalStage;

    @ApiModelProperty(value = "地区id")
    private Integer districtId;

    @ApiModelProperty(value = "地区名称")
    private String districtName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "来源商品编号")
    private String sourceNo;

    @ApiModelProperty(value = "平台交付价格，0表示免费")
    private BigDecimal platformDeliveryPrice;

    @ApiModelProperty(value = "来源类型，0=自建，1=平台")
    private Integer sourceType;

    @ApiModelProperty(value = "交付类型，0=自己交付，1=平台交付")
    private Integer deliveryType;

    @ApiModelProperty(value = "关联项目名称")
    private String relatedBusinessName;

    @ApiModelProperty(value = "关联项目编号")
    private String relatedBusinessNo;

    @ApiModelProperty(value = "关联子级项目编号")
    private String relatedChildBusinessNo;

    @ApiModelProperty(value = "关联子级项目名称")
    private String relatedChildBusinessName;

    @ApiModelProperty(value = "关键词id列表")
    private String keywordIds;

    @ApiModelProperty(value = "关键词列表")
    private List<ProductKeyword> keywordList;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "促销价格")
    private BigDecimal promoPrice;

    @ApiModelProperty(value = "销售量")
    private Integer salesVolume;

    @ApiModelProperty(value = "销售额")
    private BigDecimal salesAmount;

    @ApiModelProperty(value = "可用状态(0=未上架，1=已上架)")
    private Integer availabilityStatus;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "库存开关")
    private Integer stockOpenFlag;

    @ApiModelProperty(value = "促销开关")
    private Integer promoOpenFlag;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "促销库存")
    private String promoStock;

    @ApiModelProperty(value = "赠送优惠券数量")
    private Integer giftCouponNum;

    @ApiModelProperty(value = "编辑按钮显示")
    private Integer editButtonFlag;

    @ApiModelProperty(value = "同步按钮状态（0=显示，1=不显示，2=更新同步到B端）")
    private Integer isSync;

    @ApiModelProperty(value = "B端是否修改过此商品（0=否，1=是）")
    private Integer isModified;

    @ApiModelProperty(value = "B端商品更新按钮显示控制（0=不显示，1=显示）")
    private Integer showUpdateBtn;



}