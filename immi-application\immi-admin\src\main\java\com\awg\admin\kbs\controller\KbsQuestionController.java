package com.awg.admin.kbs.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.kbs.dto.QuestionDto;
import com.awg.kbs.dto.QuestionListDto;
import com.awg.kbs.entity.QuestionTagDto;
import com.awg.kbs.service.IKbsQuestionService;
import com.awg.kbs.vo.*;
import com.awg.system.eo.UserMsgCursorEo;
import com.awg.system.externalService.IUserMsgViewedExternalService;
import com.awg.utils.date.DateUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@ApiSupport( order = 47 )
@Api( tags = {"知识库-问题相关接口"} )
@RestController
@RequestMapping( "/kbs/question" )
public class KbsQuestionController extends BaseController {

    @Resource
    private IKbsQuestionService kbsQuestionService;

    @Resource
    private IUserMsgViewedExternalService userMsgViewedExternalService;

    @ApiOperationSupport(order = 3)
    @ApiOperation( "获取问题标签列表" )
    @PostMapping( "/tagListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionTagDto.class )
    })
    public DataResult tagListAll() {
        return renderSuccess(kbsQuestionService.getTagListAll());
    }

    @ApiOperationSupport(order = 5)
    @ApiOperation( "更新问题标签列表" )
    @PostMapping( "/updateTagList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateTagList(@RequestBody UpdateQuestionTagListVo vo) {

        ValidationUtils.validate(vo);

        // 迭代校验列表
        vo.getQuestionTagList().forEach( item -> {
            ValidationUtils.validate(item);
        });

        kbsQuestionService.updateQuestionTagList(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 7)
    @ApiOperation( "获取问题列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionListDto.class )
    })
    public DataResult getQuestion(@RequestBody QueryQuestionVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<QuestionListDto> result = kbsQuestionService.getQuestionList(vo);

        // 通过该接口调用时，刷新消息已读【简单处理，没有关键词时且为第一页时便更新】
        if( StringUtils.isBlank(vo.getKeyword()) && !vo.getPageNo().equals(1) ) {
            UserMsgCursorEo userMsgCursorEo = new UserMsgCursorEo();
            userMsgCursorEo.setLeadsCursor( (int) DateUtils.getCurrentTimestamp() );
            userMsgCursorEo.setKbsCursor(null);
            userMsgViewedExternalService.updateUserMsgCursor(getUserLoginInfoEo().getUserInfoId(), userMsgCursorEo);
        }

        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 8)
    @ApiOperation( "获取提问详情" )
    @PostMapping( "/detail/{questionNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionDto.class )
    })
    public DataResult detail(@PathVariable( value = "questionNo" ) String questionNo) {
        AssertUtils.isTrue(  Long.parseLong(questionNo) <= 0, "No不合法");
        return renderSuccess(kbsQuestionService.detail(questionNo));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "提交问题" )
    @PostMapping( "/submit" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult submit(@RequestBody QuestionSubmitVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsQuestionService.submitQuestion(vo, getUserLoginInfoEo(), getIpAddrPro()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑问题" )
    @PostMapping( "/upd" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult upd(@RequestBody QuestionSubmitVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsQuestionService.updateQuestion(vo, getUserLoginInfoEo(), getIpAddrPro()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "问题排序" )
    @PostMapping( "/sort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sort(@RequestBody SortQuestionVo vo) {
        ValidationUtils.validate(vo);
        kbsQuestionService.sortQuestion(vo);
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除问题" )
    @PostMapping( "/del/{questionNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult del(@PathVariable( value = "questionNo" ) String questionNo) {
        AssertUtils.isTrue(  Long.parseLong(questionNo ) <= 0, "No不合法");
        return renderSuccess(kbsQuestionService.delQuestion(questionNo, getUserLoginInfoEo()));
    }

}
