package com.awg.common.config.knife4j;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * @description:
 * @author: yang<PERSON>ang
 * @date: 2020-10-25 11:40
 * @version: V1.0
 **/
@Configuration
@EnableSwagger2WebMvc
public class Knife4jConfiguration {


    @Bean(value = "groupAwgDanceRestApi")
    public Docket groupRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .groupName("移民商机")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.awg"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo groupApiInfo() {
        return new ApiInfoBuilder()
                .title("app-consumer-APIs")
                .description("移民商机")
                .contact(new Contact("lun","","<EMAIL>"))
                .version("1.0")
                .build();
    }
}