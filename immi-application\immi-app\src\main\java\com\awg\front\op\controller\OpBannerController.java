package com.awg.front.op.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.op.dto.BannerDto;
import com.awg.op.service.IOpBannerService;
import com.awg.op.vo.QueryBannerVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 75 )
@Api( tags = {"banner-相关接口"} )
@RestController
@RequestMapping( "/op/banner" )
@Slf4j
public class OpBannerController extends BaseController {

    @Resource
    private IOpBannerService opBannerService;

    @ApiOperation( "banner列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BannerDto.class )
    })
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult list(@RequestBody QueryBannerVo vo) {
        AssertUtils.isTrue(vo.getOrgId()==null || vo.getOrgId()<=0, "公司id不能为空");
        ValidationUtils.validate(vo);
        vo.setDisplayFlag(1);
        BasePageResult<BannerDto> result = opBannerService.bannerList(vo);
        return renderSuccess(result);
    }
}
