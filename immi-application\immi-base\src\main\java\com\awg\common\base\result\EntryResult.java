package com.awg.common.base.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>EntryResult</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/11/19 16:46
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EntryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    private Object key;

    private String value;
}
