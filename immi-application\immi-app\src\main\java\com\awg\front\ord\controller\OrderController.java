package com.awg.front.ord.controller;

import com.awg.client.dto.MyInviteRecordDto;
import com.awg.client.vo.MyInviteRecordVo;
import com.awg.comm.dto.ProductInfoDto;
import com.awg.comm.dto.ProductInfoMiniDto;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.ord.dto.OrderCompleteInfoDto;
import com.awg.ord.dto.OrderItemDto;
import com.awg.ord.eo.MemberCouponEo;
import com.awg.ord.eo.OrderDetailEo;
import com.awg.ord.eo.OrderSubmitEo;
import com.awg.ord.externalService.ICouponExternalService;
import com.awg.ord.service.IOrderService;
import com.awg.ord.vo.OrderCreateVo;
import com.awg.ord.vo.QueryOrderVo;
import com.awg.system.eo.UserInfoEo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 80 )
@Api( tags = {"订单-相关接口"} )
@RestController
@RequestMapping( "/ord/order" )
@Slf4j
public class OrderController extends BaseController {

    @Resource
    private IOrderService orderService;

    @Resource
    private ICouponExternalService couponExternalService;


    @ApiOperation( "提交订单" )
    @PostMapping( "/orderSubmit" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderSubmitEo.class )
    })
    public DataResult orderSubmit(@RequestBody OrderCreateVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(orderService.orderSubmit(vo, getMemberLoginInfoEo()));
    }

    @ApiOperation( "订单取消" )
    @PostMapping( "/orderCancel/{orderNo}" )
    @ApiOperationSupport(order = 15)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult orderCancel(@PathVariable( value = "orderNo" ) String orderNo) {

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        orderService.orderCancel(orderNo, getMemberLoginInfoEo(), false);
        return renderSuccess();
    }

    @ApiOperation( "订单详情" )
    @PostMapping( "/detail/{orderNo}" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderDetailEo.class )
    })
    public DataResult detail(@PathVariable( value = "orderNo" ) String orderNo) {
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        return renderSuccess(orderService.orderDetail(orderNo, getMemberLoginInfoEo()));
    }

    @ApiOperation( "获取订单分配的文案人员信息" )
    @PostMapping( "/getCopyWriterInfo/{orderNo}" )
    @ApiOperationSupport(order = 23)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = UserInfoEo.class )
    })
    public DataResult getCopyWriterInfo(@PathVariable( value = "orderNo" ) String orderNo) {
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        return renderSuccess(orderService.getCopyWriterInfo(orderNo, getMemberLoginInfoEo()));
    }

    @ApiOperation( "订单商品详情" )
    @PostMapping( "/productDetail/{orderNo}" )
    @ApiOperationSupport(order = 25)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProductInfoMiniDto.class )
    })
    public DataResult productDetail(@PathVariable( value = "orderNo" ) String orderNo){
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        return renderSuccess(
                orderService.orderProductDetailMini(orderNo, getMemberLoginInfoEo())
        );
    }

    @ApiOperation( "订单完成信息" )
    @PostMapping( "/orderCompleteInfo/{orderNo}" )
    @ApiOperationSupport(order = 26)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderCompleteInfoDto.class )
    })
    public DataResult orderCompleteInfo(@PathVariable( value = "orderNo" ) String orderNo){
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        return renderSuccess(
                orderService.orderCompleteInfo(orderNo, getMemberLoginInfoEo())
        );
    }

    @ApiOperation( "获取适用的优惠券" )
    @PostMapping( "/getApplicableCoupon/{productNo}" )
    @ApiOperationSupport(order = 28)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberCouponEo.class )
    })
    public DataResult getApplicableCoupon(@PathVariable( value = "productNo" ) String productNo){
        AssertUtils.isTrue(StringUtils.isBlank(productNo), "商品编号不能为空");

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");

        return renderSuccess(
                couponExternalService.getMemberApplicableCoupon(productNo, memberId)
        );
    }

    @ApiOperation( "交易记录（后面上线成型后应该优化这个接口）" )
    @PostMapping( "/myOrderList" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderItemDto.class )
    })
    public DataResult myOrderList(@RequestBody QueryOrderVo vo){

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");
        vo.setMemberId(memberId);
        vo.setSortMode(1);  // 支付时间倒序

        // 状态筛选
        List<Integer> statusList = new ArrayList<>();
        statusList.add(1);
        statusList.add(2);
        vo.setStatusList(statusList);
        vo.setOrgId(getCurrentMemberOrgId());

        ValidationUtils.validate(vo);
        BasePageResult<OrderItemDto> result = orderService.orderList(vo, null);

        // 循环处理结果，交易金额 = 钱包抵扣 + 应付金额，然后取负数
        result.getData().forEach(item -> {
            item.setTransactionAmount(
                    item.getAmountDue().add(item.getWalletDeduction()).negate().setScale(2, BigDecimal.ROUND_HALF_UP)
            );
        });

        return renderSuccess(result);
    }

    @ApiOperation( "我的已购" )
    @PostMapping( "/myProductList" )
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrderItemDto.class )
    })
    public DataResult myProductList(@RequestBody QueryOrderVo vo){

        Integer memberId = getCurrentMemberId();
        AssertUtils.isTrue(memberId==null || memberId<=0, "用户未登录");
        vo.setMemberId(memberId);

        // 状态筛选成已完成和已结算
        List<Integer> statusList = new ArrayList<>();
        statusList.add(1);
        statusList.add(2);
        vo.setStatusList(statusList);
        vo.setOrgId(getCurrentMemberOrgId());

        ValidationUtils.validate(vo);
        BasePageResult<OrderItemDto> result = orderService.orderList(vo, null);

        return renderSuccess(result);
    }



}
