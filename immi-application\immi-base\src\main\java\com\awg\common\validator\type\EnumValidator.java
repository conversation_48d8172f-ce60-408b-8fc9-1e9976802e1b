package com.awg.common.validator.type;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <p>
 * <b>EnumValidator</b> is
 * </p>
 *
 * @description: 枚举类型效验逻辑
 * <AUTHOR>
 * @date 2021/3/29 10:03
 */

public class EnumValidator implements ConstraintValidator<ValidationEnum, Object> {

    private ValidationEnum annotation;

    @Override
    public void initialize(ValidationEnum constraintAnnotation) {
        this.annotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        try {
            //为空返回false
            if (obj == null) {
                //在有多个枚举参数效验的情况下, 直接提示类型不可为空无法准确定位参数字段，如果空参需要进行不同的提示，请单独使用@NotNull注解进行效验
                //context.buildConstraintViolationWithTemplate("类型不可为空").addConstraintViolation();
                return false;
            }

            //获取枚举values
            Object[] objects = annotation.clazz().getEnumConstants();

            //获取需要效验的枚举属性
            Method method = annotation.clazz().getMethod(annotation.method());
            for (Object o : objects) {
                if (Objects.equals(obj, method.invoke(o))) {
                    return true;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return false;
    }
}
