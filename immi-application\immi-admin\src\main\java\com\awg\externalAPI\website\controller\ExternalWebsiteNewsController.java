package com.awg.externalAPI.website.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.NewsDataDto;
import com.awg.website.dto.NewsDto;
import com.awg.website.dto.ProjectDataDto;
import com.awg.website.dto.ProjectDto;
import com.awg.website.service.IWebsiteNewsService;
import com.awg.website.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@ApiSupport( order = 80 )
@Api( tags = {"官网-新闻接口"} )
@RestController
@RequestMapping( "/externalAPI/website/news" )
public class ExternalWebsiteNewsController extends BaseController {

    @Resource
    private IWebsiteNewsService websiteNewsService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperationSupport(order = 5)
    @ApiOperation( "获取新闻标签列表" )
    @PostMapping( "/newsTagListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult newsTagListAll(@RequestBody OfficialBaseVo vo) {
        ValidationUtils.validate(vo);
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());
        return renderSuccess(websiteNewsService.getNewsTagListAll(userLoginInfoEo));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取新闻页数据" )
    @PostMapping( "/getData" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = NewsDataDto.class )
    })
    public DataResult getData(@RequestBody QueryNewsVo vo) {

        NewsDataDto newsDataDto = new NewsDataDto();
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());

        BasePageResult<NewsDto> newsResult = websiteNewsService.queryNewsList(vo, userLoginInfoEo,1);

        // 循环填入完整路径
        for (NewsDto newsDto : newsResult.getData()) {
            newsDto.setCoverImageUrl(orgExternalService.getFileUrlByDomain(vo.getOrgId(), newsDto.getCoverImagePath()));
        }

        newsDataDto.setNewsData(newsResult);

        return DataResult.success(newsDataDto);
    }

    @ApiOperationSupport(order = 15)
    @ApiOperation( "获取新闻详情" )
    @PostMapping( "/detail" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProjectDto.class )
    })
    public DataResult getNewsDetail(@RequestBody NewsDetailVo vo) {
        ValidationUtils.validate(vo);
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());
        return renderSuccess(websiteNewsService.getNewsDetail(vo.getNewsNo(), userLoginInfoEo));
    }

    @ApiOperationSupport(order = 15)
    @ApiOperation( "获取新闻推荐列表" )
    @PostMapping( "/recommendList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = NewsDto.class )
    })
    public DataResult recommendList(@RequestBody NewsRecommendVo vo) {

        QueryNewsVo queryNewsVo = new QueryNewsVo();
        queryNewsVo.setOrgId(vo.getOrgId());
        queryNewsVo.setPageNo(1);
        queryNewsVo.setPageSize(20);

        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());

        BasePageResult<NewsDto> newsResult = null;
        if(vo.getType()==1) {
            // 最新的
            newsResult = websiteNewsService.queryNewsList(queryNewsVo, userLoginInfoEo, 2);
        }
        else {
            // 热门的
            queryNewsVo.setHotFlag(TrueFalseEnum.TRUE.getCode());
            newsResult = websiteNewsService.queryNewsList(queryNewsVo, userLoginInfoEo, 2);
        }

        return DataResult.success(newsResult);
    }
}
