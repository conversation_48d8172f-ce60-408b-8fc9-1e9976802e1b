package com.awg.account.enums;

import com.awg.qs.enums.QuestionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b> WalletActionCategoryEnum </b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-05
 */

@Getter
@AllArgsConstructor
public enum WalletActionCategoryEnum {

    // 操作类目（1=充值，2=消费，3=金额到账，4=返佣，5=手动操作,6=退还，7=系统操作）
    RECHARGE(1, "消费"),
    CONSUME(2, "消费"),
    AMOUNT_ARRIVAL(3, "金额到账"),
    COMMISSION(4, "返佣"),
    MANUAL_OPERATION(5, "手动操作"),
    REFUND(6, "退还"),
    SYSTEM_OPERATION(7, "系统操作")
    ;

    private final Integer code;
    private final String label;

    public static WalletActionCategoryEnum parse(Integer code) {
        return Arrays.stream( WalletActionCategoryEnum.values() )
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    public static WalletActionCategoryEnum parse(String label) {
        return Arrays.stream(WalletActionCategoryEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }
}
