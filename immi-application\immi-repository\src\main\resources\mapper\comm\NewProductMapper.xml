<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.awg.comm.mapper.NewProductMapper">

    


    <select id="getPlatformSyncProducts" resultType="com.awg.comm.dto.PlatformSyncProductDto">
        SELECT DISTINCT
            p.product_no AS productNo,
            p.is_modified AS isModified,
            p.org_id AS orgId,
            pd.source_no AS sourceNo,
            pd.sales_consultation_type AS salesConsultationType,
            pd.consultation_button_text AS consultationButtonText,
            pd.consultant_wechat AS consultantWechat,
            pd.consultant_qrcode AS consultantQrcode,
            p.product_vid AS productVid,
            pd.name AS name,
            p.sync_source AS syncSource
        FROM comm_product p
        INNER JOIN comm_product_data pd ON p.product_no = pd.product_no AND p.product_vid = pd.product_vid
        WHERE p.is_delete = 0
          AND pd.is_delete = 0
          AND p.sync_source = 1
          AND pd.source_no = #{platformProductNo}
          AND p.org_id != 1
    </select>

    <update id="updatePlatformSyncVid">
        UPDATE comm_product
        SET platform_sync_vid = #{platformSyncVid}
        WHERE product_no = #{productNo}
    </update>
    
</mapper>
