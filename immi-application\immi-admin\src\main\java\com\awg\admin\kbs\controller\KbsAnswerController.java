package com.awg.admin.kbs.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.kbs.dto.AnswerDto;
import com.awg.kbs.service.IKbsAnswerService;
import com.awg.kbs.vo.AnswerSubmitVo;
import com.awg.kbs.vo.QueryAnswerVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@ApiSupport( order = 48 )
@Api( tags = {"知识库-回答相关接口"} )
@RestController
@RequestMapping( "/kbs/answer" )
public class KbsAnswerController extends BaseController {

    @Resource
    private IKbsAnswerService kbsAnswerService;

    @ApiOperationSupport(order = 3)
    @ApiOperation( "获取回答列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AnswerDto.class )
    })
    public DataResult getAnswerList(@RequestBody QueryAnswerVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<AnswerDto> result = kbsAnswerService.getAnswerList(vo, getUserLoginInfoEo());
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 5)
    @ApiOperation( "获取回答详情" )
    @PostMapping( "/detail/{answerNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AnswerDto.class )
    })
    public DataResult detail(@PathVariable( value = "answerNo" ) String answerNo) {
        AssertUtils.isTrue(  Long.parseLong(answerNo ) <= 0, "No不合法");
        return renderSuccess(kbsAnswerService.getAnswer(answerNo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "提交回答" )
    @PostMapping( "/submit" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult submit(@RequestBody AnswerSubmitVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsAnswerService.answerSubmit(vo, getUserLoginInfoEo(), getIpAddrPro()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑回答" )
    @PostMapping( "/upd" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult update(@RequestBody AnswerSubmitVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsAnswerService.answerUpd(vo, getUserLoginInfoEo(), getIpAddrPro()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "点赞回答" )
    @PostMapping( "/upvote/{answerDetailNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult upvote(@PathVariable( value = "answerDetailNo" ) String answerDetailNo) {
        AssertUtils.isTrue(  Long.parseLong(answerDetailNo ) <= 0, "No不合法");
        kbsAnswerService.upvote(answerDetailNo, getUserLoginInfoEo(), getIpAddrPro());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除回答" )
    @PostMapping( "/del/{answerNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult del(@PathVariable( value = "answerNo" ) String answerNo) {
        AssertUtils.isTrue(  Long.parseLong(answerNo ) <= 0, "No不合法");
        kbsAnswerService.answerDel(answerNo, getUserLoginInfoEo());
        return renderSuccess();
    }

}
