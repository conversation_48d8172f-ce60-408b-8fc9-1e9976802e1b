/**
 * ValidationUtils.java 2018年11月13日
 */
package com.awg.common.validator;


import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Path;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * <p>
 * <b>VoValidationUtils</b> is
 * </p>
 * * 接口入参数据校验工具类.<br/>
 * 使用hibernate-validator进行校验.<br/>
 *
 * <AUTHOR>
 * @Null 被注释的元素必须为 null
 * @NotNull 被注释的元素必须不为 null
 * @AssertTrue 被注释的元素必须为 true
 * @AssertFalse 被注释的元素必须为 false
 * @Min(value) 被注释的元素必须是一个数字，其值必须大于等于指定的最小值
 * @Max(value) 被注释的元素必须是一个数字，其值必须小于等于指定的最大值
 * @DecimalMin(value) 被注释的元素必须是一个数字，其值必须大于等于指定的最小值
 * @DecimalMax(value) 被注释的元素必须是一个数字，其值必须小于等于指定的最大值
 * @Size(max=, min=)   被注释的元素的大小必须在指定的范围内
 * @Digits (integer, fraction)     被注释的元素必须是一个数字，其值必须在可接受的范围内
 * @Past 被注释的元素必须是一个过去的日期
 * @Future 被注释的元素必须是一个将来的日期
 * @Pattern(regex=,flag=) 被注释的元素必须符合指定的正则表达式
 * Hibernate Validator 附加的 constraint
 * @NotBlank(message =)   验证字符串非null，且长度必须大于0
 * @Email 被注释的元素必须是电子邮箱地址
 * @Length(min=,max=) 被注释的字符串的大小必须在指定的范围内
 * @NotEmpty 被注释的字符串的必须非空
 * @Range(min=,max=,message=) 被注释的元素必须在合适的范围内
 * @since 2019年6月28日下午2:57:58
 */
public class ValidationUtils {

    private ValidationUtils() {
    }

    /**
     * 使用hibernate的注解来进行验证
     */
    private static final Validator VALIDATOR = Validation
            .byProvider(HibernateValidator.class)
            .configure()
            .failFast(true)
            .buildValidatorFactory()
            .getValidator();

    /**
     * 功能描述: <br>
     * 〈注解验证参数〉
     *
     * @param obj: obj
     */
    public static <T> void validate(T obj) {
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(obj);
        // 抛出检验异常
        if (constraintViolations.size() > 0) {
            //错误描述
            String errorMsg = constraintViolations.iterator().next().getMessage();
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), errorMsg);
        }
    }





    /**
     * 功能描述: <br>
     * 〈注解验证参数〉
     * 分组校验
     *
     * @param obj: obj
     */
    public static <T> void validate(T obj, Class<?>... groups) {
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(obj, groups);
        // 抛出检验异常
        if (constraintViolations.size() > 0) {
            //错误描述
            String errorMsg = constraintViolations.iterator().next().getMessage();
            //字段名称
            Path path = constraintViolations.iterator().next().getPropertyPath();
            String msg = errorMsg + " [字段：" + path + "] ";
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), msg);
        }
    }
}
