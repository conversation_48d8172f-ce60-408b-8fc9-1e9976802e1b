package com.awg.admin.client.controller;

import com.awg.client.dto.ConsumerMemberItemDto;
import com.awg.client.dto.InviteMemberRewardInfoDto;
import com.awg.client.service.IConsumerIdentityService;
import com.awg.client.vo.QueryCMemberVo;
import com.awg.client.vo.QueryInviteMemberRewardVo;
import com.awg.client.vo.UpdateConsumerMemberVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.ord.eo.MemberCouponEo;
import com.awg.ord.externalService.ICouponExternalService;
import com.awg.ord.vo.QueryMemberCouponVo;
import com.awg.ord.vo.SendTargetedCouponsVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 200 )
@Api( tags = {"C端用户相关接口"} )
@RestController
@RequestMapping( "/client/consumer" )
public class ConsumerIdentityController extends BaseController {

    @Resource
    private IConsumerIdentityService consumerIdentityService;

    @Resource
    private ICouponExternalService couponExternalService;

    @ApiOperation( "C端用户列表" )
    @PostMapping( "/list" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ConsumerMemberItemDto.class )
    })
    public DataResult getConsumerMemberList(@RequestBody QueryCMemberVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<ConsumerMemberItemDto> result = consumerIdentityService.getConsumerMemberList(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "会员优惠券列表" )
    @PostMapping( "/memberCouponList" )
    @ApiOperationSupport(order = 15)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberCouponEo.class )
    })
    public DataResult memberCouponList(@RequestBody QueryMemberCouponVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<MemberCouponEo> result = couponExternalService.memberCouponList(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "C端用户信息" )
    @PostMapping( "/info/{cid}" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ConsumerMemberItemDto.class )
    })
    public DataResult getConsumerMemberInfo(@PathVariable( value = "cid" ) Integer cid){
        AssertUtils.isTrue(cid <= 0, "cid不合法");
        ConsumerMemberItemDto result = consumerIdentityService.getConsumerMemberInfo(cid, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "更新C端用户信息" )
    @PostMapping( "/update" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateConsumerMemberInfo(@RequestBody UpdateConsumerMemberVo vo){
        ValidationUtils.validate(vo);
        consumerIdentityService.updateConsumerMemberInfo(vo, getUserLoginInfoEo());

        return renderSuccess();
    }

    @ApiOperation( "禁用C端" )
    @PostMapping( "/disable/{cid}" )
    @ApiOperationSupport(order = 40)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult disableConsumerIdentity(@PathVariable( value = "cid" ) Integer cid){
        AssertUtils.isTrue(cid <= 0, "cid不合法");
        consumerIdentityService.disableConsumerIdentity(cid, 1, (long)getUserInfoId(), "管理员禁用C端身份", getCurrentOrgId());

        return renderSuccess();
    }

    @ApiOperation( "启用C端" )
    @PostMapping( "/enable/{cid}" )
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult enableConsumerIdentity(@PathVariable( value = "cid" ) Integer cid){
        AssertUtils.isTrue(cid <= 0, "cid不合法");
        consumerIdentityService.enableConsumerIdentity(cid, 1, (long)getUserInfoId(), "管理员恢复C端身份", getCurrentOrgId());

        return renderSuccess();
    }

    @ApiOperation( "邀请的用户列表和奖励详情" )
    @PostMapping( "/inviteMemberAndRewardDetail" )
    @ApiOperationSupport(order = 60)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = InviteMemberRewardInfoDto.class )
    })
    public DataResult inviteMemberAndRewardDetail(@RequestBody QueryInviteMemberRewardVo vo){
        ValidationUtils.validate(vo);
        InviteMemberRewardInfoDto result = consumerIdentityService.getInviteMemberAndRewardDetail(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "发送定向优惠券" )
    @PostMapping( "/sendTargetedCoupons" )
    @ApiOperationSupport(order = 70)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sendTargetedCoupons(@RequestBody SendTargetedCouponsVo vo){
        ValidationUtils.validate(vo);
        UserLoginInfoEo userLoginInfoEo = getUserLoginInfoEo();
        couponExternalService.sendTargetedCoupons(vo, userLoginInfoEo.getUserInfoId(), userLoginInfoEo.getOrgId());

        return renderSuccess();
    }
    
}
