package com.awg.comm.service;

import com.awg.comm.dto.DistrictDto;
import com.awg.comm.entity.NewDistrictInfo;
import com.awg.comm.vo.DisplayChangeVo;
import com.awg.comm.vo.DistrictInfoVo;
import com.awg.comm.vo.DistrictSortVo;
import com.awg.comm.vo.QueryDistrictVo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 地区信息表 服务类 - 新版本
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface NewDistrictInfoService extends IService<NewDistrictInfo> {
    
    /**
     * <p>
     * 获取地区列表
     * </p>
     *
     * @return:
     */
    List<DistrictDto> getDistrictListAll(QueryDistrictVo vo);

    /**
     * <p>
     * 添加地区
     * </p>
     *
     * @return:
     */
    Integer addDistrictInfo(DistrictInfoVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 修改地区
     * </p>
     *
     * @return:
     */
    Integer updateDistrictInfo(DistrictInfoVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 删除地区信息
     * </p>
     *
     * @return:
     */
    void deleteDistrictInfo(Integer districtId, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 地区信息排序
     * </p>
     *
     * @return:
     */
    void sortDistrictInfo(DistrictSortVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 修改地区展示状态
     * </p>
     *
     * @return:
     */
    void changeDisplayFlag(DisplayChangeVo vo, UserLoginInfoEo userLoginInfoEo);
}