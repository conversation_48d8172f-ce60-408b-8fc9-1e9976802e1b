package com.awg.crm.vo;

import com.awg.mybatis.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "政府费政府截图")
public class CrmLeadsGovernmentScreenshotVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Integer id = 0;

    @ApiModelProperty("政府费记录ID")
    private Integer crmLeadsGovernmentId;

    @ApiModelProperty("支付截图路径")
    private String screenshotPath;

    @ApiModelProperty("1-新增 2-删除")
    @TableField(exist = false) // 排除该字段，不映射到数据库
    private Integer code = 0;







}
