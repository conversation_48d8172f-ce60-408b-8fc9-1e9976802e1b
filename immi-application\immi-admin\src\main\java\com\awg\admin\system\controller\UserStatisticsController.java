package com.awg.admin.system.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.dto.RankingDataDto;
import com.awg.system.dto.RankingParticipantDto;
import com.awg.system.service.IUserStatisticsService;
import com.awg.system.vo.QueryRankingParticipantVo;
import com.awg.system.vo.RankingDataVo;
import com.awg.system.vo.RankingParticipantVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@ApiSupport( order = 13 )
@Api( tags = {"用户统计相关接口"} )
@RestController
@RequestMapping( "/system/userStatistics" )
public class UserStatisticsController extends BaseController {

    @Resource
    private IUserStatisticsService userStatisticsService;

    @ApiOperation( "获取参与排行人员列表" )
    @ApiOperationSupport(order = 10)
    @PostMapping( "/ranking/participant/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = RankingParticipantDto.class )
    })
    public DataResult queryRankingParticipant(@RequestBody QueryRankingParticipantVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userStatisticsService.queryRankingParticipant(vo));
    }

    @ApiOperation( "排行榜参与人员添加" )
    @ApiOperationSupport(order = 20)
    @PostMapping( "/ranking/participant/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addRankingParticipant(@RequestBody RankingParticipantVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userStatisticsService.addRankingParticipant(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "排行榜参与人员更新" )
    @ApiOperationSupport(order = 30)
    @PostMapping( "/ranking/participant/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateRankingParticipant(@RequestBody RankingParticipantVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userStatisticsService.updateRankingParticipant(vo, getUserLoginInfoEo()));
    }

    @ApiOperation( "排行榜参与人员移除" )
    @ApiOperationSupport(order = 40)
    @PostMapping( "/ranking/participant/delete/{userInfoId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult deleteRankingParticipant(@PathVariable( value = "userInfoId" ) Integer userInfoId) {
        AssertUtils.isTrue(userInfoId <= 0, "人员id不合法");
        Integer delId = userStatisticsService.deleteRankingParticipant(userInfoId, getUserLoginInfoEo());
        return renderSuccess(delId);
    }

    @ApiOperation( "获取排行榜数据" )
    @ApiOperationSupport(order = 50)
    @PostMapping( "/getRankingData" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = RankingDataDto.class )
    })
    public DataResult getRankingData(@RequestBody RankingDataVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(userStatisticsService.getRankingData(vo, getUserLoginInfoEo()));
    }
}
