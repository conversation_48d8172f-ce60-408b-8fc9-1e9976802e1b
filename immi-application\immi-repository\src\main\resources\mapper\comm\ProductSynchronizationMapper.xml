<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.awg.comm.mapper.ProductSynchronizationMapper">
    <resultMap id="BaseResultMap" type="com.awg.comm.entity.ProductSynchronization">
        <id column="id" property="id" />
        <result column="org_id" property="orgId" />
        <result column="synchronization_type" property="synchronizationType" />
        <result column="consultation_button_text" property="consultationButtonText" />
        <result column="consultant_wechat" property="consultantWechat" />
        <result column="consultant_qrcode" property="consultantQrcode" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <select id="countUnSyncedDetailsByProductNo" resultType="int" parameterType="string">
        SELECT COUNT(*)
        FROM comm_product_synchronization s
        JOIN comm_product_synchronization_details d
          ON d.sync_id = s.id
        WHERE d.is_sync = 1
          AND d.product_no = #{productNo}
    </select>

    <!-- 可根据业务需求添加自定义SQL -->
</mapper> 