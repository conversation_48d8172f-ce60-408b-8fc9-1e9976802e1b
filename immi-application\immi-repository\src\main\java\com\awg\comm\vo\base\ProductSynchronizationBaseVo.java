package com.awg.comm.vo.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 商品同步表-基础VO
 */
@Data
public class ProductSynchronizationBaseVo {
    @ApiModelProperty(value = "机构id", required = true)
    @NotNull(message = "机构id不能为空")
    private Long orgId;

    @ApiModelProperty(value = "同步类型（1: 咨询, 2: 无支付咨询入口）", required = true)
    @NotNull(message = "请选择同步类型")
    private Integer synchronizationType;

    @ApiModelProperty(value = "咨询按钮文案")
    @Size(max = 20, message = "咨询按钮文案长度不能超过20个字符")
    private String consultationButtonText;

    @ApiModelProperty(value = "咨询顾问微信号")
    @Size(max = 20, message = "咨询顾问微信号长度不能超过20个字符")
    private String consultantWechat;

    @ApiModelProperty(value = "咨询顾问二维码")
    private String consultantQrcode;
} 
