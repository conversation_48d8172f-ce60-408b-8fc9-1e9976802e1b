package com.awg.common.validator.email;


import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Documented
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Constraint(validatedBy = {EmailValidator.class})
@SuppressWarnings(value = "unused")
public @interface ValidationEmail {

    //提示信息
    String message() default "不合法的邮箱格式";

    //不同情况下效验逻辑
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
