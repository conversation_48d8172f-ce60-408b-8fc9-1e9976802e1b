package com.awg.comm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 批量同步商品结果 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@ApiModel(value = "批量同步商品结果")
public class BatchSynchronizeResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总商品数量")
    private Integer totalCount;

    @ApiModelProperty(value = "成功同步数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败同步数量")
    private Integer failCount;

    @ApiModelProperty(value = "成功同步的商品编号列表")
    private List<String> successProductNos = new ArrayList<>();

    @ApiModelProperty(value = "失败同步的商品信息列表")
    private List<FailedSyncInfo> failedSyncInfos = new ArrayList<>();

    /**
     * 失败同步信息
     */
    @Data
    @ApiModel(value = "失败同步信息")
    public static class FailedSyncInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "商品编号")
        private String productNo;

        @ApiModelProperty(value = "同步类型（0=初始同步，1=更新同步）")
        private Integer syncType;

        @ApiModelProperty(value = "失败原因")
        private String failReason;

        public FailedSyncInfo() {}

        public FailedSyncInfo(String productNo, Integer syncType, String failReason) {
            this.productNo = productNo;
            this.syncType = syncType;
            this.failReason = failReason;
        }
    }

    /**
     * 添加成功同步的商品
     */
    public void addSuccessProduct(String productNo) {
        this.successProductNos.add(productNo);
        this.successCount = this.successProductNos.size();
    }

    /**
     * 添加失败同步的商品
     */
    public void addFailedProduct(String productNo, Integer syncType, String failReason) {
        this.failedSyncInfos.add(new FailedSyncInfo(productNo, syncType, failReason));
        this.failCount = this.failedSyncInfos.size();
    }

    /**
     * 计算总数
     */
    public void calculateTotal() {
        this.totalCount = this.successCount + this.failCount;
    }

    /**
     * 判断是否全部成功
     */
    public boolean isAllSuccess() {
        return this.failCount == 0;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (this.totalCount == 0) {
            return 0.0;
        }
        return (double) this.successCount / this.totalCount * 100;
    }
}
