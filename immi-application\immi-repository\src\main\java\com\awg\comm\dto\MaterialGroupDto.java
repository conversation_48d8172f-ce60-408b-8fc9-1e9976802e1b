package com.awg.comm.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * <b>MaterialGroupDto</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel(value = "材料分组信息")
public class MaterialGroupDto implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "材料分组编号")
    private String materialGroupNo;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "材料清单列表")
    private List<MaterialDto> materialList;


}
