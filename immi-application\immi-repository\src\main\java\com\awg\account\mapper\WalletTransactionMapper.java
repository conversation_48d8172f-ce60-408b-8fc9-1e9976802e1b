package com.awg.account.mapper;

import com.awg.account.dto.WalletTransactionItemDto;
import com.awg.account.entity.WalletTransaction;
import com.awg.account.vo.QueryWalletTransactionVo;
import com.awg.comm.dto.ProductItemDto;
import com.awg.comm.vo.QueryProductVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 钱包交易流水表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface WalletTransactionMapper extends BaseMapper<WalletTransaction> {
}
