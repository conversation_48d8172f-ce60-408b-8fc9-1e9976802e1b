package com.awg.common.base.exception;

public enum ExceptionTypeEnum {
    /**
     * 异常日志出错模块
     */

    APP(1, "app"),

    ADMIN(2, "admin");

    Integer code;
    String label;

    ExceptionTypeEnum(Integer code, String label) {
        this.code = code;
        this.label = label;
    }

    public Integer getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
