package com.awg.common.base.eo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>MemberConsumerInfoEo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "C端用户信息数据")
public class MemberConsumerInfoEo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    private Integer memberId;

    /**
     * 会员编号
     */
    private Long memberNo;

    /**
     * cid
     */
    private Integer cid;

    /**
     * 姓名
     */
    private String name;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

}
