package com.awg.account.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <b>MemberWalletInfoVo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-05
 */

@Data
@ApiModel(value = "获取会员钱包信息参数")
public class MemberWalletInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型(1=B端，2=C端)")
    private Integer type;
}
