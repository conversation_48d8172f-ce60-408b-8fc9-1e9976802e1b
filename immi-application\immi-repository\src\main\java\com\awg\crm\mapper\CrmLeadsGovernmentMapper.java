package com.awg.crm.mapper;

import com.awg.crm.dto.CrmLeadsGovernmentDto;
import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.vo.CrmLeadsGovernmentVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName
 * @ClassName CrmLeadsGovernmentMapper
 * @Author: ZhouJun
 * @Date: 2025-07-09
 * @Description:
 **/
@Mapper
public interface CrmLeadsGovernmentMapper extends BaseMapper<CrmLeadsGovernment> {

    /**
     * 根据潜客id批量查询政府费
     * @param leadsId
     * @return
     */
    List<CrmLeadsGovernmentDto> listByLeadsId(@Param("leadsId") Integer leadsId);

    /**
     * 批量查询
     * @param id
     * @return
     */
    CrmLeadsGovernmentDto getById(@Param("id") Integer id);

    /**
    * 分页查询
    * @param bean
    * @return
    */
    IPage<CrmLeadsGovernment> selectCrmLeadsGovernmentListByPage(Page<CrmLeadsGovernment> page, CrmLeadsGovernment bean);

    /**
     * 新增
     * @param bean
     */
    Integer insertCrmLeadsGovernment(CrmLeadsGovernment bean);

    /**
     * 根据id修改
     * @param bean
     */
    Integer updateCrmLeadsGovernmentById(CrmLeadsGovernment bean);

    /**
     * 根据id删除
     * @param id
     */
    Integer deleteCrmLeadsGovernmentById(@Param("id") String id);

}
