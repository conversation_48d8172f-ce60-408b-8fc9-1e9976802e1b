package com.awg.admin.crm.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.dto.ClientBusinessDetailDto;
import com.awg.crm.dto.LeadsTagDto;
import com.awg.crm.dto.PaymentAccountDto;
import com.awg.crm.entity.PaymentAccount;
import com.awg.crm.service.IClientBusinessService;
import com.awg.crm.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 40 )
@Api( tags = {"客户业务相关接口"} )
@RestController
@RequestMapping( "/crm/clientBusiness" )
public class ClientBusinessController extends BaseController {

    @Resource
    private IClientBusinessService clientBusinessService;

    @ApiOperation( "添加客户业务" )
    @PostMapping( "/addClientBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addClientBusiness(@RequestBody AddClientBusinessVo vo){
        ValidationUtils.validate(vo);
        return renderSuccess(clientBusinessService.addClientBusiness(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "编辑客户业务" )
    @PostMapping( "/editClientBusiness" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult editClientBusiness(@RequestBody EditClientBusinessVo vo){
        ValidationUtils.validate(vo);
        return renderSuccess(clientBusinessService.editClientBusiness(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "删除客户业务" )
    @PostMapping( "/delClientBusiness/{clientBusinessNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult delClientBusiness(@PathVariable( value = "clientBusinessNo" ) String clientBusinessNo){
        AssertUtils.isNull(clientBusinessNo, "客户编号不能为空");
        return renderSuccess(clientBusinessService.delClientBusiness(clientBusinessNo, getUserInfoId(), getCurrentOrgId(), getCurrentOrgType()));
    }

    @ApiOperation( "新增支付状态" )
    @PostMapping( "/v2/addPaymentStatus" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addPaymentStatus(@RequestBody EditPaymentStatusVo vo){
        ValidationUtils.validate(vo);
        vo.setV(2);
        return renderSuccess(clientBusinessService.addPaymentStatus(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "编辑支付状态" )
    @PostMapping( "/v2/editPaymentStatus" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult editPaymentStatus(@RequestBody EditPaymentStatusVo vo){
        ValidationUtils.validate(vo);
        vo.setV(2);
        if (vo.getId()==null || vo.getId()<=0) {
            return renderSuccess(clientBusinessService.addPaymentStatus(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
        }
        else {
            return renderSuccess(clientBusinessService.editPaymentStatus(vo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
        }
    }

    @ApiOperation( "删除支付状态" )
    @PostMapping( "/v2/delPaymentStatus/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult delPaymentStatus(@PathVariable( value = "id" ) Integer id){
        AssertUtils.isFalse(id>0, "id不能为空");
        return renderSuccess(clientBusinessService.delPaymentStatus(id, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "流程保存" )
    @PostMapping( "/v2/saveProcessStatus/{leadsId}/{clientBusinessNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult saveProcessStatus(
            @RequestBody List<EditProcessStatusVo> vos,
            @PathVariable( value = "leadsId" ) Integer leadsId,
            @PathVariable( value = "clientBusinessNo" ) String clientBusinessNo
    ){
        // 迭代校验列表
        vos.forEach( item -> {
            ValidationUtils.validate(item);
        });
        return renderSuccess(clientBusinessService.saveProcessStatus(vos, getUserLoginInfoEo(), leadsId, clientBusinessNo));
    }

    @ApiOperation( "收款账户保存" )
    @PostMapping( "/v2/savePaymentAccount" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult savePaymentAccount(
            @RequestBody List<PaymentAccountVo> vos
    ){
        // 迭代校验列表
        vos.forEach( item -> {
            ValidationUtils.validate(item);
        });
        return renderSuccess(clientBusinessService.savePaymentAccount(vos, getUserLoginInfoEo()));
    }

    @ApiOperation( "收款账户列表" )
    @PostMapping( "/getPaymentAccountAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = PaymentAccountDto.class )
    })
    public DataResult getPaymentAccountAll() {
        return renderSuccess(clientBusinessService.getPaymentAccountAll(getUserLoginInfoEo()));
    }

    @ApiOperation( "查询客户业务详情【列表】" )
    @PostMapping( "/getClientBusinessDetailList/{leadsId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ClientBusinessDetailDto.class )
    })
    public DataResult getClientBusinessDetailList(@PathVariable( value = "leadsId" ) Integer leadsId){
        AssertUtils.isTrue(leadsId <= 0, "id不合法");
        return renderSuccess(clientBusinessService.getClientBusinessDetailList(leadsId, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }

    @ApiOperation( "查询客户业务详情【单个】" )
    @PostMapping( "/getClientBusinessDetailOne/{clientBusinessNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ClientBusinessDetailDto.class )
    })
    public DataResult getClientBusinessDetailOne(@PathVariable( value = "clientBusinessNo" ) String clientBusinessNo){
        AssertUtils.isTrue( clientBusinessNo==null || clientBusinessNo.equals(""), "客户业务编号不能为空");
        return renderSuccess(clientBusinessService.getClientBusinessDetail(clientBusinessNo, getCurrentOrgId(), getUserInfoId(), getCurrentOrgType()));
    }
}
