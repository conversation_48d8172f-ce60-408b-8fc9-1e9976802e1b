package com.awg.front.client.controller;

import com.awg.client.dto.BusinessMemberItemDto;
import com.awg.client.dto.MyInviteRecordDto;
import com.awg.client.eo.MemberLoginInfoPlusEo;
import com.awg.client.service.IMemberInfoService;
import com.awg.client.service.IMemberInviteService;
import com.awg.client.service.IMemberService;
import com.awg.client.vo.ConsumerBaseInfoVo;
import com.awg.client.vo.GetMemberInviteCodeVo;
import com.awg.client.vo.MyInviteRecordVo;
import com.awg.client.vo.QueryBMemberVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.dto.OrgSkinDto;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.system.vo.OrgSkinVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 60 )
@Api( tags = {"会员-相关接口"} )
@RestController
@RequestMapping( "/client/member" )
@Slf4j
public class memberController extends BaseController {

    @Resource
    private IMemberService memberService;

    @Resource
    private IMemberInfoService memberInfoService;

    @Resource
    private IOrgExternalService orgExternalService;

    @Resource
    private IMemberInviteService memberInviteService;

    @ApiOperation( "获取用户登录信息" )
    @PostMapping( "/userLoginInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberLoginInfoPlusEo.class )
    })
    public DataResult userLoginInfo() {
        Integer memberId = getCurrentMemberId();
        MemberLoginInfoPlusEo memberLoginInfoPlusEo = memberService.getUserLoginInfo(memberId, getMemberLoginHashKey());

        String inviteCode = memberInviteService.getInviteCode(2, 5, (long)memberId);
        AssertUtils.isNull(inviteCode, "暂无您的邀请码，请联系管理员");
        memberLoginInfoPlusEo.setInviteCode(inviteCode);

        return renderSuccess(memberLoginInfoPlusEo);
    }

    @ApiOperation( "设置用户基础信息" )
    @PostMapping( "/setMemberBaseInfo" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult setMemberBaseInfo(@RequestBody ConsumerBaseInfoVo vo) {

        Integer memberId = getCurrentMemberId();
        MemberLoginInfoPlusEo memberLoginInfoPlusEo = memberService.getUserLoginInfo(memberId, getMemberLoginHashKey());

        if(memberLoginInfoPlusEo.getBid()>0) {
            vo.setBusinessName(vo.getNickname());
            vo.setNickname(null);
            AssertUtils.isTrue(StringUtils.isBlank(vo.getBusinessName()), "昵称不能为空");
        }
        else {
            vo.setNickname(vo.getNickname());
            vo.setBusinessName(null);
            AssertUtils.isTrue(StringUtils.isBlank(vo.getNickname()), "昵称不能为空");
        }

        ValidationUtils.validate(vo);
        memberInfoService.setMemberBaseInfo(vo, getMemberLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "获取邀请码" )
    @PostMapping( "/getMemberInviteCodeUrl" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = String.class )
    })
    public DataResult getMemberInviteCodeUrl(@RequestBody GetMemberInviteCodeVo vo) {
        String inviteCode = memberInviteService.getMemberInviteCodeUrl(getMemberLoginInfoEo(), vo);
        return renderSuccess(inviteCode);
    }

    @ApiOperation( "获取邀请记录" )
    @PostMapping( "/getMyInviteRecord" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MyInviteRecordDto.class )
    })
    public DataResult getMyInviteRecord(@RequestBody MyInviteRecordVo vo) {
        ValidationUtils.validate(vo);
        BasePageResult<MyInviteRecordDto> result = memberInviteService.getMyInviteRecord(vo, getMemberLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "用户点击邀请悬浮按钮" )
    @PostMapping( "/memberClickInviteButton" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult memberClickInviteButton() {
        Integer memberId = getCurrentMemberId();
        memberService.memberClickInviteButton(memberId);
        return renderSuccess();
    }

    @ApiOperation( "获取机构皮肤" )
    @PostMapping( "/getSkin" )
    @Authority( AuthorityEnum.NOCHECK )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = OrgSkinDto.class)
    })
    public DataResult getSkin(@RequestBody(required = false) OrgSkinVo orgSkinVo) {

        OrgSkinVo vo = new OrgSkinVo();
        if(orgSkinVo!=null) {
            BeanUtils.copyProperties( orgSkinVo, vo );
        }

        if(vo.getOrgId()==null) {
            vo.setOrgId(getCurrentMemberOrgId());
        }

        ValidationUtils.validate(vo);
        return renderSuccess(orgExternalService.getSkin(vo));
    }

    @ApiOperation ( "退出登录" )
    @PostMapping ( "/logout" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult logout() {
        Integer memberId = getCurrentMemberId();
        memberService.logout(memberId, getMemberLoginHashKey(), getCurrentMemberOrgId());
        return renderSuccess(memberId);
    }

}
