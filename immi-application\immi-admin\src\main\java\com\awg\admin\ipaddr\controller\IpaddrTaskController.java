package com.awg.admin.ipaddr.controller;

import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.ipaddr.service.IIpaddrLocationService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * <p>
 * <b>IpaddrTaskController</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@Api ( tags = {"ip地址模块定时任务"} )
@RestController
@RequestMapping ( "/ipaddr/timer" )
@Authority ( AuthorityEnum.NOCHECK )
@Slf4j
public class IpaddrTaskController {

    @Resource
    private IIpaddrLocationService ipaddrLocationService;

    @ApiOperation ( value = "IP归属地解析执行" )
    @GetMapping ( value = "/execute/ipLocationResolution" )
    public void executeIpLocationResolution() {
        ipaddrLocationService.executeIpLocationResolution();
    }
}
