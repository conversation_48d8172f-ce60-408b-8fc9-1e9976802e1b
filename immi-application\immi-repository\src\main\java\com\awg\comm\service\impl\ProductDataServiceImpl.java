package com.awg.comm.service.impl;

import com.awg.comm.entity.ProductData;
import com.awg.comm.mapper.ProductDataMapper;
import com.awg.comm.service.IProductDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 商品数据表 服务实现类
 */
@Service
public class ProductDataServiceImpl extends ServiceImpl<ProductDataMapper, ProductData> implements IProductDataService {
    // 可根据业务需求添加自定义方法实现
} 