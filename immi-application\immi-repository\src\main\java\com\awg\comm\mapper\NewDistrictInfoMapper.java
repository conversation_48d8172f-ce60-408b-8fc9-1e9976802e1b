package com.awg.comm.mapper;

import com.awg.comm.dto.DistrictDto;
import com.awg.comm.entity.NewDistrictInfo;
import com.awg.comm.vo.QueryDistrictVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 地区信息表 Mapper 接口 - 新版本
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface NewDistrictInfoMapper extends BaseMapper<NewDistrictInfo> {

    /**
     * <p>
     * 获取地区列表
     * </p>
     *
     * @author: your_name
     * @date: 2024-12-19
     */
    List<DistrictDto> getDistrictListAll(
            @Param("query") QueryDistrictVo query,
            @Param("regionCode") String regionCode
    );
}