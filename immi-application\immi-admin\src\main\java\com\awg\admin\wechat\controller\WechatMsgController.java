package com.awg.admin.wechat.controller;

import com.awg.admin.wechat.aes.WXBizMsgCrypt;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.redis.RedisUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.mybatis.entity.BaseEntity;
import com.awg.utils.io.IOUtil;
import com.awg.utils.json.JsonUtil;
import com.awg.utils.xml.XmlUtil;
import com.awg.website.entity.WebsiteBaseConfig;
import com.awg.wechat.constant.WechatConstant;
import com.awg.wechat.entity.WechatReceiveMsgRecord;
import com.awg.wechat.entity.WechatUserAssociation;
import com.awg.wechat.mapper.WechatReceiveMsgRecordMapper;
import com.awg.wechat.mapper.WechatUserAssociationMapper;
import com.awg.wechat.service.IWechatMsgRecordService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
@ApiSupport( order = 120 )
@Api( tags = {"微信通知-项目相关接口"} )
@RestController
@RequestMapping( "/wechat/msg" )
@Slf4j
public class WechatMsgController extends BaseController {

    @Resource
    private IWechatMsgRecordService wechatMsgRecordService;

    @Resource
    private WechatReceiveMsgRecordMapper wechatReceiveMsgRecordMapper;

    @Resource
    private HttpServletResponse response;

    @Resource
    private WechatUserAssociationMapper wechatUserAssociationMapper;

    @Resource
    private RedisUtils redisUtils;

    @ApiOperation( value = "获取绑定二维码" )
    @PostMapping( value = "/getBindQrcode/{userId}" )
    public DataResult getBindQrcode(@PathVariable( value = "userId" ) Integer userId) {
        return renderSuccess(wechatMsgRecordService.createQrcode(userId));
    }

    @ApiOperation( value = "获取自己绑定二维码" )
    @PostMapping( value = "/getMyBindQrcode" )
    public DataResult getBindQrcode() {
        UserLoginInfoEo userLoginInfoEo = getUserLoginInfoEo();
        return renderSuccess(wechatMsgRecordService.createQrcode(userLoginInfoEo.getUserId()));
    }

    @ApiOperation( value = "发送测试消息" )
    @PostMapping( value = "/sendTestMsg/{userId}" )
    public DataResult sendTestMsg(@PathVariable( value = "userId" ) Integer userId) {
        return renderSuccess(wechatMsgRecordService.sendTestMsg(userId));
    }

    @ApiOperation( value = "发送通知定时任务" )
    @GetMapping( value = "/execute/sendMsg/{password}" )
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult executeSendMsg(@PathVariable( value = "password" ) String password) {

        // 密码正确
        if(password.equals("daoidjeiodejduoeaheaiod9eu38ey3s")){
            wechatMsgRecordService.sendMsgPro();
        }

        return renderSuccess();
    }

    /**
     * <p>
     * 消息与事件接收回调接口（如果携带了appid则代表是第三方平台的回调，未携带则代表是公众号自身进行的回调）
     * </p>
     *
     * @author: 夜晓
     * @date: 2021/8/18
     * @return: java.lang.String
     */
    @ApiIgnore
    @RequestMapping ( value = "/notify", method = {RequestMethod.GET, RequestMethod.POST} )
    @Authority ( AuthorityEnum.NOCHECK )
    public void wechatNotify(@RequestParam ( value = "appid", required = false ) String appid,
                             @RequestParam ( value = "openid", required = false ) String openid,
                             @RequestParam ( value = "msg_signature", required = false ) String msgSignature,
                             @RequestParam ( value = "timestamp", required = false ) String timestamp,
                             @RequestParam ( value = "nonce", required = false ) String nonce) {
        System.out.println("========= wechat notify =========");

        try {
            String echo = request.getParameter("echostr");
            if (StringUtils.isNotBlank(echo)) {
                response.getWriter().println(Long.parseLong(echo));
            }

            // 解析信息
            Map<String, Object> decryptMsg = decryptMsg(StringUtils.isBlank(appid), msgSignature, timestamp, nonce);
            String decryptMsgStr = JsonUtil.objToJson(decryptMsg);
            System.out.println("wechat notify=======================" + decryptMsgStr + "=======================");

            WechatReceiveMsgRecord wechatReceiveMsgRecord = new WechatReceiveMsgRecord();
            wechatReceiveMsgRecord.setOpenid(openid);
            wechatReceiveMsgRecord.setContent(decryptMsgStr);
            wechatReceiveMsgRecordMapper.insert(wechatReceiveMsgRecord);

            // 获取消息类型
            String event = String.valueOf(decryptMsg.getOrDefault("Event", ""));

            // 扫描带参数二维码事件，或者关注事件
            if (decryptMsg.containsKey("Ticket") && StringUtils.isNotBlank(openid) && StringUtils.isBlank(appid)) {
                String eventKey = String.valueOf(decryptMsg.getOrDefault("EventKey", ""));

                // 关注事件
                if ("subscribe".equals(event)) {
                    // 未关注时eventKey有前缀qrscene_, 后面拼接的为场景id
                    eventKey = eventKey.substring(8);
                }

                // 准备缓存信息
                Map<String, Object> redisInfo = null;

                // 扫码事件或者关注事件
                if ("subscribe".equals(event) || "SCAN".equals(event)) {
                    // eventKey需要有内容
                    if (StringUtils.isNotBlank(eventKey)) {
                        // 获取redis保存信息
                        String redisKey = "immiWx:accessToken:crm:qrcode:" + eventKey;

                        if (redisUtils.exists(redisKey)) {
                            redisInfo = JsonUtil.jsonToObj(JsonUtil.objToJson(redisUtils.hGetAll(redisKey)), Map.class);
                            redisUtils.remove(redisKey);
                        }
                        else {
                            bindingReply(decryptMsg, timestamp, nonce, "二维码已失效");
                        }
                    }
                }

                if(redisInfo!=null){
                    // 获取用户id和过期时间
                    Integer userId = Integer.parseInt(String.valueOf(redisInfo.getOrDefault("userId", "")));


                    // userId 正常大于0，openid不能为空
                    if(userId!=null && userId>0 && StringUtils.isNotBlank(openid)){
                        // 查找绑定关系
                        WechatUserAssociation wechatUserAssociation = wechatUserAssociationMapper.selectOne(Wrappers.<WechatUserAssociation>lambdaQuery()
                                .eq(WechatUserAssociation::getUserId, userId)
                                .eq(BaseEntity::getIsDelete, TrueFalseEnum.FALSE.getCode())
                                .orderByDesc(WechatUserAssociation::getId)
                                .last("limit 1")
                        );

                        // 未找到绑定关系，则创建
                        if(wechatUserAssociation==null) {
                            wechatUserAssociation = new WechatUserAssociation();
                            wechatUserAssociation.setUserId(userId);
                            wechatUserAssociation.setOpenid(openid);
                            wechatUserAssociationMapper.insert(wechatUserAssociation);
                        }

                        // 更新openid
                        wechatUserAssociation.setOpenid(openid);
                        wechatUserAssociationMapper.updateById(wechatUserAssociation);

                        // 回复消息
                        bindingReply(decryptMsg, timestamp, nonce, "授权成功");
                    }
                }
            }


        } catch (Exception e) {
            log.error("回调或绑定用户发生异常：{0}", e.fillInStackTrace());
        }
    }

    /**
     * <p>
     * 微信回调内容解码
     * </p>
     *
     * @author: 夜晓
     * @date: 2021/8/18
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     */
    private Map<String, Object> decryptMsg(boolean isAwg, String msgSignature, String timestamp, String nonce) {
        Map<String, Object> result = new LinkedHashMap<>();
        if (StringUtils.isBlank(msgSignature) || StringUtils.isBlank(timestamp) || StringUtils.isBlank(nonce)) {
            return result;
        }

        try {
            String content = IOUtil.read(request.getInputStream());
            if (StringUtils.isBlank(content)) {
                return result;
            }

            WXBizMsgCrypt crypt;
            if (isAwg) {
                crypt = new WXBizMsgCrypt(WechatConstant.IMMI_CRM_WX_APP_ID, WechatConstant.IMMI_CRM_WX_TOKEN, WechatConstant.IMMI_CRM_ENCODING_AES_KEY);
            } else {
                crypt = new WXBizMsgCrypt();
            }

            // 解码xml信息
            String xmlText = crypt.decryptMsg(msgSignature, timestamp, nonce, content);
            result = XmlUtil.xmlToMap(xmlText);
        } catch (Exception e) {
            log.error("微信消息解码异常：{0}", e.fillInStackTrace());
        }

        return result;
    }

    /**
     * <p>
     * 绑定成功回复
     * </p>
     *
     * @author: jqnn
     * @date: 2022/10/18
     * @return: void
     */
    private void bindingReply(Map<String, Object> message, String timestamp, String nonce, String content) {
        if (CollectionUtils.isEmpty(message)) {
            return;
        }

        Map<String, String> replyMsg = new LinkedHashMap<>();
        replyMsg.put("ToUserName", String.valueOf(message.get("FromUserName")));
        replyMsg.put("FromUserName", String.valueOf(message.get("ToUserName")));
        replyMsg.put("CreateTime", String.valueOf(System.currentTimeMillis()));
        replyMsg.put("MsgType", "text");

        if(StringUtils.isBlank(content)){
            return;
        }

        replyMsg.put("Content", new String(content.getBytes(), StandardCharsets.UTF_8));

        // 消息回复
        try {
            if (StringUtils.isNotBlank(content)) {
                String xml = XmlUtil.mapToXml(replyMsg);
                WXBizMsgCrypt crypt = new WXBizMsgCrypt(WechatConstant.IMMI_CRM_WX_APP_ID, WechatConstant.IMMI_CRM_WX_TOKEN, WechatConstant.IMMI_CRM_ENCODING_AES_KEY);

                // 加密xml信息
                String xmlText = crypt.encryptMsg(xml, timestamp, nonce);
                response.getWriter().write(xmlText);
            } else {
                response.getWriter().write(content);
            }
        } catch (Exception e) {
            log.error("微信被动回复失败：{0}", e.fillInStackTrace());
        }
    }
}
