package com.awg.comm.entity;

import com.awg.comm.dto.MaterialDto;
import com.awg.comm.dto.MaterialGroupDto;
import com.awg.comm.dto.ProductBannerDto;
import com.awg.comm.dto.ProductPdfDto;
import com.awg.ord.eo.CouponTemplateEo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "商品对比实体")
public class CompareProductEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @org.javers.core.metamodel.annotation.Id
    @ApiModelProperty(value = "商品编号")
    private String productNo;

    @ApiModelProperty(value = "分类（1=签证类，2=申校类）")
    private Integer category;

    @ApiModelProperty(value = "二级分类id")
    private Integer secondaryCategory;

    @ApiModelProperty(value = "二级分类名称")
    private String secondaryCategoryName;

    @ApiModelProperty(value = "教育阶段（1=中小学，2=college，3=本科，4=研究生）")
    private Integer educationalStage;

    @ApiModelProperty(value = "地区id")
    private Integer districtId;

    @ApiModelProperty(value = "地区名称")
    private String districtName;


    @ApiModelProperty(value = "学校logo")
    private String schoolLogo;

    @ApiModelProperty(value = "商品封面")
    private String cover;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品描述")
    private String description;


    @ApiModelProperty(value = "平台交付价格，0表示免费")
    private BigDecimal platformDeliveryPrice;


    @ApiModelProperty(value = "交付类型，0=自己交付，1=平台交付")
    private Integer deliveryType;

    @ApiModelProperty(value = "关键词id列表")
    private String keywordIds;

    @ApiModelProperty(value = "关键词列表")
    private List<ProductKeyword> keywordList;

    @ApiModelProperty(value = "关联项目(业务)")
    private String relatedBusinessNo;

    @ApiModelProperty(value = "关联项目(业务)名称")
    private String relatedBusinessName;

    @ApiModelProperty(value = "关联子级项目(业务)编号")
    private String relatedChildBusinessNo;

    @ApiModelProperty(value = "关联子级项目(业务)名称")
    private String relatedChildBusinessName;

    @ApiModelProperty(value = "注意事项")
    private String notes;

    @ApiModelProperty(value = "商品简介")
    private String feeDescription;

    @ApiModelProperty(value = "服务条款")
    private String termsOfService;

    @ApiModelProperty(value = "价格，0表示免费")
    private BigDecimal price;

    @ApiModelProperty(value = "必须材料列表")
    private List<MaterialDto> requiredMaterialList;

    @ApiModelProperty(value = "可选材料列表")
    private List<MaterialDto> optionalMaterialList;

    @ApiModelProperty(value = "分组材料列表（N选M材料）")
    private List<MaterialGroupDto> materialGroupList;

    @ApiModelProperty(value = "商品素材列表")
    private List<ProductBannerDto> productBannerList;

    @ApiModelProperty(value = "商品pdf")
    private List<ProductPdfDto> pdfList;



}
