package com.awg.common.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * <b>Constant</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/11/12 12:09
 */

public class ConfigConstant {

    /**
     * 维护开关
     */
    public static final String CONFIG_SWITCH_MAINTAIN = "config:switch:maintain";

    /**
     * ip白名单
     */
    public static final String CONFIG_IP_WHITE_LIST = "config:ip_white_list";

    /**
     * 默认ip白名单
     */
    public static final List<String> DEFAULT_IP_WHITE_LIST = Arrays.asList(
            "*************",
            "*************",
            "*************"
    );

    /**
     * 受保护的uri，需要登录，但是不需要鉴权
     */
    public static final List<String> PROTECTED_URI = Arrays.asList(
            "/system/businessUser/userLoginInfo",
            "/system/businessUser/all",
            "/system/businessUser/getRemainingSeats",
            "/system/businessUser/my/inviteUrlList",
            "/system/businessUser/my/avatarUpdate",
            "/system/businessUser/my/passwordUpdate",
            "/system/userStatistics/getRankingData",
            "/system/businessUser/switchOrg",
            "/system/businessUser/logout",
            "/system/businessUser/leadsTableConfig/get",
            "/system/businessUser/leadsTableConfig/set",
            "/system/businessUser/leadsColumnsFreezeConfig/get",
            "/system/businessUser/leadsColumnsFreezeConfig/set",
            "/system/org/all",
            "/system/org/servicePlansAll",
            "/crm/leads/tagListAll",
            "/crm/leads/getFieldRoleRelation/{fieldCode}",
            "/crm/leads/sourceListAll",
            "/crm/leads/getLeadsStatusConfigAll",
            "/crm/leads/orgLeadsIdToLeadsId/{orgLeadsId}",
            "/crm/clientBusiness/getPaymentAccountAll",
            "/crm/leads/channelChange",
            "/crm/leads/checkDuplicateLeadsInfo",
            "/crm/leads/sortLeads",
            "/comm/product/selectList",
            "/comm/product/getProductKeywordAll",
            "/comm/secondaryCategory/listAll",
            "/comm/keyword/listAll",
            "/comm/product/setting",
            "/comm/product/setting/{orgId}",
            "/system/org/info",
            "/system/org/getSkin",
            "/system/role/getMenuAll",
            "/system/role/listAll",
            "/bp/business/getBusinessTypeListAll",
            "/bp/business/getBusinessListAll",
            "/bp/business/getChildBusinessListAll",
            "/bp/business/getProcessList/{businessNo}",
            "/comm/districtInfo/listAll",
            "/files/upload",
            "/kbs/category/list",
            "/kbs/category/selectList",
            "/kbs/question/tagListAll",
            "/qs/questionnaire/getAllQuestionnaire",
            "/system/businessUser/getUserUnreadMsgCount",
            "/system/businessUser/updateMsgRead",
            "/wechat/msg/getMyBindQrcode",
            "/wechat/applet/generate/authorize/url",
            "/wechat/applet/info/{orgId}",
            "/wechat/applet/notify",
            "/comm/product/appModuleConfig/get",
            "/client/memberLevel/businessLevelConfigList",
            "/client/memberLevel/consumerLevelConfigList"
    );

    /**
     * 平台超管才允许操作的uri
     */
    public static final List<String> PLATFORM_ADMIN_URI = Arrays.asList(

    );
}