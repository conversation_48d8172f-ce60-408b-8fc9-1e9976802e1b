package com.awg.common.base.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description: 分页参数传递基础参数
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2019-11-17 22:12
 * @version: V1.0
 **/
@Data
public class BasePageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分页参数-当前第几页
     */
    @ApiModelProperty(value = "当前第几页", required = true)
    @Min(value = 1, message = "pageNo 必须大于0")
    @NotNull(message = "pageNo 不能为空")
    private Integer pageNo;

    /**
     * 分页参数-每页显示的总记录数
     */
    @ApiModelProperty(value = "每页显示的总记录数", required = true)
    @Min(value = 1, message = "pageSize 必须大于0")
    @Max(value = 2000, message = "pageSize 必须小于或等于100")
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;


    /**
     * 开始位置，从0开始
     */
    @ApiModelProperty(hidden = true)
    private Integer startPos;


    /**
     * 结束位子
     */
    @ApiModelProperty(hidden = true)
    private int endRow;


    /**
     * 分页开始位置-从0开始
     */
    public Integer getStartPos() {
        startPos = (pageNo - 1) * pageSize < 0 ? 0 : (pageNo - 1) * pageSize;
        return startPos;
    }


    /**
     * 分页结束位置
     */
    public int getEndRow() {
        endRow = getStartPos() + pageSize;
        return endRow;
    }

}