package com.awg.common.base.eo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * <b>MemberBaseInfoEo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "会员基础信息数据")
public class MemberBaseInfoEo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    private Integer memberId;

    /**
     * 会员编号
     */
    private Long memberNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机区号
     */
    private String phoneCode;

    /**
     * bid
     */
    private Integer bid;

    /**
     * cid
     */
    private Integer cid;
}
