package com.awg.account.vo;

import com.awg.common.base.page.BasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * <b>QueryWalletTransactionVo</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-12
 */

@Data
@ApiModel(value = "查询钱包交易流水参数")
public class QueryWalletTransactionVo extends BasePageVo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "钱包编号")
    private String walletNo;

    @ApiModelProperty(value = "操作类型筛选，多选，传数组")
    private List<Integer> actionCategory;
}
