package com.awg.externalAPI.website.controller;


import com.awg.bp.externalService.IBusinessExternalService;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileBaseUtil;
import com.awg.common.validator.ValidationUtils;
import com.awg.system.externalService.IOrgExternalService;
import com.awg.website.dto.CaseApprovalLetterDto;
import com.awg.website.dto.CaseDto;
import com.awg.website.dto.ProjectDataDto;
import com.awg.website.dto.ProjectDto;
import com.awg.website.service.IWebsiteProjectService;
import com.awg.website.vo.OfficialBaseVo;
import com.awg.website.vo.ProjectDetailVo;
import com.awg.website.vo.QueryProjectVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@ApiSupport( order = 75 )
@Api( tags = {"官网-项目接口"} )
@RestController
@RequestMapping( "/externalAPI/website/project" )
public class ExternalWebsiteProjectController extends BaseController {

    @Resource
    private IBusinessExternalService businessExternalService;

    @Resource
    private IWebsiteProjectService websiteProjectService;

    @Resource
    private IOrgExternalService orgExternalService;

    @ApiOperationSupport(order = 5)
    @ApiOperation( "获取业务类型列表" )
    @PostMapping( "/businessTypeList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult businessTypeList(@RequestBody OfficialBaseVo vo) {
        ValidationUtils.validate(vo);
        return DataResult.success(businessExternalService.getBusinessTypeListAll());
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "获取项目页数据" )
    @PostMapping( "/getData" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProjectDataDto.class )
    })
    public DataResult getData(@RequestBody QueryProjectVo vo) {

        ProjectDataDto projectVo = new ProjectDataDto();
        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());

        BasePageResult<ProjectDto> projectResult = websiteProjectService.queryProjectList(vo, userLoginInfoEo);

        // 循环填入完整路径
        for (ProjectDto projectDto : projectResult.getData()) {
            projectDto.setPreviewImageUrl(orgExternalService.getFileUrlByDomain(vo.getOrgId(), projectDto.getPreviewImagePath()));
        }

        projectVo.setProjectData(projectResult);

        return DataResult.success(projectVo);
    }

    @ApiOperationSupport(order = 15)
    @ApiOperation( "获取项目详情" )
    @PostMapping( "/detail")
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProjectDto.class )
    })
    public DataResult getProjectDetail(@RequestBody ProjectDetailVo vo) {

        ValidationUtils.validate(vo);

        UserLoginInfoEo userLoginInfoEo = new UserLoginInfoEo();
        userLoginInfoEo.setOrgId(vo.getOrgId());
        ProjectDto projectDto = websiteProjectService.projectDetail(vo.getProjectNo(), userLoginInfoEo);

        // 循环填入完整路径
        for(CaseDto caseDto: projectDto.getRelatedCaseList()) {

            for( CaseApprovalLetterDto caseApprovalLetterDto : caseDto.getApprovalLetterList() ){
                caseApprovalLetterDto.setApprovalLetterFileUrl(
                        orgExternalService.getFileUrlByDomain( vo.getOrgId(), caseApprovalLetterDto.getApprovalLetterFilePath())
                );
            }
        }

        return renderSuccess(projectDto);
    }


}
