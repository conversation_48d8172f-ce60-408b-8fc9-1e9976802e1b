package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b> RoleTypeEnum </b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-05
 */

@Getter
@AllArgsConstructor
public enum RoleTypeEnum {
    // 角色类型（0:管理员，1渠道人员）
    ADMIN(0, "管理员"),
    CHANNEL(1, "渠道人员")
    ;

    private final Integer code;
    private final String label;

    public static RoleTypeEnum parse(Integer code) {
        return Arrays.stream( RoleTypeEnum.values() )
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    public static RoleTypeEnum parse(String label) {
        return Arrays.stream(RoleTypeEnum.values())
                .filter(obj -> StringUtils.equals(label, obj.getLabel()))
                .findFirst()
                .orElse(null);
    }
}
