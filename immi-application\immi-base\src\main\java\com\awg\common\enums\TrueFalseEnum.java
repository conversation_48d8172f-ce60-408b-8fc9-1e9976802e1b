/**
 * TrueFalseEnum.java 2019年5月8日
 */
package com.awg.common.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b>TrueFalseEnum</b> is 真、假枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2019年5月8日下午1:58:40
 */
public enum TrueFalseEnum {

    /**
     * 假
     */
    FALSE(0, "0"),

    /**
     * 真
     */
    TRUE(1, "1");


    Integer code;
    String label;

    TrueFalseEnum(Integer code, String label) {
        this.code = code;
        this.label = label;
    }

    public Integer getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static TrueFalseEnum parse(Integer code) {
        return Arrays.stream(TrueFalseEnum.values())
                .filter(obj -> Objects.equals(obj.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
