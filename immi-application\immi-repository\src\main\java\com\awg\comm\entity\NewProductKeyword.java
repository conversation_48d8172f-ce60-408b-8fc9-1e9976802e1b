package com.awg.comm.entity;

import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品关键词表 - 新版本（支持MyBatis-Plus逻辑删除）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "comm_product_keyword")
public class NewProductKeyword extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 名称
     */
    private String name;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 来自平台的id，0表示不来自平台
     */
    private Integer fromPlatformId;

    /**
     * 展示标志[0不展示，1展示]
     */
    private Integer displayFlag;
}