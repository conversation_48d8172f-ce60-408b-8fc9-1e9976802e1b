package com.awg.common.sms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * <b>SmsEnum</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/9/29 10:26
 */

@Getter
@AllArgsConstructor
public enum SmsCodeEnum {

    /**
     * 注册
     */
    REGISTER(1, "687625"),

    /**
     * 登录
     */
    LOGIN(2, "890607"),
    ;

    private final Integer code;
    private final String templateId;

    public static SmsCodeEnum parse(Integer code) {
        return Arrays.stream(SmsCodeEnum.values())
                .filter(obj -> Objects.equals(obj.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
