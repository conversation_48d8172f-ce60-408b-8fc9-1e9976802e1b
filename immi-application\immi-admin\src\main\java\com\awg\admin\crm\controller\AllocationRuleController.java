package com.awg.admin.crm.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.dto.AllocationRuleDto;
import com.awg.crm.entity.AllocationRule;
import com.awg.crm.service.IAllocationRuleService;
import com.awg.crm.vo.AddClientBusinessVo;
import com.awg.crm.vo.AddRuleVo;
import com.awg.crm.vo.UpdateRuleVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@ApiSupport( order = 40 )
@Api( tags = {"销售分配相关接口"} )
@RestController
@RequestMapping( "/crm/allocation" )
public class AllocationRuleController extends BaseController {

    @Resource
    private IAllocationRuleService allocationRuleService;

    @Resource
    private Redisson redisson;

    @ApiOperation( "规则列表" )
    @ApiOperationSupport(order = 10)
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AllocationRuleDto.class )
    })
    public DataResult queryList(){
        return renderSuccess(allocationRuleService.queryList(getUserLoginInfoEo()));
    }

    @ApiOperation( "添加规则" )
    @ApiOperationSupport(order = 20)
    @PostMapping( "/addRule" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addRule(@RequestBody AddRuleVo vo){
        ValidationUtils.validate(vo);

        Integer ruleNo = null;

        // 开启锁
        RLock lock = redisson.getLock("immiLock:crm:allocation:ruleChange");
        lock.lock();
        try {
            ruleNo = allocationRuleService.addRule(vo, getUserLoginInfoEo());
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessageCode(), e.getDetailMessage());
        } catch (Exception e) {
            // 抛出异常
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), e.getMessage());
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return renderSuccess(ruleNo);
    }

    @ApiOperation( "更新规则" )
    @ApiOperationSupport(order = 30)
    @PostMapping( "/updRule" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updRule(@RequestBody UpdateRuleVo vo){
        ValidationUtils.validate(vo);

        Integer ruleNo = null;

        // 开启锁
        RLock lock = redisson.getLock("immiLock:crm:allocation:ruleChange");
        lock.lock();

        try {
            ruleNo = allocationRuleService.updateRule(vo, getUserLoginInfoEo());
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessageCode(), e.getDetailMessage());
        } catch (Exception e) {
            // 抛出异常
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), e.getMessage());
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return renderSuccess(ruleNo);
    }

    @ApiOperation( "删除规则" )
    @ApiOperationSupport(order = 40)
    @PostMapping( "/delRule/{ruleNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updRule(@PathVariable( value = "ruleNo" ) Integer ruleNo){
        AssertUtils.isTrue(ruleNo <= 0, "编号不合法");
        return renderSuccess(allocationRuleService.delRule(ruleNo, getUserLoginInfoEo()));
    }
}
