package com.awg.comm.service.impl;

import com.awg.comm.dto.PlatformSyncProductDto;
import com.awg.comm.entity.NewProduct;
import com.awg.comm.entity.NewProductData;
import com.awg.comm.entity.Product;
import com.awg.comm.mapper.NewProductMapper;
import com.awg.comm.mapper.ProductMapper;
import com.awg.comm.service.IProductService;
import com.awg.comm.service.NewIProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class NewProductServiceImpl extends ServiceImpl<NewProductMapper, NewProduct> implements NewIProductService {

    @Resource
    NewProductMapper newProductMapper;




    @Override
    public List<PlatformSyncProductDto> getPlatformSyncProducts(Long platformProductNo) {
        return newProductMapper.getPlatformSyncProducts(platformProductNo);
    }

    @Override
    public int updatePlatformSyncVid(String productNo, Integer platformSyncVid) {
        return newProductMapper.updatePlatformSyncVid(productNo, platformSyncVid);
    }
}
