package com.awg.common.exception;

import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.ExceptionLogVo;
import com.awg.common.base.result.DataResult;
import com.awg.utils.random.IdWorker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolationException;
import java.util.Date;

/**
 * <p>
 * <b>ExceptionUtils</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/4/4 15:52
 */
public class ExceptionUtils {

    /**
     * @param e:      s
     * @param result:
     * @description:常见异常信息处理
     * @author: yangqiang
     * @date: 2021/4/4 15:54
     * @return: void
     **/
    public static void exceptionHandler(Exception e, DataResult result) {
        Integer code = result.getCode();
        String message = e.toString();

        //请求方式异常
        if (e instanceof HttpRequestMethodNotSupportedException) {
            HttpRequestMethodNotSupportedException suEx = (HttpRequestMethodNotSupportedException) e;
            message = "该接口不支持该请求方式, 请使用:" + suEx.getSupportedHttpMethods() + "请求";
            code = BaseResponseCode.ILLEGAL_REQUEST.getCode();
        }

        //参数类型异常
        if (e instanceof MethodArgumentTypeMismatchException) {
            MethodArgumentTypeMismatchException tyEx = (MethodArgumentTypeMismatchException) e;
            message = "参数:" + tyEx.getName() + "类型错误";
            code = BaseResponseCode.INVALID_PARAMETERS_TYPE_MISMATCH.getCode();
        }

        //json序列化异常
        if (e instanceof HttpMessageNotReadableException) {
            message = "json转换异常, 请检查参数格式";
            code = BaseResponseCode.INVALID_PARAMETERS.getCode();
        }

        //Validator效验器
        if (e instanceof ConstraintViolationException) {
            ConstraintViolationException cvEx = (ConstraintViolationException) e;
            message = cvEx.getMessage();
            code = BaseResponseCode.INVALID_PARAMETERS.getCode();
        }

        //Validated form参数效验
        if (e instanceof BindException) {
            BindException biEx = (BindException) e;
            message = biEx.getMessage();
            code = BaseResponseCode.INVALID_PARAMETERS.getCode();
        }

        //Validated body参数效验
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException maEx = (MethodArgumentNotValidException) e;
            message = maEx.getMessage();
            code = BaseResponseCode.INVALID_PARAMETERS.getCode();
        }

        //RequestParam 参数效验
        if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException reEx = (MissingServletRequestParameterException) e;
            message = "参数:" + reEx.getParameterName() + "不可为空";
            code = BaseResponseCode.INVALID_PARAMETERS.getCode();
        }
        result.setCode(code);
        result.setMsg(message);
    }

    /**
     * 构建异常日志bean对象，用于存储在redis
     *
     * @param e
     * @return:
     */
    public static ExceptionLogVo inserErrorLog(Exception e, Integer code) {
        //异常类路径
        String classPath = null;
        //异常方法名
        String method = null;
        //异常发生的行号
        int lineNumber = 0;
        for (StackTraceElement stackTraceElement : e.getStackTrace()) {
            String classTemp = stackTraceElement.getClassName();
            if (classTemp.startsWith("com.awg")) {
                classPath = stackTraceElement.getClassName();
                method = stackTraceElement.getMethodName();
                lineNumber = stackTraceElement.getLineNumber();
                break;
            }
        }
        //异常内容
        String msg = StringUtils.isBlank(e.getMessage()) ? e.getLocalizedMessage() : e.getMessage();
        ExceptionLogVo logVo = new ExceptionLogVo();
        logVo.setId(IdWorker.getRandomNo());
        logVo.setClassPath(classPath);
        logVo.setMethod(method);
        logVo.setLineNumber(lineNumber);
        logVo.setContent(msg);
        logVo.setCode(code);
        logVo.setCreateTime(new Date());
        return logVo;
    }
}

