package com.awg.crm.service.impl;

import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.entity.CrmLeadsGovernmentScreenshot;
import com.awg.crm.mapper.CrmLeadsGovernmentMapper;
import com.awg.crm.mapper.CrmLeadsGovernmentScreenshotMapper;
import com.awg.crm.service.CrmLeadsGovernmentScreenshotService;
import com.awg.crm.service.CrmLeadsGovernmentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CrmLeadsGovernmentScreenshotServiceImpl extends ServiceImpl<CrmLeadsGovernmentScreenshotMapper, CrmLeadsGovernmentScreenshot> implements CrmLeadsGovernmentScreenshotService {
    @Override
    public List<CrmLeadsGovernmentScreenshot> listByCrmLeadsGovernmentIds(List<Integer> crmLeadsGovernmentIds, long createdAt) {
        return baseMapper.selectList(
                new LambdaQueryWrapper<CrmLeadsGovernmentScreenshot>()
                        .in(CrmLeadsGovernmentScreenshot::getCrmLeadsGovernmentId, crmLeadsGovernmentIds)
                        .le(CrmLeadsGovernmentScreenshot::getCreatedAt, createdAt)
        );
    }

    @Override
    public List<CrmLeadsGovernmentScreenshot> listByCrmLeadsGovernmentIdsIncludeDeleted(List<Integer> crmLeadsGovernmentIds, long createdAt) {
        return baseMapper.listByCrmLeadsGovernmentIdsIncludeDeleted(crmLeadsGovernmentIds, createdAt);
    }
}
