package com.awg.common.security;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.awg.system.eo.MenuEo;
import com.awg.system.externalService.IUserExternalService;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.constant.ConfigConstant;
import com.awg.common.constant.UserTokenConstant;
import com.awg.common.jwt.JWTUtils;
import com.awg.common.jwt.JwtConstant;
import com.awg.common.jwt.JwtProperties;
import com.awg.common.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * <b>AuthorityUtil</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/10/25 15:09
 */

@Component
@Slf4j
public class AuthorityUtil {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private IUserExternalService userExternalService;

    /**
     * <p>
     * 权限鉴定，鉴权当前登录用户是否有接口权限
     * </p>
     *
     * @param accessToken : token
     * @param uri         : 请求接口uri
     * @author: 夜晓
     * @date: 2021-10-25
     * @return: boolean
     */
    public boolean authentication(String accessToken, String uri) {
        //验证token合法性
        DecodedJWT decodedJwt = JWTUtils.verify(accessToken);

        //token是否失效
        String uid = decodedJwt.getClaim(JwtConstant.UID).asString();
        String timeKey = decodedJwt.getClaim(JwtConstant.TIME_KEY).asString();
        String category = decodedJwt.getClaim(JwtConstant.CATEGORY).asString();
        String hashKey = StringUtils.isEmpty(timeKey) ?
                UserTokenConstant.ADMIN_ONLINE_USER_PREFIX + uid :
                UserTokenConstant.ADMIN_ONLINE_USER_PREFIX + uid + ":" + timeKey ;
        if (!redisUtils.exists(hashKey)) {
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
        }
        Map<Object, Object> userRedisMap = redisUtils.hGetAll(hashKey);
        if (!StringUtils.equals(String.valueOf(userRedisMap.getOrDefault(JwtProperties.TOKEN_KEY, "")), accessToken)) {
            //如果请求token与缓存token信息不一致，代表缓存已更新过，需要重新登录
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
        }

        // 当前机构
        Integer currentOrgId = (Integer) userRedisMap.get("currentOrgId");
        Integer currentOrgType = (Integer) userRedisMap.get("currentOrgType");
        Integer userInfoId = (Integer) userRedisMap.get("userInfoId");

        if(userExternalService.getOrgDisabledFlag(currentOrgId).equals(1)) {
            throw new BusinessException(BaseResponseCode.USER_NAME_ROLE_DISABLE);
        }

        // 读取角色缓存
        Integer roleId = (Integer) userRedisMap.get("roleId");
        Integer isAdmin = 0;
        Integer roleType = -1;
        List<MenuEo> menuList = null;
        if(roleId>0) {
            String roleRedisKey = UserTokenConstant.ADMIN_ONLINE_ROLE_PREFIX + roleId;
            if (!redisUtils.exists(roleRedisKey)) {
                throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
            }
            Map<Object, Object> roleRedisMap = redisUtils.hGetAll(roleRedisKey);
            // 判断是否redis是否为空
            if(CollectionUtils.isEmpty(roleRedisMap)) {
                throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
            }

            isAdmin = (Integer) roleRedisMap.get("isAdmin");
            roleType = (Integer) roleRedisMap.get("roleType");

            // 填入菜单列表
            menuList = (List<MenuEo>) roleRedisMap.get("roleMenuList");
        }

        // 是否需要进行菜单鉴权,使用ConfigConstant.PROTECTED_URI匹配
        Boolean isProtected = false;
        for(String protectedUri : ConfigConstant.PROTECTED_URI) {
            if(uri.equals(protectedUri)){
                isProtected = true;
                break;
            }
        }

        // 受保护的uri只需鉴权登录，所以直接放行
        if(isProtected) {
            return true;
        }

        // 是否平台超管专属的URI
        boolean isPlatformAdminUri = false;
        for(String platformAdminUri : ConfigConstant.PLATFORM_ADMIN_URI) {
            if(uri.equals(platformAdminUri)){
                isPlatformAdminUri = true;
                break;
            }
        }

        // 超管专属uri
        if(isPlatformAdminUri) {
            // 判断是否是超管
            if( isAdmin==1 && roleType.equals(0) ) {
                return true;
//                if(userExternalService.isPlatformAdmin( Integer.parseInt(uid) )) {
//                    return true;
//                }
//                else {
//                    throw new BusinessException(BaseResponseCode.USER_NAME_ROLE_DISABLE);
//                }
            }
            else {
                throw new BusinessException(BaseResponseCode.USER_NAME_ROLE_DISABLE);
            }
        }

        // 角色和菜单没有的话，直接没有权限
        if(roleId<=0 || menuList==null || CollectionUtils.isEmpty(menuList)) {
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
        }

        // 是否有权限
        boolean isExist;
        boolean isProductEditorUri = false;
        Integer productEditorUserFlag = 0;
        isExist = menuList.stream().filter(obj -> obj.getUrl().equals(uri)).count() > 0;
        if(!isExist) {
            List<String> productEditorUriList = Arrays.asList(
                    "/comm/product/list",
                    "/comm/product/schoolApplication/list",
                    "/comm/product/localService/list",
                    "/comm/product/detail/{productNo}",
                    "/comm/product/schoolApplication/detail/{productNo}",
                    "/comm/product/localService/detail/{productNo}",
                    "/comm/product/update",
                    "/comm/product/schoolApplication/update",
                    "/comm/product/localService/update"
            );
            for(String productEditorUri : productEditorUriList) {
                if(uri.equals(productEditorUri)){
                    isProductEditorUri = true;
                    break;
                }
            }
            productEditorUserFlag = userExternalService.getUserProductEditorUserFlag(userInfoId);
            if(!(isProductEditorUri && productEditorUserFlag.equals(1))) {
                throw new BusinessException(BaseResponseCode.USER_NAME_ROLE_DISABLE);
            }
        }

        if(isProductEditorUri && productEditorUserFlag.equals(1)) {
            return true;
        }

        // 找到当前菜单
        MenuEo menu = menuList.stream().filter(obj -> obj.getUrl().equals(uri)).findFirst().get();

        // 菜单是否被禁用
        if(menu.getDisabledFlag()==1) {
            throw new BusinessException(BaseResponseCode.USER_NAME_ROLE_DISABLE);
        }

        // 如果访问的是否平台有的菜单，是的话则要严格鉴权
        if(menu.getPlatformFlag().equals(1) || currentOrgType.equals(3) ) {
            if(!userExternalService.authValidator( Integer.parseInt(uid), uri, currentOrgId, false)) {
                throw new BusinessException(BaseResponseCode.USER_NAME_ROLE_DISABLE);
            }
        }

        return true;
    }

    /**
     * <p>
     * 效验维护期间是否放行
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-11-12
     * @return: boolean
     */
    public boolean isOpen(HttpServletRequest request) {
        boolean isOpen = true;

        String uri = String.valueOf(request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE));

        if (redisUtils.exists(ConfigConstant.CONFIG_SWITCH_MAINTAIN)) {
            String isMaintain = String.valueOf(redisUtils.get(ConfigConstant.CONFIG_SWITCH_MAINTAIN));
            if ("1".equals(isMaintain)) {
                String ip;
                //wechat 请求不处理
                if (!StringUtils.contains(uri, "wechat")) {
                    ip = getIpAddr(request);
                    log.debug("请求IP：" + ip);

                    List<Object> whiteList = redisUtils.lRange(ConfigConstant.CONFIG_IP_WHITE_LIST, 0, redisUtils.lLen(ConfigConstant.CONFIG_IP_WHITE_LIST) - 1);
                    isOpen = whiteList.contains(ip);
                }
            }
        }

        return isOpen;
    }

    /**
     * <p>
     * 获取ip地址
     * </p>
     *
     * @author: 夜晓
     * @date: 2021-11-12
     * @return: java.lang.String
     */
    @SuppressWarnings("all")
    private String getIpAddr(HttpServletRequest request) {

        String ipAddress = request.getHeader("X-Real-IP");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("x-forwarded-for");
        }

        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ipAddress = inet.getHostAddress();
            }
        }

        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress != null && ipAddress.length() > 15) {
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }

        return ipAddress;
    }
}
