package com.awg.crm.service;

import com.awg.crm.entity.CrmLeadsGovernment;
import com.awg.crm.entity.CrmLeadsGovernmentScreenshot;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CrmLeadsGovernmentScreenshotService extends IService<CrmLeadsGovernmentScreenshot> {

    /**
     * 根据政府费id查询所有的图片文件
     * @param crmLeadsGovernmentIds
     * @return
     */
    List<CrmLeadsGovernmentScreenshot> listByCrmLeadsGovernmentIds(List<Integer> crmLeadsGovernmentIds, long createdAt);

    /**
     * 根据政府费id查询所有的图片文件（包含逻辑删除的数据）
     * @param crmLeadsGovernmentIds
     * @param createdAt
     * @return
     */
    List<CrmLeadsGovernmentScreenshot> listByCrmLeadsGovernmentIdsIncludeDeleted(List<Integer> crmLeadsGovernmentIds, long createdAt);
}
