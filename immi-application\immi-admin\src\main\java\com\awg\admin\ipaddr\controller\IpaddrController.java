package com.awg.admin.ipaddr.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.vo.AddAttachmentVo;
import com.awg.ipaddr.eo.AreaInfoEo;
import com.awg.ipaddr.externalService.IIpaddrLocationExternalService;
import com.awg.ipaddr.vo.CompressImageVo;
import com.awg.thirdparty.sdk.cos.TencentCOSUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 40 )
@Api( tags = {"IP地区接口"} )
@RestController
@RequestMapping( "/ipaddr/ip" )
public class IpaddrController extends BaseController {

    @Resource
    private IIpaddrLocationExternalService iIpaddrLocationExternalService;

    @ApiOperation( "获取国家地区树形结构" )
    @PostMapping( "/areaTree" )
    @Authority( AuthorityEnum.NOCHECK )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = AreaInfoEo.class )
    })
    public DataResult areaTree(){
        return renderSuccess(iIpaddrLocationExternalService.getAreaTree());
    }
    
}
