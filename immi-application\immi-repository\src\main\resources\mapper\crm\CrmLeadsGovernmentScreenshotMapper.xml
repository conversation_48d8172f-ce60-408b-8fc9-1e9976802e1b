<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.awg.crm.mapper.CrmLeadsGovernmentScreenshotMapper">

    <!-- 根据政府费id查询所有的图片文件（包含逻辑删除的数据） -->
    <select id="listByCrmLeadsGovernmentIdsIncludeDeleted" resultType="com.awg.crm.entity.CrmLeadsGovernmentScreenshot">
        SELECT 
            id,
            crm_leads_government_id,
            screenshot_path,
            created_at,
            updated_at,
            is_delete
        FROM crm_leads_government_screenshot
        WHERE crm_leads_government_id IN
        <foreach collection="crmLeadsGovernmentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND created_at <![CDATA[<=]]> #{createdAt}
    </select>

</mapper>

