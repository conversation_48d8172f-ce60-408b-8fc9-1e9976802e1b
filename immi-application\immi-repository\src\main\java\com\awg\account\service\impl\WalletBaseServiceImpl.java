package com.awg.account.service.impl;

import com.awg.account.entity.Wallet;
import com.awg.account.entity.WalletTransaction;
import com.awg.account.enums.WalletActionCategoryEnum;
import com.awg.account.enums.WalletActionCodeEnum;
import com.awg.account.eo.ConsumeByWalletEo;
import com.awg.account.mapper.WalletMapper;
import com.awg.account.mapper.WalletTransactionMapper;
import com.awg.account.service.IWalletBaseService;
import com.awg.account.vo.AdjustBalanceVo;
import com.awg.client.externalService.IMemberExternalService;
import com.awg.client.externalService.IMemberValidExternalService;
import com.awg.comm.entity.CommissionInfo;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.exception.AssertUtils;
import com.awg.ord.entity.OrderProduct;
import com.awg.utils.random.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 钱包基础 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class WalletBaseServiceImpl extends ServiceImpl<WalletMapper, Wallet> implements IWalletBaseService {

    @Resource
    private WalletMapper walletMapper;

    @Resource
    private WalletTransactionMapper walletTransactionMapper;

    @Resource
    private IMemberValidExternalService memberValidExternalService;

    @Resource
    private Redisson redisson;

    /**
     * <p>
     * 手动调整钱包余额
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void adjustBalance(AdjustBalanceVo vo, UserLoginInfoEo userLoginInfoEo) {
        AssertUtils.isTrue(StringUtils.isBlank(vo.getWalletNo()), "钱包编号不能为空");

        Wallet wallet = getWalletByWalletNo(vo.getWalletNo());

        // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
        Integer cid = memberValidExternalService.getCidByMemberId(wallet.getMemberId());
        boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
        AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

        // 记录旧余额
        BigDecimal oldBalance = wallet.getBalance();

        // 值不能少于0
        AssertUtils.isTrue(vo.getAmount().compareTo(BigDecimal.ZERO) < 0, "填写金额不能小于0");

        // 记录改变值
        BigDecimal changeValue = BigDecimal.ZERO;
        if(vo.getType().equals(1)) {
            // 增加
            changeValue = vo.getAmount();
        }
        if(vo.getType().equals(2)) {
            // 减少，乘以-1
            changeValue = vo.getAmount().multiply(new BigDecimal(-1));
        }
        // 处理成两位小数
        changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

        // 新的可用金额
        BigDecimal newAvailableBalance = wallet.getAvailableBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newAvailableBalance.compareTo(BigDecimal.ZERO) < 0, "可用余额不能小于0");

        // 新的余额
        BigDecimal newBalance = wallet.getBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newBalance.compareTo(BigDecimal.ZERO) < 0, "余额不能小于0");

        // 流水记录
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setTransactionNo(IdWorker.getRandomLongId());
        walletTransaction.setWalletNo(wallet.getWalletNo());
        walletTransaction.setActionCode(WalletActionCodeEnum.MANUAL_OPERATION.getCode());
        walletTransaction.setActionCategory(WalletActionCategoryEnum.MANUAL_OPERATION.getCode());
        walletTransaction.setBalanceChange(changeValue);
        walletTransaction.setAvailableBalanceChange(changeValue);
        walletTransaction.setBalanceSnapshot(newBalance);
        walletTransaction.setAvailableBalanceSnapshot(newAvailableBalance);
        walletTransaction.setPendingBalanceSnapshot(wallet.getPendingBalance());
        walletTransaction.setRemarks(vo.getRemarks());
        walletTransaction.setOperatorId( Long.valueOf(userLoginInfoEo.getUserInfoId()) );
        walletTransaction.setOperatorType(1);

        walletTransactionMapper.insert(walletTransaction);

        // 更新钱包
        wallet.setAvailableBalance(newAvailableBalance);
        wallet.setBalance(newBalance);
        walletMapper.updateById(wallet);


    }

    /**
     * <p>
     * 注册赠送
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void registerGive(String walletNo) {
        AssertUtils.isTrue(StringUtils.isBlank(walletNo), "钱包编号不能为空");

        Wallet wallet = getWalletByWalletNo(walletNo);

        // 奖励金额为10刀
        BigDecimal giveAmount = new BigDecimal(10);

        // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
        Integer cid = memberValidExternalService.getCidByMemberId(wallet.getMemberId());
        boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
        AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

        // 是否已经赠送过
        List<WalletTransaction> walletTransactionList = walletTransactionMapper.selectList(Wrappers.<WalletTransaction>lambdaQuery()
                .eq(WalletTransaction::getWalletNo, walletNo)
                .eq(WalletTransaction::getActionCode, WalletActionCodeEnum.REGISTER_GIFT.getCode())
                .eq(WalletTransaction::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );
        AssertUtils.isTrue(walletTransactionList.size()>0, "已经获取过注册奖励");

        // 记录旧余额
        BigDecimal oldBalance = wallet.getBalance();

        // 记录改变值
        BigDecimal changeValue = giveAmount;
        // 处理成两位小数
        changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

        // 新的可用金额
        BigDecimal newAvailableBalance = wallet.getAvailableBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newAvailableBalance.compareTo(BigDecimal.ZERO) < 0, "可用余额不能小于0");

        // 新的余额
        BigDecimal newBalance = wallet.getBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newBalance.compareTo(BigDecimal.ZERO) < 0, "余额不能小于0");

        // 流水记录
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setTransactionNo(IdWorker.getRandomLongId());
        walletTransaction.setWalletNo(wallet.getWalletNo());
        walletTransaction.setActionCode(WalletActionCodeEnum.REGISTER_GIFT.getCode());
        walletTransaction.setActionCategory(WalletActionCategoryEnum.SYSTEM_OPERATION.getCode());
        walletTransaction.setBalanceChange(changeValue);
        walletTransaction.setAvailableBalanceChange(changeValue);
        walletTransaction.setBalanceSnapshot(newBalance);
        walletTransaction.setAvailableBalanceSnapshot(newAvailableBalance);
        walletTransaction.setPendingBalanceSnapshot(wallet.getPendingBalance());
        walletTransaction.setRemarks("注册赠送");
        walletTransaction.setOperatorId( 0L );
        walletTransaction.setOperatorType(0);

        walletTransactionMapper.insert(walletTransaction);

        // 更新钱包
        wallet.setAvailableBalance(newAvailableBalance);
        wallet.setBalance(newBalance);
        walletMapper.updateById(wallet);


    }

    /**
     * <p>
     * 使用钱包消费
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void consumeByWallet(ConsumeByWalletEo vo, UserLoginInfoEo userLoginInfoEo) {
        AssertUtils.isTrue(StringUtils.isBlank(vo.getWalletNo()), "钱包编号不能为空");
        AssertUtils.isTrue(StringUtils.isBlank(vo.getOrderNo()), "订单编号不能为空");
        AssertUtils.isTrue(vo.getMemberId()==null, "会员ID不能为空");

        // 钱包抵抗金额必须大于0
        BigDecimal walletDeduction = vo.getWalletDeduction();
        AssertUtils.isTrue( walletDeduction==null || walletDeduction.compareTo(BigDecimal.ZERO) <= 0, "消费金额必须大于0");

        // 找到钱包
        Wallet wallet = getWalletByWalletNo(vo.getWalletNo());
        AssertUtils.isFalse(wallet.getMemberId().equals(vo.getMemberId()), "钱包与会员不匹配");

        // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
        Integer cid = memberValidExternalService.getCidByMemberId(wallet.getMemberId());
        boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
        AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

        // 钱包余额是否足够抵抗（总余额和可用余额都检测）
        AssertUtils.isTrue(wallet.getBalance().compareTo(walletDeduction) < 0, "钱包余额不足");
        AssertUtils.isTrue(wallet.getAvailableBalance().compareTo(walletDeduction) < 0, "钱包可用余额不足");

        // 记录旧余额
        BigDecimal oldBalance = wallet.getBalance();

        // 记录改变值
        BigDecimal changeValue = walletDeduction.multiply(new BigDecimal(-1));
        // 处理成两位小数
        changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

        // 新的可用金额
        BigDecimal newAvailableBalance = wallet.getAvailableBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newAvailableBalance.compareTo(BigDecimal.ZERO) < 0, "可用余额不能小于0");

        // 新的余额
        BigDecimal newBalance = wallet.getBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newBalance.compareTo(BigDecimal.ZERO) < 0, "余额不能小于0");

        // 流水记录
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setTransactionNo(IdWorker.getRandomLongId());
        walletTransaction.setWalletNo(wallet.getWalletNo());
        walletTransaction.setOrderNo( Long.valueOf(vo.getOrderNo()) );
        walletTransaction.setActionCode(WalletActionCodeEnum.BUY_PRODUCT.getCode());
        walletTransaction.setActionCategory(WalletActionCategoryEnum.CONSUME.getCode());

        walletTransaction.setBalanceChange(changeValue);
        walletTransaction.setAvailableBalanceChange(changeValue);
        walletTransaction.setBalanceSnapshot(newBalance);
        walletTransaction.setAvailableBalanceSnapshot(newAvailableBalance);
        walletTransaction.setPendingBalanceSnapshot(wallet.getPendingBalance());
        walletTransaction.setRemarks(vo.getRemarks()==null?"":vo.getRemarks());

        if(userLoginInfoEo==null){
            // 会员操作
            walletTransaction.setOperatorId( Long.valueOf(vo.getMemberId()) );
            walletTransaction.setOperatorType(2);
        }
        else {
            // 管理员操作
            walletTransaction.setOperatorId( Long.valueOf(userLoginInfoEo.getUserInfoId()) );
            walletTransaction.setOperatorType(1);
        }

        walletTransactionMapper.insert(walletTransaction);

        // 更新钱包
        wallet.setAvailableBalance(newAvailableBalance);
        wallet.setBalance(newBalance);
        walletMapper.updateById(wallet);


    }

    /**
     * <p>
     * 订单返佣
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void orderCommission( BigDecimal commission, Integer memberId, String orderNo, boolean isSelf ) {

        AssertUtils.isTrue(memberId==null || memberId<=0, "会员ID不能为空");
        AssertUtils.isTrue(commission==null || commission.compareTo(BigDecimal.ZERO) < 0, "返佣金额必须大于0");

        // 获取会员钱包
        Wallet wallet = getMemberWallet(memberId);
        AssertUtils.isTrue(wallet==null, "会员钱包不存在");
        AssertUtils.isFalse(wallet.getMemberId().equals(memberId), "钱包与会员不匹配");
        String walletNo = wallet.getWalletNo().toString();

        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
        Integer cid = memberValidExternalService.getCidByMemberId(wallet.getMemberId());
        boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
        AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

        // 记录旧余额
        BigDecimal oldBalance = wallet.getBalance();

        // 记录改变值
        BigDecimal changeValue = commission;
        // 处理成两位小数
        changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

        // 新的待到账金额
        BigDecimal newPendingBalance = wallet.getPendingBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newPendingBalance.compareTo(BigDecimal.ZERO) < 0, "待到账金额不能小于0");

        // 新的余额
        BigDecimal newBalance = wallet.getBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newBalance.compareTo(BigDecimal.ZERO) < 0, "余额不能小于0");

        // 流水记录
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setTransactionNo(IdWorker.getRandomLongId());
        walletTransaction.setWalletNo(wallet.getWalletNo());
        walletTransaction.setOrderNo( Long.valueOf(orderNo) );
        walletTransaction.setActionCode( isSelf ? WalletActionCodeEnum.ORDER_CASHBACK.getCode() : WalletActionCodeEnum.ORDER_COMMISSION.getCode());
        walletTransaction.setActionCategory(WalletActionCategoryEnum.COMMISSION.getCode());

        walletTransaction.setBalanceChange(changeValue);
        walletTransaction.setPendingBalanceChange(changeValue);
        walletTransaction.setBalanceSnapshot(newBalance);
        walletTransaction.setAvailableBalanceSnapshot(wallet.getAvailableBalance());
        walletTransaction.setPendingBalanceSnapshot(newPendingBalance);
        walletTransaction.setRemarks("");

        walletTransaction.setOperatorId(0L);
        walletTransaction.setOperatorType(0);

        walletTransactionMapper.insert(walletTransaction);

        // 更新钱包
        wallet.setPendingBalance(newPendingBalance);
        wallet.setBalance(newBalance);
        walletMapper.updateById(wallet);
    }

    /**
     * <p>
     * 订单返还
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public void orderRefund(MemberLoginInfoEo loginInfoEo, BigDecimal refundAmount, Integer memberId, String orderNo, UserLoginInfoEo userLoginInfoEo ) {

        AssertUtils.isTrue(memberId==null || memberId<=0, "会员ID不能为空");
        AssertUtils.isTrue(refundAmount==null || refundAmount.compareTo(BigDecimal.ZERO) < 0, "退款金额必须大于0");

        // 获取会员钱包
        Wallet wallet = getMemberWallet(memberId);
        AssertUtils.isTrue(wallet==null, "会员钱包不存在");
        AssertUtils.isFalse(wallet.getMemberId().equals(memberId), "钱包与会员不匹配");
        String walletNo = wallet.getWalletNo().toString();

        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
        Integer cid = memberValidExternalService.getCidByMemberId(wallet.getMemberId());
        boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
        AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

        // 记录旧余额
        BigDecimal oldBalance = wallet.getBalance();

        // 记录改变值
        BigDecimal changeValue = refundAmount;
        // 处理成两位小数
        changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

        // 新的可用金额
        BigDecimal newAvailableBalance = wallet.getAvailableBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newAvailableBalance.compareTo(BigDecimal.ZERO) < 0, "可用余额不能小于0");

        // 新的余额
        BigDecimal newBalance = wallet.getBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
        AssertUtils.isTrue(newBalance.compareTo(BigDecimal.ZERO) < 0, "余额不能小于0");

        // 流水记录
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setTransactionNo(IdWorker.getRandomLongId());
        walletTransaction.setWalletNo(wallet.getWalletNo());
        walletTransaction.setOrderNo( Long.valueOf(orderNo) );
        walletTransaction.setActionCode(WalletActionCodeEnum.ORDER_REFUND.getCode());
        walletTransaction.setActionCategory(WalletActionCategoryEnum.REFUND.getCode());

        walletTransaction.setBalanceChange(changeValue);
        walletTransaction.setAvailableBalanceChange(changeValue);
        walletTransaction.setBalanceSnapshot(newBalance);
        walletTransaction.setAvailableBalanceSnapshot(newAvailableBalance);
        walletTransaction.setPendingBalanceSnapshot(wallet.getPendingBalance());
        walletTransaction.setRemarks("");

        if(userLoginInfoEo!=null){
            // 管理员操作
            walletTransaction.setOperatorId( Long.valueOf(userLoginInfoEo.getUserInfoId()) );
            walletTransaction.setOperatorType(1);
        }
        else if(loginInfoEo!=null){
            // 会员操作
            walletTransaction.setOperatorId( Long.valueOf(loginInfoEo.getMemberId()) );
            walletTransaction.setOperatorType(2);
        }
        else {
            // 系统操作
            walletTransaction.setOperatorId(0L);
            walletTransaction.setOperatorType(0);
        }

        walletTransactionMapper.insert(walletTransaction);

        // 更新钱包
        wallet.setAvailableBalance(newAvailableBalance);
        wallet.setBalance(newBalance);
        walletMapper.updateById(wallet);
    }

    /**
     * <p>
     * 订单返佣金额到账
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public List<Integer> orderCommissionArrival(UserLoginInfoEo userLoginInfoEo, String orderNo ) {
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // getActionCode 可以是 ORDER_CASHBACK 或者 ORDER_COMMISSION
        List<Integer> actionCodeList = new ArrayList<>();
        actionCodeList.add(WalletActionCodeEnum.ORDER_CASHBACK.getCode());
        actionCodeList.add(WalletActionCodeEnum.ORDER_COMMISSION.getCode());

        // 找出当时返佣的流水
        List<WalletTransaction> walletTransactionList = walletTransactionMapper.selectList(Wrappers.<WalletTransaction>lambdaQuery()
                .eq(WalletTransaction::getOrderNo, orderNo)
                .in(WalletTransaction::getActionCode, actionCodeList)
                .eq(WalletTransaction::getActionCategory, WalletActionCategoryEnum.COMMISSION.getCode())
                .eq(WalletTransaction::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByDesc(WalletTransaction::getId)
        );

        // 准备返回会员列表
        List<Integer> memberIdList = new ArrayList<>();

        // 循环到账
        for( WalletTransaction walletTransaction : walletTransactionList ){
            // 获取会员钱包
            Wallet wallet = getWalletByWalletNo(walletTransaction.getWalletNo().toString());
            AssertUtils.isTrue(wallet==null, "会员钱包不存在");

            Integer memberId = wallet.getMemberId();

            // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
            Integer cid = memberValidExternalService.getCidByMemberId(memberId);
            boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
            AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

            // 记录改变值
            BigDecimal changeValue = walletTransaction.getPendingBalanceChange();
            // 处理成两位小数
            changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

            // 必须大于0
            AssertUtils.isTrue( changeValue==null || changeValue.compareTo(BigDecimal.ZERO) < 0, "佣金金额异常");

            // 还需要一个负的改变值
            BigDecimal subChangeValue = changeValue.multiply(new BigDecimal(-1));
            subChangeValue = subChangeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

            // 记录旧余额
            BigDecimal oldBalance = wallet.getBalance();

            // 新的可用余额
            BigDecimal newAvailableBalance = wallet.getAvailableBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
            AssertUtils.isTrue(newAvailableBalance.compareTo(BigDecimal.ZERO) < 0, "可用余额不能小于0");

            // 新的待到账金额，是负的改变值
            BigDecimal newPendingBalance = wallet.getPendingBalance().subtract(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
            AssertUtils.isTrue(newPendingBalance.compareTo(BigDecimal.ZERO) < 0, "待到账金额不能小于0");

            // 流水记录
            WalletTransaction walletTransactionNew = new WalletTransaction();
            walletTransactionNew.setTransactionNo(IdWorker.getRandomLongId());
            walletTransactionNew.setWalletNo(wallet.getWalletNo());
            walletTransactionNew.setOrderNo( Long.valueOf(orderNo) );

            if(walletTransaction.getActionCode().equals(WalletActionCodeEnum.ORDER_COMMISSION.getCode())){
                walletTransactionNew.setActionCode(WalletActionCodeEnum.ORDER_COMMISSION_ARRIVAL.getCode());
            }
            if(walletTransaction.getActionCode().equals(WalletActionCodeEnum.ORDER_CASHBACK.getCode())){
                walletTransactionNew.setActionCode(WalletActionCodeEnum.ORDER_CASHBACK_ARRIVAL.getCode());
            }

            walletTransactionNew.setActionCategory(WalletActionCategoryEnum.AMOUNT_ARRIVAL.getCode());

            walletTransactionNew.setAvailableBalanceChange(changeValue);
            walletTransactionNew.setPendingBalanceChange(subChangeValue);
            walletTransactionNew.setBalanceSnapshot(wallet.getBalance());
            walletTransactionNew.setAvailableBalanceSnapshot(newAvailableBalance);
            walletTransactionNew.setPendingBalanceSnapshot(newPendingBalance);
            walletTransactionNew.setRemarks("");

            if(userLoginInfoEo!=null){
                // 管理员操作
                walletTransactionNew.setOperatorId( Long.valueOf(userLoginInfoEo.getUserInfoId()) );
                walletTransactionNew.setOperatorType(1);
            }
            else {
                // 系统操作
                walletTransactionNew.setOperatorId(0L);
                walletTransactionNew.setOperatorType(0);
            }

            walletTransactionMapper.insert(walletTransactionNew);

            // 更新钱包
            wallet.setAvailableBalance(newAvailableBalance);
            wallet.setPendingBalance(newPendingBalance);
            walletMapper.updateById(wallet);

            memberIdList.add(memberId);
        }
        
        return memberIdList;
    }

    /**
     * <p>
     * 订单返佣金额作废
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public List<Integer> orderCommissionVoid(UserLoginInfoEo userLoginInfoEo, String orderNo, String remarks ) {
        AssertUtils.isTrue(StringUtils.isBlank(orderNo), "订单编号不能为空");

        // getActionCode 可以是 ORDER_CASHBACK 或者 ORDER_COMMISSION
        List<Integer> actionCodeList = new ArrayList<>();
        actionCodeList.add(WalletActionCodeEnum.ORDER_CASHBACK.getCode());
        actionCodeList.add(WalletActionCodeEnum.ORDER_COMMISSION.getCode());

        // 找出当时返佣的流水
        List<WalletTransaction> walletTransactionList = walletTransactionMapper.selectList(Wrappers.<WalletTransaction>lambdaQuery()
                .eq(WalletTransaction::getOrderNo, orderNo)
                .in(WalletTransaction::getActionCode, actionCodeList)
                .eq(WalletTransaction::getActionCategory, WalletActionCategoryEnum.COMMISSION.getCode())
                .eq(WalletTransaction::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .orderByDesc(WalletTransaction::getId)
        );

        // 准备会员列表
        List<Integer> memberIdList = new ArrayList<>();

        // 循环作废
        for( WalletTransaction walletTransaction : walletTransactionList ){
            // 获取会员钱包
            Wallet wallet = getWalletByWalletNo(walletTransaction.getWalletNo().toString());
            AssertUtils.isTrue(wallet==null, "会员钱包不存在");

            Integer memberId = wallet.getMemberId();

            // 检测会员的有效性【目前的情况下，C端有效的话，就判断为有效了】
            Integer cid = memberValidExternalService.getCidByMemberId(memberId);
            boolean validFlag = memberValidExternalService.checkConsumerIdentityValid(cid);
            AssertUtils.isTrue(!validFlag, "会员无效或者已经被禁用");

            // 记录改变值
            BigDecimal changeValue = walletTransaction.getPendingBalanceChange();
            // 处理成两位小数
            changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

            // 必须大于0
            AssertUtils.isTrue( changeValue==null || changeValue.compareTo(BigDecimal.ZERO) < 0, "佣金金额异常");

            // 变成负值
            changeValue = changeValue.multiply(new BigDecimal(-1));
            changeValue = changeValue.setScale(2, BigDecimal.ROUND_HALF_UP);

            // 记录旧余额
            BigDecimal oldBalance = wallet.getBalance();

            // 新的待到账金额，扣回来
            BigDecimal newPendingBalance = wallet.getPendingBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
            AssertUtils.isTrue(newPendingBalance.compareTo(BigDecimal.ZERO) < 0, "待到账金额不能小于0");

            // 新的余额
            BigDecimal newBalance = wallet.getBalance().add(changeValue).setScale(2, BigDecimal.ROUND_HALF_UP);
            AssertUtils.isTrue(newBalance.compareTo(BigDecimal.ZERO) < 0, "余额不能小于0");

            // 流水记录
            WalletTransaction walletTransactionNew = new WalletTransaction();
            walletTransactionNew.setTransactionNo(IdWorker.getRandomLongId());
            walletTransactionNew.setWalletNo(wallet.getWalletNo());
            walletTransactionNew.setOrderNo( Long.valueOf(orderNo) );

            if(walletTransaction.getActionCode().equals(WalletActionCodeEnum.ORDER_COMMISSION.getCode())){
                walletTransactionNew.setActionCode(WalletActionCodeEnum.ORDER_COMMISSION_VOID.getCode());
            }
            if(walletTransaction.getActionCode().equals(WalletActionCodeEnum.ORDER_CASHBACK.getCode())){
                walletTransactionNew.setActionCode(WalletActionCodeEnum.ORDER_CASHBACK_VOID.getCode());
            }

            walletTransactionNew.setActionCategory(WalletActionCategoryEnum.COMMISSION.getCode());

            walletTransactionNew.setBalanceChange(changeValue);
            walletTransactionNew.setPendingBalanceChange(changeValue);
            walletTransactionNew.setBalanceSnapshot(newBalance);
            walletTransactionNew.setAvailableBalanceSnapshot(wallet.getAvailableBalance());
            walletTransactionNew.setPendingBalanceSnapshot(newPendingBalance);
            walletTransactionNew.setRemarks(remarks);

            if(userLoginInfoEo!=null){
                // 管理员操作
                walletTransactionNew.setOperatorId( Long.valueOf(userLoginInfoEo.getUserInfoId()) );
                walletTransactionNew.setOperatorType(1);
            }
            else {
                // 系统操作
                walletTransactionNew.setOperatorId(0L);
                walletTransactionNew.setOperatorType(0);
            }

            walletTransactionMapper.insert(walletTransactionNew);

            // 更新钱包
            wallet.setBalance(newBalance);
            wallet.setPendingBalance(newPendingBalance);
            walletMapper.updateById(wallet);

            memberIdList.add(memberId);
        }

        return memberIdList;
    }

    /**
     * <p>
     * 根据钱包编号找出对应钱包
     * </p>
     *
     * @return:
     */
    @Override
    public Wallet getWalletByWalletNo(String walletNo) {
        Wallet wallet = walletMapper.selectOne(Wrappers.<Wallet>lambdaQuery()
                .eq(Wallet::getWalletNo, walletNo)
                .eq(Wallet::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        return wallet;
    }

    /**
     * <p>
     * 获取会员钱包
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class )
    public Wallet getMemberWallet(Integer memberId) {
        AssertUtils.isTrue(memberId == null || memberId<=0, "该用户不存在");

        Wallet wallet = null;

        // 开启锁
        RLock lock = redisson.getLock("immiLock:account:wallet:getMemberWallet");
        lock.lock();
        try {
            wallet = getMemberWalletSubMethod(memberId);
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessageCode(), e.getDetailMessage());
        } catch (Exception e) {
            // 抛出异常
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), e.getMessage());
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        AssertUtils.isNull(wallet, "获取钱包账户失败");

        return wallet;
    }

    /**
     * <p>
     * 获取会员钱包，分布式锁里的方法
     * </p>
     *
     * @return:
     */
    @Override
    @Transactional( rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW )
    public Wallet getMemberWalletSubMethod(Integer memberId) {

        AssertUtils.isTrue(memberId == null || memberId<=0, "该用户不存在");

        Wallet wallet = walletMapper.selectOne(Wrappers.<Wallet>lambdaQuery()
                .eq(Wallet::getMemberId, memberId)
                .eq(Wallet::getIsDelete, TrueFalseEnum.FALSE.getCode())
        );

        // 不存在则创建钱包
        if(wallet == null) {
            wallet = new Wallet();
            wallet.setMemberId(memberId);
            wallet.setBalance(BigDecimal.ZERO);
            wallet.setAvailableBalance(BigDecimal.ZERO);
            wallet.setPendingBalance(BigDecimal.ZERO);
            wallet.setWalletNo(IdWorker.getRandomLongId());

            walletMapper.insert(wallet);
        }

        return wallet;
    }
}
