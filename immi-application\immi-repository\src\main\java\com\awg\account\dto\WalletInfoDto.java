package com.awg.account.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * <b>WalletInfoDto</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel(value = "钱包信息")
public class WalletInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "钱包编号")
    private String walletNo;

    @ApiModelProperty(value = "余额")
    private BigDecimal balance = BigDecimal.ZERO;

    @ApiModelProperty(value = "可用余额")
    private BigDecimal availableBalance = BigDecimal.ZERO;

    @ApiModelProperty(value = "待到账金额")
    private BigDecimal pendingBalance = BigDecimal.ZERO;
}
