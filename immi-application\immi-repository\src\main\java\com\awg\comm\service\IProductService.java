package com.awg.comm.service;

import com.awg.comm.dto.*;
import com.awg.comm.entity.Product;
import com.awg.comm.entity.ProductKeyword;
import com.awg.comm.entity.ProductSynchronization;
import com.awg.comm.vo.*;
import com.awg.common.base.eo.MemberLoginInfoEo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.base.page.BasePageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
public interface IProductService extends IService<Product> {


    /**
     * <p>
     * 获取商品列表
     * </p>
     *
     * @param vo 查询条件
     * @param userLoginInfoEo 用户登录信息
     * @return 商品列表
     */
    BasePageResult<ProductItemDto> getProductList(QueryProductVo vo, UserLoginInfoEo userLoginInfoEo, Integer category);

    Integer getOnlyProductEditorFlag(Integer userInfoId, String uri, Integer orgId);

    boolean checkProductPermission(Integer userInfoId, String uri, Integer orgId, String productNo);

    /**
     * <p>
     * 获取商品选择列表
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    BasePageResult<ProductSelectDto> getProductSelectList(QueryProductVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 获取商品列表(迷你版，小程序端)
     * </p>
     *
     * @param vo 查询条件
     * @return 商品列表
     */
    BasePageResult<ProductMinDto> getProductMiniList(QueryProductVo vo);

    /**
     * <p>
     * 获取商品详情
     * </p>
     *
     * @param productNo 商品编号
     * @param userLoginInfoEo 用户登录信息
     * @return 商品详情
     */
    ProductInfoDto getProductDetail(String productNo, UserLoginInfoEo userLoginInfoEo, ProductDetailVo productDetailVo, boolean isLogin,
                                    Integer productVid, Integer category, boolean checkDelete);


    /**
     * 申校商品查看平台最新内容
     * @param productNo
     * @param userLoginInfoEo
     * @return
     */
    ProductCompareResultVo getProductAndPlatformDetail(String productNo, UserLoginInfoEo userLoginInfoEo);


    /**
     * <p>
     * 获取商品详情-原始信息
     * </p>
     *
     * @param productNo 商品编号
     * @param userLoginInfoEo 用户登录信息
     * @return 商品详情
     */
    ProductInfoDto getProductRawDetail(String productNo, UserLoginInfoEo userLoginInfoEo, ProductDetailVo productDetailVo, boolean isLogin,
                                       Integer productVid, Integer category, Integer watermarkOrgId, boolean checkDelete);

    /**
     * <p>
     * 获取商品详情(迷你版，小程序端)
     * </p>
     *
     * @param productNo 商品编号
     * @return 商品详情
     */
    ProductInfoMiniDto getProductDetailMini(String productNo, boolean isLogin, Integer productVid, Integer category);

    /**
     * <p>
     * 获取商品赠送优惠券信息
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    List<GiftCouponInfoDto> giftCouponInfo(String productNo, Integer category);

    /**
     * <p>
     * 获取商品小程序码
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    String getProductQrcodeUrl (GetProductQrcodeVo vo);

    /**
     * <p>
     * 创建商品信息
     * </p>
     *
     * @param vo 商品信息
     * @param userLoginInfoEo 用户登录信息
     * @return 商品编号
     */
    String createProduct (ProductVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 更新商品信息
     * </p>
     *
     * @param vo 商品信息
     * @param userLoginInfoEo 用户登录信息
     * @return 商品编号
     */
    String updateProduct (ProductVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 刷新商品材料
     * </p>
     *
     * @param productNo 商品编号
     */
    void refreshProductMaterial(String productNo, String relatedBusinessNo);

    /**
     * <p>
     * 填充商品数据
     * </p>
     *
     * @param vo 商品信息
     * @param product 商品
     * @param userLoginInfoEo 用户登录信息
     * @return 商品版本id
     */
    Integer fillProductData (ProductVo vo, Product product, UserLoginInfoEo userLoginInfoEo, boolean isUpdate);

    /**
     * <p>
     * 删除商品信息
     * </p>
     *
     * @param productNo 商品编号
     * @param userLoginInfoEo 用户登录信息
     * @return 商品编号
     */
    String deleteProduct (String productNo, UserLoginInfoEo userLoginInfoEo, Integer category);

    /**
     * <p>
     * 商品排序
     * </p>
     *
     * @param vo 商品排序信息
     * @param userLoginInfoEo 用户登录信息
     */
    void sortProduct(ProductSortVo vo, UserLoginInfoEo userLoginInfoEo, Integer category);

    /**
     * <p>
     * 商品状态变更
     * </p>
     *
     * @param vo 商品状态变更信息
     * @param userLoginInfoEo 用户登录信息
     * @return 商品编号
     */
    String ProductStatusChange (ProductStatusChangeVo vo, UserLoginInfoEo userLoginInfoEo, Integer category);

    /**
     * <p>
     * 获取全部关键词
     * </p>
     *
     * @return:
     */
    List<ProductKeyword> getProductKeywordAll();

    /**
     * <p>
     * 分配文案
     * </p>
     *
     * @param productNo 商品编号
     * @param orderNo 订单编号
     */
    void assignCopyWriter (String productNo, String orderNo, Integer memberId);

    /**
     * <p>
     * 获取文案id
     * </p>
     *
     * @param orderNo 订单编号
     * @return 文案id
     */
    Integer getCopyWriterIdByOrderNo (String orderNo);

    /**
     * <p>
     * 材料库列表
     * </p>
     *
     * @author: lun
     * @date: 2023-05-10
     */
    BasePageResult<MaterialDto> materialLibraryList(QueryMaterialVo vo, Integer watermarkOrgId);

    /**
     * <p>
     * 材料清单库添加
     * </p>
     *
     * @return:
     */
    void materialLibraryAdd(MaterialVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 材料清单库编辑
     * </p>
     *
     * @return:
     */
    void materialLibraryUpdate(MaterialVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 材料清单库删除
     * </p>
     *
     * @return:
     */
    String materialLibraryDelete (String materialNo, UserLoginInfoEo userLoginInfoEo);

    /**
     * <p>
     * 材料清单库排序
     * </p>
     *
     * @return:
     */
    void materialLibrarySort(ProductSortVo vo, UserLoginInfoEo userLoginInfoEo);

    /**
     * 商品同步
     * @param productNo
     * @param userLoginInfoEo
     */
    void synchronizeProduct(String productNo, UserLoginInfoEo userLoginInfoEo, String type);

    /**
     * 批量商品同步
     * @param batchSyncVo 批量同步请求参数
     * @param userLoginInfoEo 用户登录信息
     */
    BatchSyncResultVo batchSynchronizeProduct(BatchSynchronizeProductVo batchSyncVo, UserLoginInfoEo userLoginInfoEo);

    /**
     * 商品同步数据
     * @param productInfoDto
     * @param productNo
     * @param productSynchronization
     */
    void synchronizeProduct(ProductInfoDto productInfoDto, String productNo, ProductSynchronization productSynchronization);
}
