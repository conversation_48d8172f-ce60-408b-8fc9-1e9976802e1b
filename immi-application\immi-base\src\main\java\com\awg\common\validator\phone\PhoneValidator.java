package com.awg.common.validator.phone;

import com.awg.utils.check.EmailCheckUtil;
import com.awg.utils.check.MobilePhoneCheckUtils;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <p>
 * <b>PhoneValidator</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/4/22 15:48
 */

public class PhoneValidator implements ConstraintValidator<ValidationPhone, String> {


    @Override
    public void initialize(ValidationPhone constraintAnnotation) {

    }

    @Override
    public boolean isValid(String phone, ConstraintValidatorContext context) {
        if (StringUtils.isEmpty(phone)) {
            context.buildConstraintViolationWithTemplate("手机号不可为空").addConstraintViolation();
            return false;
        }

        return MobilePhoneCheckUtils.checkPhone(phone);
    }
}
