package com.awg.admin.wechat.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.page.BasePageVo;
import com.awg.common.base.result.DataResult;
import com.awg.common.base.result.EntryResult;
import com.awg.common.validator.ValidationUtils;
import com.awg.wechat.dto.AuditRecordDto;
import com.awg.wechat.dto.AuthorizerAppletDto;
import com.awg.wechat.service.IAppletAuthorizerService;
import com.awg.wechat.service.IWechatOrderService;
import com.awg.wechat.utils.WeChatUtil;
import com.awg.wechat.vo.SubmitAuditVo;
import com.awg.wechat.vo.UploadCodeVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 377 )
@Api( tags = {"小程序管理相关接口"} )
@RestController
@RequestMapping( "/wechat/applet" )
@Slf4j
public class AppletAuthorizerController extends BaseController {

    @Resource
    private IAppletAuthorizerService appletAuthorizerService;

    @Resource
    private WeChatUtil weChatUtil;

    @ApiOperation( "生成授权url" )
    @GetMapping( "/generate/authorize/url" )
    public DataResult generateAuthorizeUrl() {
        // 获取预授权码
        String code = weChatUtil.getPreAuthCode();

        return renderSuccess(weChatUtil.generateAuthUrl(code,
                "https://admin.immiclick.com/organization/miniProgramBinding",
                2));
    }

    @ApiOperation ( "授权绑定接口" )
    @RequestMapping ( value = "/notify", method = {RequestMethod.GET, RequestMethod.POST} )
    public DataResult subscribeNotify(@RequestParam( value = "orgId" ) Integer orgId,
                                      @RequestParam ( value = "auth_code" ) String authCode) {
        System.out.println("============ applet authorizer notify ============");

        // 拉取授权信息, 并与用户进行绑定
        Map<String, Object> authInfo = weChatUtil.saveAuthInfo(authCode);
        String authorizerAppId = String.valueOf(authInfo.get("authorizer_appid"));
        appletAuthorizerService.binding(authorizerAppId, getCurrentOrgId());

        return renderSuccess("success");
    }

    @ApiOperation ( "设置小程序用户隐私指引" )
    @ApiImplicitParams( value = {
            @ApiImplicitParam ( name = "appId", value = "小程序appId", paramType = "String" ),
            @ApiImplicitParam ( name = "privacyType", value = "隐私类型 1：现网版本 2：开发版（该字段非必填，默认视为开发版）", paramType = "String" )
    } )
    @GetMapping ( "/set/privacy/setting" )
    public DataResult setPrivacySetting(@RequestParam ( value = "appId" ) String appId,
                                        @RequestParam ( value = "privacyType", required = false ) Integer privacyType) {
        if (privacyType != null && !(privacyType == 1 || privacyType == 2)) {
            throw new BusinessException(BaseResponseCode.INVALID_PARAMETERS.getCode(), "不合法的隐私类型");
        }

        weChatUtil.setPrivacySetting(appId, privacyType);
        return renderSuccess();
    }

    @ApiOperation ( "设置小程序服务器域名" )
    @DynamicParameters( name = "modifyDomainParam", properties = {
            @DynamicParameter ( name = "requestdomain", value = "request 合法域名", required = true, dataTypeClass = String[].class ),
            @DynamicParameter ( name = "wsrequestdomain", value = "socket 合法域名", required = true, dataTypeClass = String[].class ),
            @DynamicParameter ( name = "uploaddomain", value = "uploadFile 合法域名", required = true, dataTypeClass = String[].class ),
            @DynamicParameter ( name = "downloaddomain", value = "downloadFile 合法域名", required = true, dataTypeClass = String[].class ),
            @DynamicParameter ( name = "udpdomain", value = "udp 合法域名", required = true, dataTypeClass = String[].class ),
            @DynamicParameter( name = "tcpdomain", value = "tcp 合法域名", required = true, dataTypeClass = String[].class )
    } )
    @PostMapping ( "/modify/domain/{appId}" )
    public DataResult modifyDomain(@PathVariable ( value = "appId" ) String appId,
                                   @RequestBody Map<String, Object> modifyDomainParam) {
        weChatUtil.modifyDomain(appId, modifyDomainParam);
        return renderSuccess();
    }

    @ApiOperation ( "设置小程序业务域名" )
    @DynamicParameters ( name = "webViewDomainParam", properties = {
            @DynamicParameter ( name = "webviewdomain", value = "小程序合法域名", required = true, dataTypeClass = String[].class )
    } )
    @PostMapping ( "/set/web/view/domain/{appId}" )
    public DataResult setWebViewDomain(@PathVariable ( value = "appId" ) String appId,
                                       @RequestBody Map<String, Object> webViewDomainParam) {
        weChatUtil.setWebViewDomain(appId, webViewDomainParam);
        return renderSuccess();
    }

    @ApiOperation ( "获取授权小程序列表" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用成功", response = AuthorizerAppletDto.class ),
            @ApiResponse ( code = 200, message = "接口调用成功", response = AuditRecordDto.class )
    } )
    @GetMapping ( "list" )
    public DataResult getAuthorizerApplets(BasePageVo vo) {
        ValidationUtils.validate(vo);
        BasePageResult<AuthorizerAppletDto> result = appletAuthorizerService.getAuthorizerApplets(vo);
        return renderSuccess(result);
    }

    @ApiOperation ( "获取授权小程序详情" )
    @GetMapping ( "/info/{orgId}" )
    public DataResult getAuthorizerAppletInfo(@PathVariable ( value = "orgId" ) Integer orgId) {
        AuthorizerAppletDto result = appletAuthorizerService.getAuthorizerAppletInfo(orgId);
        return renderSuccess(result);
    }

    @ApiOperation ( "上传代码" )
    @PostMapping ( "/upload/code" )
    public DataResult uploadCode(@RequestBody UploadCodeVo vo) {
        ValidationUtils.validate(vo);
        List<EntryResult> result = appletAuthorizerService.uploadCode(vo);
        return renderSuccess(result);
    }

    @ApiOperation ( "提交审核" )
    @PostMapping ( "/submit/audit" )
    public DataResult submitAudit(@RequestBody SubmitAuditVo vo) {
        ValidationUtils.validate(vo);
        List<EntryResult> result = appletAuthorizerService.submitAudit(vo);
        return renderSuccess(result);
    }

    @ApiOperation ( "撤销审核记录" )
    @ApiImplicitParams ( value = {
            @ApiImplicitParam ( name = "appId", value = "小程序appId", paramType = "String" ),
            @ApiImplicitParam ( name = "auditId", value = "审核id", paramType = "String" )
    } )
    @GetMapping ( "/undo/code/audit" )
    public DataResult undoCodeAudit(@RequestParam ( value = "appId" ) String appId,
                                    @RequestParam ( value = "auditId" ) String auditId) {
        appletAuthorizerService.undoCodeAudit(appId, auditId);
        return renderSuccess();
    }

    @ApiOperation ( "发布通过审核的小程序" )
    @PostMapping ( "/release" )
    public DataResult release(@RequestBody SubmitAuditVo vo) {
        ValidationUtils.validate(vo);
        List<EntryResult> result = appletAuthorizerService.release(vo);
        return renderSuccess(result);
    }

    @ApiOperation ( "获取审核结果" )
    @ApiImplicitParams ( value = {
            @ApiImplicitParam ( name = "appId", value = "小程序appId", paramType = "String" ),
            @ApiImplicitParam ( name = "auditId", value = "审核id", paramType = "String" )
    } )
    @GetMapping ( "/audit/status" )
    public DataResult getAuditStatus(@RequestParam ( value = "appId" ) String appId,
                                     @RequestParam ( value = "auditId" ) String auditId) {
        Map<String, Object> result = appletAuthorizerService.getAuditStatus(appId, auditId);
        return renderSuccess(result);
    }

    @ApiOperation ( "获取备案信息" )
    @ApiImplicitParams ( value = {
            @ApiImplicitParam ( name = "appId", value = "小程序appId", paramType = "String" )
    } )
    @GetMapping ( "/icpEntranceInfo" )
    public DataResult getIcpEntranceInfo(@RequestParam ( value = "appId" ) String appId) {
        Map<String, Object> result = appletAuthorizerService.getIcpEntranceInfo(appId);
        return renderSuccess(result);
    }

}
