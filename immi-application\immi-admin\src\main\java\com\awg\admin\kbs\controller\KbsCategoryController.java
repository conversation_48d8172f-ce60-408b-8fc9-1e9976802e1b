package com.awg.admin.kbs.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.kbs.dto.CategoryDto;
import com.awg.kbs.dto.CategorySelectDto;
import com.awg.kbs.service.IKbsCategoryService;
import com.awg.kbs.vo.AddCategoryVo;
import com.awg.kbs.vo.CategorySortVo;
import com.awg.kbs.vo.QueryCategoryListVo;
import com.awg.kbs.vo.UpdateCategoryVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@ApiSupport( order = 45 )
@Api( tags = {"知识库-分类相关接口"} )
@RestController
@RequestMapping( "/kbs/category" )
public class KbsCategoryController extends BaseController {

    @Resource
    private IKbsCategoryService kbsCategoryService;

    @ApiOperationSupport(order = 7)
    @ApiOperation( "分类列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = CategoryDto.class )
    })
    public DataResult list(@RequestBody QueryCategoryListVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsCategoryService.categoryList(vo));
    }

    @ApiOperationSupport(order = 8)
    @ApiOperation( "分类选择列表" )
    @PostMapping( "/selectList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = CategorySelectDto.class )
    })
    public DataResult selectList() {
        return renderSuccess(kbsCategoryService.categorySelectList());
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "添加分类" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addCategory(@RequestBody AddCategoryVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsCategoryService.addCategory(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑分类" )
    @PostMapping( "/upd" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateCategory(@RequestBody UpdateCategoryVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(kbsCategoryService.updateCategory(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "分类排序" )
    @PostMapping( "/sort" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult sortCategory(@RequestBody CategorySortVo vo) {
        ValidationUtils.validate(vo);
        kbsCategoryService.sortCategory(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除分类" )
    @PostMapping( "/del/{categoryId}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delCategory(@PathVariable( value = "categoryId" ) Integer categoryId) {
        AssertUtils.isTrue( categoryId <= 0, "id不合法");
        kbsCategoryService.delCategory(categoryId, getUserLoginInfoEo());
        return renderSuccess();
    }
}
