package com.awg.admin.website.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.website.dto.NewsDto;
import com.awg.website.dto.ProjectDto;
import com.awg.website.service.IWebsiteNewsService;
import com.awg.website.vo.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@ApiSupport( order = 60 )
@Api( tags = {"官网管理-新闻相关接口"} )
@RestController
@RequestMapping( "/website/news" )
public class WebsiteNewsController extends BaseController {

    @Resource
    private IWebsiteNewsService websiteNewsService;

    @ApiOperationSupport(order = 6)
    @ApiOperation( "获取新闻标签列表" )
    @PostMapping( "/tagListAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ProjectDto.class )
    })
    public DataResult tagListAll() {
        return renderSuccess(websiteNewsService.getNewsTagListAll(getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "更新新闻标签列表" )
    @PostMapping( "/updateTagList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateTagList(@RequestBody UpdateNewsTagListVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");

        ValidationUtils.validate(vo);

        // 迭代校验列表
        vo.getNewsTagList().forEach( item -> {
            ValidationUtils.validate(item);
        });

        websiteNewsService.updateNewsTagList(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 12)
    @ApiOperation( "获取新闻列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = NewsDto.class )
    })
    public DataResult getNewsList(@RequestBody QueryNewsVo vo) {
        ValidationUtils.validate(vo);
        vo.setOrgId(getUserLoginInfoEo().getOrgId());
        BasePageResult<NewsDto> result = websiteNewsService.queryNewsList(vo,getUserLoginInfoEo(), 1);
        return renderSuccess(result);
    }

    @ApiOperationSupport(order = 16)
    @ApiOperation( "获取新闻详情" )
    @PostMapping( "/detail/{newsNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = NewsDto.class )
    })
    public DataResult getNewsDetail(@PathVariable( value = "newsNo" ) String newsNo) {
        AssertUtils.isTrue( Long.parseLong(newsNo) <= 0, "No不合法");
        return renderSuccess(websiteNewsService.getNewsDetail(newsNo,getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "添加新闻" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addNews(@RequestBody AddNewsVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");

        ValidationUtils.validate(vo);
        return renderSuccess(websiteNewsService.addNews(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 25)
    @ApiOperation( "编辑新闻" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateNews(@RequestBody UpdateNewsVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteNewsService.updateNews(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "切换首页显示" )
    @PostMapping( "/switchDisplayFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult switchDisplayFlag(@RequestBody SwitchNewsFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteNewsService.switchDisplayFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "切换热门标志" )
    @PostMapping( "/switchHotFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult switchHotFlag(@RequestBody SwitchNewsFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteNewsService.switchHotFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 50)
    @ApiOperation( "切换置顶标志" )
    @PostMapping( "/switchTopFlag" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult switchTopFlag(@RequestBody SwitchNewsFlagVo vo) {
        ValidationUtils.validate(vo);
        websiteNewsService.switchTopFlag(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 60)
    @ApiOperation( "删除新闻" )
    @PostMapping( "/del/{newsNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delNews(@PathVariable( value = "newsNo" ) String newsNo) {
        AssertUtils.isTrue( Long.parseLong(newsNo) <= 0, "No不合法");
        websiteNewsService.delNews(newsNo, getUserLoginInfoEo());
        return renderSuccess();
    }
}
