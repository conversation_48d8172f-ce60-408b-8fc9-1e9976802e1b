package com.awg.front.client.controller;

import com.awg.client.eo.MemberLevelConfigEo;
import com.awg.client.eo.MemberLoginInfoPlusEo;
import com.awg.client.externalService.IMemberLevelConfigExternalService;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@ApiSupport( order = 66 )
@Api( tags = {"会员等级-相关接口"} )
@RestController
@RequestMapping( "/client/memberLevel" )
@Slf4j
public class MemberLevelController extends BaseController {

    @Resource
    private IMemberLevelConfigExternalService memberLevelConfigExternalService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "B端等级配置表" )
    @Authority( AuthorityEnum.NOCHECK )
    @PostMapping( "/businessLevelConfigList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberLevelConfigEo.class )
    })
    public DataResult businessLevelConfigList() {
        return renderSuccess(memberLevelConfigExternalService.businessLevelConfigList());
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "C端等级配置表" )
    @Authority( AuthorityEnum.NOCHECK )
    @PostMapping( "/consumerLevelConfigList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberLevelConfigEo.class )
    })
    public DataResult consumerLevelConfigList() {
        return renderSuccess(memberLevelConfigExternalService.consumerLevelConfigList());
    }

}
