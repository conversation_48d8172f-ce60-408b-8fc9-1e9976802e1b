package com.awg.mybatis.config;

import com.awg.utils.date.DateUtils;
import com.awg.utils.random.IdWorker;
import com.awg.mybatis.constant.DataBaseFieldConstant;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;


/**
 * mybatis plus 默认值配置
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2020年3月18日
 */
@Component
public class MetaObjectHandlerConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        String[] setterNames = metaObject.getSetterNames();
        HashSet<String> setterNameSet = new HashSet<>(Arrays.asList(setterNames));
        if (setterNameSet.contains(DataBaseFieldConstant.ID)) {
            //生成id
//            setFieldValByName(DataBaseFieldConstant.ID, IdWorker.getRandomNo(), metaObject);
        }
        if (setterNameSet.contains(DataBaseFieldConstant.CREATE_TIME)) {
            //创建时间默认当前时间
            if (metaObject.getValue(DataBaseFieldConstant.CREATE_TIME) == null) {
                setFieldValByName(DataBaseFieldConstant.CREATE_TIME, DateUtils.getCurrentTimestamp(), metaObject);
            }
            //更新时间默认当前时间
            if (metaObject.getValue(DataBaseFieldConstant.UPDATE_TIME) == null) {
                setFieldValByName(DataBaseFieldConstant.UPDATE_TIME, DateUtils.getCurrentTimestamp(), metaObject);
            }


        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        String[] setterNames = metaObject.getSetterNames();
        HashSet<String> setterNameSet = new HashSet<>(Arrays.asList(setterNames));
        if (setterNameSet.contains(DataBaseFieldConstant.UPDATE_TIME)) {
            //更新时间默认当前时间
            setFieldValByName(DataBaseFieldConstant.UPDATE_TIME, DateUtils.getCurrentTimestamp(), metaObject);
        }
    }
}