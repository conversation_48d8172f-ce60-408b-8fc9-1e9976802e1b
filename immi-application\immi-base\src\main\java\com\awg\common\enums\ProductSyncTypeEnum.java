package com.awg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 商品同步类型枚举
 */
@Getter
@AllArgsConstructor
public enum ProductSyncTypeEnum {

    INITIAL_SYNC(0, "第一次同步数据"),
    UPDATE_SYNC(1, "后续更新同步");

    private final Integer code;
    private final String label;

    public static ProductSyncTypeEnum parse(Integer code) {
        return Arrays.stream(ProductSyncTypeEnum.values())
                .filter(obj -> Objects.equals(code, obj.getCode()))
                .findFirst()
                .orElse(null);
    }

    public static ProductSyncTypeEnum parse(String codeStr) {
        try {
            Integer code = Integer.parseInt(codeStr);
            return parse(code);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}