package com.awg.comm.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品表实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "comm_product")
public class NewProduct extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 商品编号
     */
    private Long productNo;

    /**
     * 商品版本id
     */
    private Integer productVid;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 短码
     */
    private String shortCode;

    /**
     * 分类（1=签证类，2=申校类，3=本地服务）
     */
    private Integer category;

    /**
     * 可用状态(0=未上架，1=已上架)
     */
    private Integer availabilityStatus;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 可重置库存
     */
    private Integer resettableStock;

    /**
     * 初始库存-用于重置库存
     */
    private Integer initialStock;

    /**
     * 促销库存
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer promoStock;

    /**
     * 库存开启标志
     */
    private Integer stockOpenFlag;

    /**
     * 促销开启标志
     */
    private Integer promoOpenFlag;

    /**
     * 是否同步
     */
    private Integer isSync;

    /**
     * 平台同步版本号
     */
    private Integer platformSyncVid;

    /**
     * B端需要显示按钮提示按钮(0=不显示，1=显示)
     */
    private Integer showUpdateBtn;

    /**
     * B端是否修改过此商品(0:否,1:是)
     */
    private Integer isModified;

}
