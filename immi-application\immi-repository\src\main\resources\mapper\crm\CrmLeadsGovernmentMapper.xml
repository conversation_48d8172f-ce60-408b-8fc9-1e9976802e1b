<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.awg.crm.mapper.CrmLeadsGovernmentMapper">

    <!-- 结果映射 -->
    <resultMap type="com.awg.crm.dto.CrmLeadsGovernmentDto" id="crmLeadsGovernmentMap">
        <result property="id" column="government_id"/>
        <result property="leadsId" column="leads_id"/>
        <result property="feeName" column="fee_name"/>
        <result property="actualAmount" column="actual_amount"/>
        <result property="currencyUnit" column="currency_unit"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="isDelete" column="is_delete"/>

        <!-- 映射 screenshots 列表 -->
        <collection property="screenshots" ofType="com.awg.crm.entity.CrmLeadsGovernmentScreenshot">
            <result property="id" column="screenshot_id"/>
            <result property="crmLeadsGovernmentId" column="crm_leads_government_id"/>
            <result property="screenshotPath" column="screenshot_path"/>
            <result property="createdAt" column="screenshot_created_at"/>
            <result property="updatedAt" column="screenshot_updated_at"/>
            <result property="isDelete" column="screenshot_is_delete"/>
        </collection>
    </resultMap>



    <!-- 根据潜客ID查询所有政府费 -->
    <select id="listByLeadsId" resultMap="crmLeadsGovernmentMap">
        SELECT
        g.id AS government_id,
        g.leads_id,
        g.fee_name,
        g.actual_amount,
        g.currency_unit,
        g.created_at,
        g.updated_at,
        s.id AS screenshot_id,
        s.crm_leads_government_id,
        s.screenshot_path,
        s.created_at AS screenshot_created_at,
        s.updated_at AS screenshot_updated_at,
        s.is_delete AS screenshot_is_delete
        FROM
        crm_leads_government g
        LEFT JOIN
        crm_leads_government_screenshot s
        ON g.id = s.crm_leads_government_id
        AND s.is_delete = 0
        WHERE
        g.leads_id = #{leadsId}
        AND g.is_delete = 0
        ORDER BY
        g.id ASC;
    </select>



    <select id="getById" resultMap="crmLeadsGovernmentMap">
        SELECT
            g.id AS government_id,
            g.leads_id,
            g.fee_name,
            g.actual_amount,
            g.currency_unit,
            g.created_at,
            g.updated_at,
            g.is_delete,
            s.id AS screenshot_id,
            s.crm_leads_government_id,
            s.screenshot_path,
            s.created_at AS screenshot_created_at,
            s.updated_at AS screenshot_updated_at,
            s.is_delete AS screenshot_is_delete
        FROM
            crm_leads_government g
                LEFT JOIN
            crm_leads_government_screenshot s ON g.id = s.crm_leads_government_id AND s.is_delete = 0
        WHERE
            g.id = #{id}
          AND g.is_delete = 0
    </select>


</mapper>