package com.awg.admin.comm.controller;

import com.awg.comm.entity.ProductSynchronization;
import com.awg.comm.mapstruct.ProductSynchronizationMapstruct;
import com.awg.comm.service.IProductSynchronizationService;
import com.awg.comm.vo.ProductSynchronizationCreateVo;
import com.awg.comm.vo.ProductSynchronizationUpdateVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.validator.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import java.util.List;

@ApiSupport(order = 221)
@Api(tags = {"商品同步相关接口"})
@RestController
@RequestMapping("/comm/product-synchronization")
@Validated
public class ProductSynchronizationController extends BaseController {

    @Resource
    private IProductSynchronizationService productSynchronizationService;


    /**
     * 根据机构ID查询商品同步记录
     */
    @GetMapping("/{orgId}")
    @ApiOperation("根据当前登录的机构ID查询商品同步记录")
    public DataResult getProductSynchronizationById(@PathVariable @Min(value = 1, message = "ID必须大于等于1") Long orgId) {
        ProductSynchronization entity = productSynchronizationService.getProductSynchronizationByOrgId(orgId);
        return renderSuccess(entity);
    }

    /**
     * 新增-修改商品同步记录
     */
    @PostMapping("/add")
    @ApiOperation("新增-修改 商品同步记录")
    public DataResult createProductSynchronization(@Validated @RequestBody ProductSynchronizationCreateVo productSynchronizationCreateVo) {
//        ProductSynchronization productSynchronization = mapstruct.toEntity(productSynchronizationCreateVo);
        productSynchronizationService.createProductSynchronization(productSynchronizationCreateVo,getUserLoginInfoEo());
        return renderSuccess();
    }


//    /**
//     * 修改商品同步记录
//     */
//    @PostMapping("/update")
//    @ApiOperation("修改商品同步记录")
//    public DataResult updateProductSynchronization(@RequestBody ProductSynchronizationUpdateVo productSynchronizationUpdateVo) {
//        ProductSynchronization productSynchronization = new ProductSynchronization();
//        BeanUtils.copyProperties(productSynchronizationUpdateVo, productSynchronization);
//        productSynchronizationService.updateById(productSynchronization);
//        return renderSuccess();
//    }
} 