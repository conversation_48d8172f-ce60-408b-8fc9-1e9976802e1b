package com.awg.mybatis.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;

/**
 * @description: 实体父类
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2019-11-18 14:17
 * @version: V1.0
 **/

public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId ( value = "id", type = IdType.AUTO )
    private Integer id;

    /**
     * 创建日期
     */
    @TableField ( value = "created_at", fill = FieldFill.INSERT )
    private Long createdAt;

    /**
     * 更新日期
     */
    @TableField ( value = "updated_at", fill = FieldFill.INSERT_UPDATE )
    private Long updatedAt;

    /**
     * 删除状态（0：正常，1：已删除）
     */
    @TableField ( value = "is_delete" )
    private Integer isDelete;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

}