package com.awg.comm.entity;

import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品地区表 - 新版本（支持MyBatis-Plus逻辑删除）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "comm_district_info")
public class NewDistrictInfo extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;

    /**
     * 父级id
     */
    private Integer pid;

    /**
     * 国家/地区代码
     */
    private String regionCode;

    /**
     * 排序
     */
    @TableField("`order`")
    private Integer order;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 来自平台的id，0表示不来自平台
     */
    private Integer fromPlatformId;

    /**
     * 展示标志[0不展示，1展示]
     */
    private Integer displayFlag;
}