package com.awg.common.validator.phone;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <p>
 * <b>ValidationPhone</b> is
 * </p>
 *
 * <AUTHOR>
 * @date 2021/4/22 15:46
 */

@Documented
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Constraint(validatedBy = {PhoneValidator.class})
public @interface ValidationPhone {

    //提示信息
    String message() default "不合法的手机号格式";

    //不同情况下效验逻辑
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
