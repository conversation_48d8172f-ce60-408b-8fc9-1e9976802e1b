package com.awg.comm.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品同步细节表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "comm_product_synchronization_details")
public class ProductSynchronizationDetails extends NewBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 来源商品编号id */
    private Long productNo;

    /** 商品同步表id */
    private Integer syncId;

    /** 是否同步（0：未同步，1：已同步） */
    private Integer isSync;



} 