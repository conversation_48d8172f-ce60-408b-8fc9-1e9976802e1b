package com.awg.admin.system.controller;


import com.awg.system.dto.ChannelUserDto;
import com.awg.system.dto.UserDto;
import com.awg.system.service.IChannelUserService;
import com.awg.system.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@ApiSupport( order = 15 )
@Api( tags = {"渠道用户相关接口"} )
@RestController
@RequestMapping( "/system/channelUser" )
public class ChannelUserController extends BaseController {

    @Resource
    private IChannelUserService channelUserService;

    @ApiOperation( "获取渠道用户列表" )
    @PostMapping( "/list" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = ChannelUserDto.class )
    })
    public DataResult list(@RequestBody QueryChannelUserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(channelUserService.getChannelList(vo, getCurrentOrgId(), getUserLoginInfoEo()));
    }

    @ApiOperation( "添加渠道人员" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult add(@RequestBody AddChannelUserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(channelUserService.addOrgChannelUser(vo, getCurrentOrgId(), getUid()));
    }

    @ApiOperation( "修改渠道人员" )
    @PostMapping( "/update" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult update(@RequestBody UpdateChannelUserVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(channelUserService.updateChannelUser(vo, getCurrentOrgId()));
    }

    @ApiOperation( "回收渠道人员" )
    @PostMapping( "/del/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult del(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "人员id不合法");
        channelUserService.takeBackChannelUser(id, getCurrentOrgId());
        return renderSuccess();
    }

    @ApiOperation( "恢复渠道人员" )
    @PostMapping( "/recover/{id}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult recover(@PathVariable( value = "id" ) Integer id) {
        AssertUtils.isTrue(id <= 0, "人员id不合法");
        channelUserService.recoverChannelUser(id, getCurrentOrgId());
        return renderSuccess();
    }

    @ApiOperation( "重置密码" )
    @PostMapping( "/resetPassword" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult resetPassword(@RequestBody ResetPasswordVo vo) {
        ValidationUtils.validate(vo);
        channelUserService.resetPassword(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperation( "知识库开关设置" )
    @PostMapping( "/channelKbsOpenSet" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult channelKbsOpenSet(@RequestBody KbsOpenVo vo) {
        ValidationUtils.validate(vo);
        channelUserService.channelKbsOpenSet(vo, getCurrentOrgId());
        return renderSuccess();
    }
}
