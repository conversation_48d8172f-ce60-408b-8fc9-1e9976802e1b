package com.awg.admin.plv.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.ipaddr.service.IIpaddrLocationService;
import com.awg.plv.externalService.IPlvUploadRecordExternalService;
import com.awg.plv.result.PolyvVideoInfoResult;
import com.awg.plv.utils.PolyvUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 212 )
@Api( tags = {"保利威相关接口"} )
@RestController
@RequestMapping( "/plv/record" )
@Slf4j
public class PlvUploadRecordController extends BaseController {

    @Resource
    private IPlvUploadRecordExternalService polyvUploadRecordService;

    @ApiOperationSupport( order = 120 )
    @ApiOperation( "保利威回调" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    } )
    @GetMapping( "/polyv/callback/dwu8u8eos356i2hd" )
    @Authority( AuthorityEnum.NOCHECK )
    public DataResult polyvCallback(
            @RequestParam( value = "sign", required = false ) String sign,
            @RequestParam ( value = "vid", required = false ) String vid,
            @RequestParam ( value = "type", required = false ) String type,
            @RequestParam ( value = "state", required = false ) String state,
            @RequestParam ( value = "fileUrl", required = false ) String fileUrl
    ) {
        log.error("保利威回调："+type+vid+sign);

        // 视频上传完成回调
        if( StringUtils.equals("upload", type) || StringUtils.equals("invalidVideo", type)){
            // 都不为空
            if(StringUtils.isNotBlank(sign) && StringUtils.isNotBlank(vid) && StringUtils.isNotBlank(type) && StringUtils.isNotBlank(state) ) {
                // 验证回调签名
                if(PolyvUtil.validateCallBackSign(sign, type, vid, state, fileUrl)){
                    if(StringUtils.equals("upload", type)){
                        polyvUploadRecordService.updateStatus(state, vid, 1);

                        // 获取视频信息，用来填充大图
                        try {
                            PolyvVideoInfoResult videoInfo = PolyvUtil.getVideoInfo(vid);
                            polyvUploadRecordService.fillVideoInfo(state, videoInfo);
                        } catch (Exception e) {
                            log.error("获取保利威视频信息失败：{0}", e.fillInStackTrace());
                        }

                    }
                    if(StringUtils.equals("invalidVideo", type)){
                        polyvUploadRecordService.updateStatus(state, vid, 4);
                    }
                }
            }
        }

        // 视频上传失败回调
        if( StringUtils.equals("async_upload", type) ){
            // 都不为空
            if( StringUtils.isNotBlank(sign) && StringUtils.isNotBlank(type) && StringUtils.isNotBlank(state) && StringUtils.isNotBlank(fileUrl) ) {
                // 验证回调签名
                if(PolyvUtil.validateCallBackSign(sign, type, vid, state, fileUrl)){
                    polyvUploadRecordService.updateStatus(state, vid, 4);
                }
            }
        }

        // 属于视频审核
        if( StringUtils.equals("pass", type) || StringUtils.equals("nopass", type) ){
            // 都不为空
            if(StringUtils.isNotBlank(sign) && StringUtils.isNotBlank(vid) && StringUtils.isNotBlank(type) ) {
                // 验证回调签名
                if(PolyvUtil.validateCallBackSign(sign, type, vid, state, fileUrl)){
                    if(StringUtils.equals("pass", type)){
                        polyvUploadRecordService.updateStatus(state, vid, 2);

                        // 获取视频信息，用来填充大图
                        try {
                            PolyvVideoInfoResult videoInfo = PolyvUtil.getVideoInfo(vid);
                            polyvUploadRecordService.fillVideoInfo(state, videoInfo);
                        } catch (Exception e) {
                            log.error("获取保利威视频信息失败：{0}", e.fillInStackTrace());
                        }
                    }
                    if(StringUtils.equals("nopass", type)){
                        polyvUploadRecordService.updateStatus(state, vid, 3);
                    }
                }
            }
        }

        return renderSuccess();
    }
}
