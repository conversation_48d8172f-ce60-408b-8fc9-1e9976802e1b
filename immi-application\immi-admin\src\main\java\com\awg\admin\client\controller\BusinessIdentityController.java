package com.awg.admin.client.controller;

import com.awg.account.externalService.IWalletExternalService;
import com.awg.client.dto.BusinessMemberItemDto;
import com.awg.client.dto.ConsumerMemberItemDto;
import com.awg.client.dto.InviteMemberRewardInfoDto;
import com.awg.client.service.IBusinessIdentityService;
import com.awg.client.service.IMemberService;
import com.awg.client.vo.*;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 210 )
@Api( tags = {"B端用户相关接口"} )
@RestController
@RequestMapping( "/client/business" )
public class BusinessIdentityController extends BaseController {

    @Resource
    private IBusinessIdentityService businessIdentityService;

    @Resource
    private IWalletExternalService walletExternalService;

    @Resource
    private IMemberService memberService;

    @ApiOperation( "获取B端用户列表" )
    @PostMapping( "/list" )
    @ApiOperationSupport(order = 10)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessMemberItemDto.class )
    })
    public DataResult getBusinessMemberList(@RequestBody QueryBMemberVo vo){
        ValidationUtils.validate(vo);
        BasePageResult<BusinessMemberItemDto> result = businessIdentityService.getBusinessMemberList(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "获取B端用户信息" )
    @PostMapping( "/info/{bid}" )
    @ApiOperationSupport(order = 20)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = BusinessMemberItemDto.class )
    })
    public DataResult getBusinessMemberInfo(@PathVariable( value = "cid" ) Integer bid){
        AssertUtils.isTrue(bid <= 0, "bid不合法");
        BusinessMemberItemDto result = businessIdentityService.getBusinessMemberInfo(bid, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "创建B端用户信息" )
    @PostMapping( "/create" )
    @ApiOperationSupport(order = 30)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult createBusinessMemberInfo(@RequestBody UpdateBusinessMemberVo vo){
        ValidationUtils.validate(vo);
        Integer bid = businessIdentityService.createBusinessMemberInfo(vo, getUserLoginInfoEo());
        Integer memberId = memberService.bidToMemberId(bid);

        // 创建钱包
        Long walletNo = walletExternalService.getMemberWalletNo(memberId);

        // 赠送金额
//        walletExternalService.registerGive(walletNo.toString());

        return renderSuccess(bid);
    }

    @ApiOperation( "编辑B端用户信息" )
    @PostMapping( "/update" )
    @ApiOperationSupport(order = 40)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult updateBusinessMemberInfo(@RequestBody UpdateBusinessMemberVo vo){
        ValidationUtils.validate(vo);
        businessIdentityService.updateBusinessMemberInfo(vo, getUserLoginInfoEo());

        return renderSuccess();
    }

    @ApiOperation( "禁用B端" )
    @PostMapping( "/disable/{bid}" )
    @ApiOperationSupport(order = 50)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult disableBusinessIdentity(@PathVariable( value = "bid" ) Integer bid){
        AssertUtils.isTrue(bid <= 0, "bid不合法");
        businessIdentityService.disableBusinessIdentity(bid, 1, (long)getUserInfoId(), "管理员禁用B端身份", getCurrentOrgId());

        return renderSuccess();
    }

    @ApiOperation( "启用B端" )
    @PostMapping( "/enable/{bid}" )
    @ApiOperationSupport(order = 60)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult enableBusinessIdentity(@PathVariable( value = "bid" ) Integer bid){
        AssertUtils.isTrue(bid <= 0, "bid不合法");
        businessIdentityService.enableBusinessIdentity(bid, 1, (long)getUserInfoId(), "管理员恢复B端身份", getCurrentOrgId());

        return renderSuccess();
    }

    @ApiOperation( "邀请的用户列表和奖励详情" )
    @PostMapping( "/inviteMemberAndRewardDetail" )
    @ApiOperationSupport(order = 70)
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = InviteMemberRewardInfoDto.class )
    })
    public DataResult inviteMemberAndRewardDetail(@RequestBody QueryInviteMemberRewardVo vo){
        ValidationUtils.validate(vo);
        InviteMemberRewardInfoDto result = businessIdentityService.getInviteMemberAndRewardDetail(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }
}
