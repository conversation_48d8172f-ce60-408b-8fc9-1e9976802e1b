package com.awg.comm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "商品同步参数")
public class QueryProductSynchronizationVo implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "商品id", required = true)
    @NotNull(message = "商品id不能为空")
    private Integer productId;

    @ApiModelProperty(value = "商品编号", required = true)
    @NotNull(message = "商品id不能为空")
    private String productNo;

    @ApiModelProperty(value = "商品版本id", required = true)
    @NotNull(message = "商品id不能为空")
    private Integer productVid;



}
