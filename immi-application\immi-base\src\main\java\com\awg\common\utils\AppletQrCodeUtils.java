package com.awg.common.utils;

import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.thirdparty.sdk.cos.TencentCOSUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.Buffer;
import java.nio.charset.Charset;

/**
 * <p>
 * <b>AppletQrCodeUtils</b> is 二维码工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/5/13 13:42
 */
@Component
public class AppletQrCodeUtils {

    private static String immiEnvCurrent;

    @Value("${immi.env.current}")
    public void setImmiEnvCurrent(String immiEnvCurrent) {
        AppletQrCodeUtils.immiEnvCurrent = immiEnvCurrent;
    }

    public static String getImmiEnvCurrent() {
        return immiEnvCurrent;
    }



    /**
     * @param page:
     * @param parameters:
     * @description: 生产小程序二维码
     * @author: yangqiang
     * @date: 2022/5/13 13:49
     * @return: java.lang.String
     **/
    public static String generateReceiveImages(
            boolean isForceProduce, String accessToken, String page,
                                               String parameters, String envVersion, String timeKey, Integer px
    ) {

        String env = getImmiEnvCurrent();

        // 默认生产环境
        String envVersionPro = envVersion;
        if (envVersion == null || envVersion.equals("")) {
            envVersionPro = "release";
        }

        if(!"pre".equals(env)) {
            envVersionPro = "trial";
        }

        String checkPathStr = "true";
        if(!envVersionPro.equals("release")) {
            checkPathStr = "false";
        }

        String cosKeyPrefix = FileBaseUtil.getCosKeyPrefix();
        String fileUrlBase = FileBaseUtil.getFileUrlBase();
        String bucketName = "file-immi";

        // 准备key的结尾部分
        String generateKey = "/applet/generate/qrcode/" + envVersionPro + "/" + page + parameters + ".jpg";
        if(timeKey!=null && !timeKey.equals("")) {
            generateKey = "/applet/generate/qrcode/" + envVersionPro + "/" + page + "/" + timeKey + parameters + ".jpg";
        }

        String cosKey = cosKeyPrefix + generateKey;

        if (!isForceProduce && TencentCOSUtils.verifyCosKey(cosKey, bucketName)) {
            String url = fileUrlBase + generateKey;
            return url;
        }
        //获取图片
        Integer width = 430;
        if(px!=null && px>=280 && px<=1280) {
            width = px;
        }
        String url2 = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
        String jsonStr = "{\n" +
                "    \"scene\":\"" + parameters + "\",\n" +
                "    \"page\":\"" + page + "\",\n" +
                "    \"env_version\":\"" + envVersionPro + "\",\n" +
                "    \"check_path\":" + checkPathStr + ",\n" +      // 是否检测页面的合法性
                "    \"width\":\"" + width + "\"\n" +
                "}";
        byte[] bytes = post(url2, jsonStr);
        String result = null;
        try {
            result = TencentCOSUtils.uploadFile(bucketName, cosKey, bytes);
            result = fileUrlBase + generateKey;
        } catch (Exception e) {
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "上传附件出现了异常！");
        }
        return result;
    }

    /**
     * @param url:
     * @param json:
     * @description:发送 post请求 用HTTPclient 发送请求
     * @author: yangqiang
     * @date: 2021/3/16 14:52
     * @return: byte[]
     **/
    private static byte[] post(String url, String json) {
        String obj = null;
        InputStream inputStream = null;
        Buffer reader = null;
        byte[] data = null;
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httppost
        HttpPost httppost = new HttpPost(url);
        httppost.addHeader("Content-type", "application/json; charset=utf-8");
        httppost.setHeader("Accept", "application/json");
        try {
            StringEntity s = new StringEntity(json, Charset.forName("UTF-8"));
            s.setContentEncoding("UTF-8");
            httppost.setEntity(s);
            CloseableHttpResponse response = httpclient.execute(httppost);
            try {
                // 获取相应实体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    inputStream = entity.getContent();
                    data = readInputStream(inputStream);
                }
                return data;
            } finally {
                response.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭连接,释放资源
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return data;
    }

    /**
     * 将流保存为数据数组
     *
     * @param inStream
     * @return
     * @throws Exception
     */
    private static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        // 创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        // 每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        // 使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        // 关闭输入流
        inStream.close();
        // 把outStream里的数据写入内存
        return outStream.toByteArray();
    }

}