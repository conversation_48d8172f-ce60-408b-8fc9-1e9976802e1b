package com.awg.admin.website.controller;

import com.awg.bp.vo.AddProcessVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.validator.ValidationUtils;
import com.awg.website.service.IWebsiteDirectoryService;
import com.awg.website.vo.AddDirectoryVo;
import com.awg.website.vo.EditDirectoryVo;
import com.awg.website.vo.QueryDirectoryListVo;
import com.awg.website.vo.WebsiteSortVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-04
 */
@ApiSupport( order = 56 )
@Api( tags = {"官网管理-目录相关接口"} )
@RestController
@RequestMapping( "/website/directory" )
public class WebsiteDirectoryController extends BaseController {

    @Resource
    private IWebsiteDirectoryService websiteDirectoryService;

    @ApiOperationSupport(order = 7)
    @ApiOperation( "目录列表" )
    @PostMapping( "/directoryList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult directoryList(@RequestBody QueryDirectoryListVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteDirectoryService.directoryList(vo, getUserLoginInfoEo().getOrgId()));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation( "添加目录" )
    @PostMapping( "/addDirectory" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addDirectory(@RequestBody AddDirectoryVo vo) {

        // 固定地区和语言
        vo.setLanguage("zh-CN");
        vo.setRegion("CA");

        ValidationUtils.validate(vo);
        return renderSuccess(websiteDirectoryService.addDirectory(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "编辑目录" )
    @PostMapping( "/updateDirectory" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult updateDirectory(@RequestBody EditDirectoryVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(websiteDirectoryService.updateDirectory(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "排序目录" )
    @PostMapping( "/sortDirectory" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult sortDirectory(@RequestBody WebsiteSortVo vo) {
        ValidationUtils.validate(vo);
        websiteDirectoryService.sortDirectory(vo, getUserLoginInfoEo());
        return renderSuccess();
    }

    @ApiOperationSupport(order = 40)
    @ApiOperation( "删除目录" )
    @PostMapping( "/delDirectory/{directoryNo}" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult delDirectory(@PathVariable( value = "directoryNo" ) String directoryNo) {
        AssertUtils.isTrue( Long.parseLong(directoryNo) <= 0, "No不合法");
        websiteDirectoryService.delDirectory(directoryNo, getUserLoginInfoEo());
        return renderSuccess();
    }


}
