package com.awg.crm.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.awg.mybatis.entity.NewBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@TableName("crm_leads_government_screenshot")
@ApiModel(value = "CrmLeadsGovernmentScreenshot对象", description = "政府费支付截图")
public class CrmLeadsGovernmentScreenshot extends NewBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("政府费记录ID")
    private Integer crmLeadsGovernmentId;

    @ApiModelProperty("支付截图路径")
    private String screenshotPath;

    @ApiModelProperty("1-新增 2-删除")
    @TableField(exist = false) // 排除该字段，不映射到数据库
    private Integer code = 0;







}
