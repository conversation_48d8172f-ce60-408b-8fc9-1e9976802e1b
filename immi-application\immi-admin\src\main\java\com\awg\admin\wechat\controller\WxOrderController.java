package com.awg.admin.wechat.controller;

import com.awg.common.base.controller.BaseController;
import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.base.result.DataResult;
import com.awg.common.exception.AssertUtils;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.utils.io.IOUtil;
import com.awg.wechat.entity.WechatOrder;
import com.awg.wechat.mapper.WechatOrderMapper;
import com.awg.wechat.pay.sdk.WXPay;
import com.awg.wechat.pay.sdk.WXPayConstants;
import com.awg.wechat.pay.sdk.WXPayUtil;
import com.awg.wechat.pay.sdk.WxPayConfigImpl;
import com.awg.wechat.service.IWechatOrderService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 370 )
@Api( tags = {"微信支付相关接口"} )
@RestController
@RequestMapping( "/wechat/wxOrder" )
@Slf4j
public class WxOrderController extends BaseController {

    @Resource
    private WxPayConfigImpl wxPayConfig;

    @Resource
    private WechatOrderMapper wechatOrderMapper;

    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    @Resource
    private IWechatOrderService wechatOrderService;

//    private Map<String, String> getWeChatOrder(String orderNo) {
//        Map<String, String> orderInfo;
//        try {
//            // 查询订单
//            WechatOrder order = null;
//            AssertUtils.isNull(order, "无效的订单信息");
//
//            // 查询商户号
//
//            Map<String, String> query = new HashMap<>(16);
//            query.put("out_trade_no", orderNo);
//            query.put("sub_mch_id", merchant.getMchId());
//            WXPay wxPay = new WXPay(wxPayConfig, false);
//            orderInfo = wxPay.orderQuery(query);
//        } catch (Exception e) {
//            log.error("微信订单查询出现异常：{0}", e.fillInStackTrace());
//            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), "微信订单查询出现异常，请联系管理员}");
//        }
//
//        return orderInfo;
//    }

//    @ApiOperation( "查询微信订单" )
//    @GetMapping( value = "/query/order" )
//    @Authority( AuthorityEnum.NOCHECK )
//    public DataResult orderQuery(@RequestParam( value = "orderNo" ) String orderNo) {
//        return renderSuccess(getWeChatOrder(orderNo));
//    }

    @ApiIgnore
    @RequestMapping ( value = "/notify28302918203827162763", method = {RequestMethod.GET, RequestMethod.POST} )
    @Authority ( AuthorityEnum.NOCHECK )
    public void payNotify() {
        System.out.println("================== notify pay ==================");

        try {
            String content = IOUtil.read(request.getInputStream());
            Map<String, String> result = WXPayUtil.xmlToMap(content);
            System.out.println(result);

            // 效验通知
            String returnCode = String.valueOf(result.getOrDefault("return_code", ""));
            AssertUtils.isFalse(WXPayConstants.SUCCESS.equals(returnCode), String.valueOf(result.get("return_msg")));
            String resultCode = String.valueOf(result.getOrDefault("result_code", ""));
            AssertUtils.isFalse(WXPayConstants.SUCCESS.equals(resultCode), String.valueOf(result.get("err_code_des")));

            // 获取订单编号
            String orderNo = result.get("out_trade_no");

            // 获取交易类型[附加信息，暂时没有用，全部都填了1]
            // OrderTypeEnum orderType = OrderTypeEnum.parse(Integer.parseInt(result.get("attach")));

            // 订单回调
            wechatOrderService.wxOrderComplete(orderNo);

            // 通知微信，避免重复回调
            response.getWriter().write("<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>");
        } catch (Exception e) {
            log.error("微信支付回调通知异常：{0}", e.fillInStackTrace());
            throw new BusinessException(BaseResponseCode.CODE_ERROR.getCode(), e.getMessage());
        }
    }
}
