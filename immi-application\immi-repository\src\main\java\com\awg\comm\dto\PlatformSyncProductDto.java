package com.awg.comm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 平台同步商品信息 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@ApiModel(value = "平台同步商品信息")
public class PlatformSyncProductDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号")
    private Long productNo;
    
    @ApiModelProperty(value = "是否修改过此商品(0:否,1:是)")
    private Integer isModified;
    
    @ApiModelProperty(value = "机构id")
    private Integer orgId;
    
    @ApiModelProperty(value = "来源商品编号")
    private Long sourceNo;
    
    @ApiModelProperty(value = "销售咨询类型（0=购买，1=咨询，2=无）")
    private Integer salesConsultationType;
    
    @ApiModelProperty(value = "咨询按钮文案")
    private String consultationButtonText;
    
    @ApiModelProperty(value = "咨询顾问微信")
    private String consultantWechat;
    
    @ApiModelProperty(value = "咨询顾问二维码")
    private String consultantQrcode;
    
    @ApiModelProperty(value = "商品版本id")
    private Integer productVid;
    
    @ApiModelProperty(value = "商品名称")
    private String name;
    
    @ApiModelProperty(value = "同步来源（0=手动导入，1=平台同步）")
    private Integer syncSource;
}
