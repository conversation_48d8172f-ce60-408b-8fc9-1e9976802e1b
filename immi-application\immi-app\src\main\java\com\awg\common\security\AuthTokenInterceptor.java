package com.awg.common.security;

import com.awg.common.base.exception.BaseResponseCode;
import com.awg.common.base.exception.BusinessException;
import com.awg.common.constant.UserTokenConstant;
import com.awg.common.jwt.JwtProperties;
import com.awg.common.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;

/**
 * @description: 认证token拦截器
 * @author: yangqiang
 * @date: 2020-10-25 16:36
 * @version: V1.0
 **/
@Slf4j
@Component
public class AuthTokenInterceptor implements HandlerInterceptor {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private AuthorityUtil authorityUtil;


    /**
     * @param request:
     * @param response:
     * @param handler:
     * @description: 请求执行前执行
     * @author: yangqiang
     * @date: 2020/12/10 14:41
     * @return: boolean
     **/
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        if (redisUtils.exists(UserTokenConstant.DEBUG_KYE)) {
            //调试的时候开启
            String url = request.getRequestURL().toString();
            log.error("请求url：{} ", url);
            //请求参数
            Enumeration enu = request.getParameterNames();
            StringBuilder sb = new StringBuilder();
            while (enu.hasMoreElements()) {
                String paraName = (String) enu.nextElement();
                sb.append(paraName).append("=").append(request.getParameter(paraName));
            }
            log.error("request请求参数：{}", sb.toString());
            // 获取RequestBody里面的参数
        }

        //预请求无法转为HandlerMethod
//        if (!(handler instanceof HandlerMethod)) {
//            return true;
//        }

        //验证是否跳过检查
        if (isNotRequiredMethod((HandlerMethod) handler)) {
            return true;
        }
        //验证是否携带
        String accessToken = request.getHeader(JwtProperties.TOKEN_KEY);
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(BaseResponseCode.LOGIN_AUTH);
        }
        //校验token合法性
        String uri = String.valueOf(request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE));
        return authorityUtil.authentication(accessToken, uri);
    }

    private boolean isNotRequiredMethod(HandlerMethod method) {
        Authority authority = method.getBeanType().getAnnotation(Authority.class);
        if (authority == null) {
            authority = method.getMethod().getAnnotation(Authority.class);
        }
        return authority != null && authority.value().equals(AuthorityEnum.NOCHECK);
    }

    /**
     * @param request:
     * @param response:
     * @param handler:
     * @param modelAndView:
     * @description: 请求结束执行
     * @author: yangqiang
     * @date: 2020/12/10 14:41
     * @return: void
     **/
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    /**
     * @description: 视图渲染完成后执行
     * @author: yangqiang
     * @date: 2020/11/3 11:44
     * @param: [request, response, handler, ex]
     * @return: void
     **/
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }
}