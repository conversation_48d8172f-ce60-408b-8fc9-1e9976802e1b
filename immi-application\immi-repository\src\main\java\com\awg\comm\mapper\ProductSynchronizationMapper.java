package com.awg.comm.mapper;

import com.awg.comm.entity.ProductSynchronization;
import com.awg.comm.vo.ProductSynchronizationCreateVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.factory.Mappers;

/**
 * 商品同步表 Mapper 接口
 */
public interface ProductSynchronizationMapper extends BaseMapper<ProductSynchronization> {

    /**
     * 查询指定商品编号未同步的同步详情数量
     * @param productNo 商品编号
     * @return 未同步数量
     */
    int countUnSyncedDetailsByProductNo(@Param("productNo") String productNo);


} 