package com.awg.admin.client.controller;

import com.awg.client.entity.LevelAssessmentRecord;
import com.awg.client.eo.MemberLevelConfigEo;
import com.awg.client.externalService.IMemberLevelConfigExternalService;
import com.awg.client.mapper.LevelAssessmentRecordMapper;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.enums.TrueFalseEnum;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.utils.date.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 300 )
@Api( tags = {"会员等级相关接口"} )
@RestController
@RequestMapping( "/client/memberLevel" )
public class MemberLevelController extends BaseController {

    @Resource
    private IMemberLevelConfigExternalService memberLevelConfigExternalService;

    @Resource
    private LevelAssessmentRecordMapper levelAssessmentRecordMapper;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "B端等级配置表" )
    @PostMapping( "/businessLevelConfigList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberLevelConfigEo.class )
    })
    public DataResult businessLevelConfigList() {
        return renderSuccess(memberLevelConfigExternalService.businessLevelConfigList());
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "C端等级配置表" )
    @PostMapping( "/consumerLevelConfigList" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = MemberLevelConfigEo.class )
    })
    public DataResult consumerLevelConfigList() {
        return renderSuccess(memberLevelConfigExternalService.consumerLevelConfigList());
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "等级考核检测" )
    @Authority( AuthorityEnum.NOCHECK )
    @GetMapping( value = "/levelAssessmentCheck/siowi98wisujdkwo9as17782" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Void.class )
    })
    public DataResult levelAssessmentCheck() {
        // 当前时间戳
        Integer currentTime = (int) DateUtils.getCurrentTimestamp();

        // 获取等级考核到期的记录 mapper query
        List<LevelAssessmentRecord> list = levelAssessmentRecordMapper.selectList(Wrappers.<LevelAssessmentRecord>lambdaQuery()
                .eq(LevelAssessmentRecord::getIsDelete, TrueFalseEnum.FALSE.getCode())
                .eq(LevelAssessmentRecord::getStatus, 0)
                .gt(LevelAssessmentRecord::getMemberId, 0)
                .lt(LevelAssessmentRecord::getEndTime, currentTime)
        );

        // 循环检测等级
        for(LevelAssessmentRecord record : list) {
            // 检测等级
            memberLevelConfigExternalService.checkMemberLevelUpdate(record.getMemberId(), true);
        }

        return renderSuccess();
    }
}
