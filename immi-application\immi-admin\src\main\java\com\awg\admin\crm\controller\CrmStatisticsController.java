package com.awg.admin.crm.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.page.BasePageResult;
import com.awg.common.base.result.DataResult;
import com.awg.common.validator.ValidationUtils;
import com.awg.crm.dto.LeadsDto;
import com.awg.crm.dto.QueryBusinessStatisticsDto;
import com.awg.crm.dto.QueryPaymentDetailsDto;
import com.awg.crm.service.ICrmStatisticsService;
import com.awg.crm.vo.BusinessStatisticsVo;
import com.awg.crm.vo.PaymentDetailsVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-13
 */
@ApiSupport( order = 42 )
@Api( tags = {"CRM统计相关接口"} )
@RestController
@RequestMapping( "/crm/statistics" )
public class CrmStatisticsController extends BaseController {

    @Resource
    private ICrmStatisticsService crmStatisticsService;

    @ApiOperation( "业务统计" )
    @PostMapping( "/businessStatistics" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QueryBusinessStatisticsDto.class )
    })
    public DataResult businessStatistics(@RequestBody BusinessStatisticsVo vo){
        ValidationUtils.validate(vo);
        QueryBusinessStatisticsDto result = crmStatisticsService.getBusinessStatistics(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }

    @ApiOperation( "支付明细" )
    @PostMapping( "/paymentDetails" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QueryPaymentDetailsDto.class )
    })
    public DataResult paymentDetails(@RequestBody PaymentDetailsVo vo){
        ValidationUtils.validate(vo);
        QueryPaymentDetailsDto result = crmStatisticsService.getPaymentDetails(vo, getUserLoginInfoEo());

        return renderSuccess(result);
    }
}
