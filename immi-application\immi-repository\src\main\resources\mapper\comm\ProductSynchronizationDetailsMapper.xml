<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.awg.comm.mapper.ProductSynchronizationDetailsMapper">
    <resultMap id="BaseResultMap" type="com.awg.comm.entity.ProductSynchronizationDetails">
        <id column="id" property="id" />
        <result column="product_no" property="productNo" />
        <result column="sync_id" property="syncId" />
        <result column="is_sync" property="isSync" />
    </resultMap>
    <!-- 可根据业务需求添加自定义SQL -->
</mapper> 