package com.awg.front.qs.controller;


import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.security.Authority;
import com.awg.common.security.AuthorityEnum;
import com.awg.common.validator.ValidationUtils;
import com.awg.qs.dto.QuestionDataDto;
import com.awg.qs.dto.QuestionDto;
import com.awg.qs.dto.SubmitDto;
import com.awg.qs.service.IQuestionnaireService;
import com.awg.qs.vo.GetQuestionnaireVo;
import com.awg.qs.vo.QsResponsesVo;
import com.awg.qs.vo.QuestionnaireSubmitVo;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 问卷 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-24
 */
@ApiSupport( order = 10 )
@Api( tags = {"问卷相关接口"} )
@RestController
@RequestMapping( "/questionnaire" )
public class QuestionnaireController extends BaseController {

    // 引入问卷服务
    @Resource
    private IQuestionnaireService questionnaireService;

    @ApiOperation( "获取问卷内容" )
    @PostMapping( "/getQuestionnaire" )
    @Authority( AuthorityEnum.NOCHECK )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = QuestionDataDto.class )
    })
    public DataResult getQuestionnaire(@RequestBody GetQuestionnaireVo vo) {
        ValidationUtils.validate(vo);

        // 迭代检验回答
        for (QsResponsesVo qsResponsesVo : vo.getResponseList()) {
            ValidationUtils.validate(qsResponsesVo);
        }

        List<QuestionDataDto> questionDataDto = questionnaireService.getQuestionnaire(vo);
        return renderSuccess(questionDataDto);
    }

    @ApiOperation( "提交问卷" )
    @PostMapping( "/submit" )
    @Authority( AuthorityEnum.NOCHECK )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = SubmitDto.class )
    })
    public DataResult submit(@RequestBody QuestionnaireSubmitVo vo) {
        ValidationUtils.validate(vo);

        // 迭代检验回答
        for (QsResponsesVo qsResponsesVo : vo.getResponseList()) {
            ValidationUtils.validate(qsResponsesVo);
        }

        SubmitDto submitDto =  questionnaireService.submit(vo, getIpAddrPro());
        return renderSuccess(submitDto);
    }
}
