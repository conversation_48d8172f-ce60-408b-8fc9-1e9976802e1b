package com.awg.comm.vo;

import com.awg.comm.vo.base.ProductSynchronizationBaseVo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 商品同步表-修改VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductSynchronizationUpdateVo extends ProductSynchronizationBaseVo {
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id不能为空")
    private Integer id;
} 