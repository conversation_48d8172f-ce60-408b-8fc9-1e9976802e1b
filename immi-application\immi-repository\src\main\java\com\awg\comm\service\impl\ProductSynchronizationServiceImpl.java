package com.awg.comm.service.impl;

import com.awg.bp.entity.NewBusiness;
import com.awg.bp.service.NewBusinessService;
import com.awg.comm.dto.ProductInfoDto;
import com.awg.comm.entity.*;
import com.awg.comm.mapper.ProductSynchronizationMapper;
import com.awg.comm.service.*;
import com.awg.comm.vo.ProductSynchronizationCreateVo;
import com.awg.common.base.eo.UserLoginInfoEo;
import com.awg.common.exception.AssertUtils;
import com.awg.common.utils.FileBaseUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品同步表 服务实现类
 */
@Service
@Slf4j
public class ProductSynchronizationServiceImpl extends ServiceImpl<ProductSynchronizationMapper, ProductSynchronization> implements IProductSynchronizationService {

    @Resource
    NewIProductService  newIProductService;

    @Resource
    NewProductDataService newProductDataService;

    @Resource
    NewDistrictInfoService newDistrictInfoService;

    @Resource
    NewProductKeywordService newProductKeywordService;

    @Resource
    NewBusinessService newBusinessService;

    @Resource
    IProductService productService;




    @Override
    public ProductSynchronization getProductSynchronizationByOrgId(Long orgId) {
        ProductSynchronization productSynchronization = lambdaQuery().eq(ProductSynchronization::getOrgId, orgId).one();
        AssertUtils.isTrue(productSynchronization == null, "未查询到商品同步信息");
        productSynchronization.setConsultantQrcode(FileBaseUtil.getFileUrl(productSynchronization.getConsultantQrcode()));
        return productSynchronization;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createProductSynchronization(ProductSynchronizationCreateVo productSynchronizationCreateVo, UserLoginInfoEo userLoginInfoEo) {
        ProductSynchronization existSync = lambdaQuery()
                .eq(ProductSynchronization::getOrgId, userLoginInfoEo.getOrgId())
                .one();
        
        //修改
        if (existSync != null){
            // 修改已存在的同步信息
            BeanUtils.copyProperties(productSynchronizationCreateVo, existSync);
            existSync.setOrgId(userLoginInfoEo.getOrgId());
            existSync.setUserInfoId(userLoginInfoEo.getUserInfoId());
            existSync.setOrgType(userLoginInfoEo.getOrgType());
            existSync.setConsultantQrcode(FileBaseUtil.getRelativeUrl(productSynchronizationCreateVo.getConsultantQrcode()));
            this.baseMapper.updateById(existSync);
        }else{
            // 新增商品同步记录
            ProductSynchronization productSynchronization = new ProductSynchronization();
            BeanUtils.copyProperties(productSynchronizationCreateVo, productSynchronization);
            //同步当前基本信息
            productSynchronization.setOrgId(userLoginInfoEo.getOrgId());
            //使用平台id
            productSynchronization.setUserInfoId(1);
            productSynchronization.setOrgType(userLoginInfoEo.getOrgType());
            productSynchronization.setConsultantQrcode(FileBaseUtil.getRelativeUrl(productSynchronizationCreateVo.getConsultantQrcode()));
            this.baseMapper.insert(productSynchronization);
            
            // 新增时：同步所有平台已标记可同步的商品
            syncAllMarkedProductsToCurrentOrg(userLoginInfoEo, productSynchronization);
        }
    }



    /**
     * 同步所有平台已标记可同步的商品到当前机构
     */
    private void syncAllMarkedProductsToCurrentOrg(UserLoginInfoEo userLoginInfoEo, ProductSynchronization productSynchronization) {
        // 查询平台所有已标记可同步的商品
        List<NewProduct> syncableProducts = newIProductService.list(
                Wrappers.<NewProduct>lambdaQuery()
                        .eq(NewProduct::getOrgId, 1) // 平台商品
                        .eq(NewProduct::getCategory, 2) // 申校类商品
                        .eq(NewProduct::getIsSync, 1) // 已标记可同步
                        .eq(NewProduct::getAvailabilityStatus, 1) // 已上架
        );
        
        if (CollectionUtils.isEmpty(syncableProducts)) {
            log.info("暂无可同步的商品");
            return;
        }
        
        // 批量查询商品详情，避免在循环中重复查询
        for (NewProduct product : syncableProducts) {
            String productNo = product.getProductNo().toString();
            try {
                // 查询商品详情
                ProductInfoDto productInfoDto = productService.getProductDetail(productNo, null, null, true, null, null, true);
                if (productInfoDto != null) {
                    syncSingleProductToOrg(productNo, userLoginInfoEo.getOrgId(), productSynchronization, productInfoDto);
                } else {
                    log.warn("商品{}详情查询失败，跳过同步", productNo);
                }
            } catch (Exception e) {
                log.error("同步商品{}失败: {}", productNo, e.getMessage());
            }
        }
    }


    /**
     * 同步单个商品到指定机构（带商品详情参数，避免重复查询）
     */
    @Override
    public void syncSingleProductToOrg(String productNo, Integer orgId, ProductSynchronization productSynchronization, ProductInfoDto productInfoDto) {
        try {
            // 检查当前机构是否已手动导入过或者同步过该商品
            long count = newProductDataService.count(
                    Wrappers.<NewProductData>lambdaQuery()
                            .eq(NewProductData::getSourceNo, Long.valueOf(productNo))
                            .eq(NewProductData::getOrgId, orgId)
            );
            
            if (count > 0) {
                log.info("商品{}在机构{}已手动或同步导入，跳过同步", productNo, orgId);
                return;
            }
            
            // 未导入进行数据同步
            enableDisplayFlags(orgId);
            
            // 如果没有传入商品详情，则查询
            if (productInfoDto == null) {
                productInfoDto = productService.getProductDetail(productNo, null, null, true, null, null, true);
                AssertUtils.isNull(productInfoDto, "商品不存在");
            }
            
            // 执行同步
            productService.synchronizeProduct(productInfoDto, productNo, productSynchronization);
            
        } catch (Exception e) {
            log.error("同步商品{}到机构{}失败: {}", productNo, orgId, e.getMessage());
        }
    }

    /**
     * 开启机构的显示标志
     */
    @Override
    public void enableDisplayFlags(Integer orgId) {
        //开启地区显示
        newDistrictInfoService.update(
                Wrappers.<NewDistrictInfo>lambdaUpdate()
                        .set(NewDistrictInfo::getDisplayFlag, 1)
                        .eq(NewDistrictInfo::getOrgId, orgId)
        );

        //开启关键字显示
        newProductKeywordService.update(
                Wrappers.<NewProductKeyword>lambdaUpdate()
                        .set(NewProductKeyword::getDisplayFlag, 1)
                        .eq(NewProductKeyword::getOrgId, orgId)
        );

        //开启业务显示
        newBusinessService.update(
                Wrappers.<NewBusiness>lambdaUpdate()
                        .set(NewBusiness::getDisplayFlag, 1)
                        .eq(NewBusiness::getOrgId, orgId)
                        .eq(NewBusiness::getOwnerType, 2)
                        .eq(NewBusiness::getFromPlatformFlag, 1)
        );
    }
}
