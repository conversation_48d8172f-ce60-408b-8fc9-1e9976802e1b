package com.awg.admin.comm.controller;

import com.awg.comm.dto.EditorDto;
import com.awg.comm.entity.ProductKeyword;
import com.awg.comm.service.IProductEditorRelationService;
import com.awg.comm.service.IProductKeywordService;
import com.awg.comm.vo.EditorVo;
import com.awg.comm.vo.ProductKeywordVo;
import com.awg.comm.vo.QueryEditorAllVo;
import com.awg.common.base.controller.BaseController;
import com.awg.common.base.result.DataResult;
import com.awg.common.validator.ValidationUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@ApiSupport( order = 227 )
@Api( tags = {"商品编辑人员相关接口"} )
@RestController
@RequestMapping( "/comm/productEditor" )
public class ProductEditorRelationController extends BaseController {

    @Resource
    private IProductEditorRelationService productEditorRelationService;

    @ApiOperationSupport(order = 10)
    @ApiOperation( "编辑人员列表【全部】" )
    @PostMapping( "/listAll" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = EditorDto.class )
    })
    public DataResult listAll(@RequestBody QueryEditorAllVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productEditorRelationService.queryEditorListAll(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation( "添加" )
    @PostMapping( "/add" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult addEditor(@RequestBody EditorVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productEditorRelationService.addEditor(vo, getUserLoginInfoEo()));
    }

    @ApiOperationSupport(order = 30)
    @ApiOperation( "移除" )
    @PostMapping( "/remove" )
    @ApiResponses( value = {
            @ApiResponse( code = 200, message = "接口调用正常", response = Integer.class )
    })
    public DataResult removeEditor(@RequestBody EditorVo vo) {
        ValidationUtils.validate(vo);
        return renderSuccess(productEditorRelationService.removeEditor(vo, getUserLoginInfoEo()));
    }

}
