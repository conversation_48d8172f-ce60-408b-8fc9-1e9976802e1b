package com.awg.account.entity;

import com.awg.mybatis.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 钱包表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@EqualsAndHashCode( callSuper = true )
@TableName(value = "account_wallet")
public class Wallet extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 持有人id（member_id）
     */
    private Integer memberId;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 可用余额
     */
    private BigDecimal availableBalance;

    /**
     * 待到账金额
     */
    private BigDecimal pendingBalance;

    /**
     * 钱包编号
     */
    private Long walletNo;

}
